import { useEffect } from 'react';
import { sharedAuthController } from '@controllers/auth';
import { useProgrammaticNavigation } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { ThemeProvider } from '@cosmos/components/theme-provider';
import { sharedAnalyticsController } from '@globals/analytics';
import {
    sharedAsyncCompanySyncController,
    sharedAsyncEventsController,
} from '@globals/async-events';
import { i18n, I18nProvider } from '@globals/i18n';
import { logger } from '@globals/logger';
import { action, observer } from '@globals/mobx';
import radixTheme from '@radix-ui/themes/layout.css?url';
import type { LinksFunction, MetaFunction } from '@remix-run/node';
import {
    type ClientLoaderFunction,
    Links,
    Meta,
    Outlet,
    redirect,
    Scripts,
    useLocation,
    useMatches,
    useNavigate,
    useParams,
} from '@remix-run/react';
import { ModalRoot } from '@ui/modal-root';
import { SnackbarRoot } from '@ui/snackbar-root';
import styles from './global.css?url';

export const meta: MetaFunction = () => {
    return [
        {
            title: 'Drata Inc.',
        },
    ];
};

export const links: LinksFunction = () => {
    return [
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        {
            rel: 'preconnect',
            href: 'https://fonts.gstatic.com',
            crossOrigin: 'anonymous',
        },
        {
            rel: 'stylesheet',
            href: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap',
        },
        {
            rel: 'stylesheet',
            href: styles,
        },
        { rel: 'stylesheet', href: radixTheme },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '256x256',
            href: 'https://drata.com/images/favicon-256x256.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '48x48',
            href: 'https://drata.com/images/favicon-48x48.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '32x32',
            href: 'https://drata.com/images/favicon-32x32.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '16x16',
            href: 'https://drata.com/images/favicon-16x16.png',
        },
        {
            rel: 'icon',
            type: 'image/x-icon',
            href: 'https://drata.com/images/favicon.ico',
        },
    ];
};

export const Layout = ({
    children,
}: {
    children: React.ReactNode;
}): React.JSX.Element => {
    return (
        <html lang="en" data-testid="Layout" data-id="Dz_Soub0">
            <head>
                <meta charSet="utf-8" />
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1"
                />
                <Meta />
                <Links />
            </head>
            <body>
                {children}
                <Scripts nonce="remix-server" />
            </body>
        </html>
    );
};

// TODO: Use the routes file.
const loginRoutes = ['/auth/login', '/auditor/auth/login', '/magic-link'];

export const clientLoader: ClientLoaderFunction = action(({ request }) => {
    sharedAsyncEventsController.init();
    sharedAsyncCompanySyncController.init();

    if (
        !sharedAuthController.isUserAuthenticated &&
        !loginRoutes.some((route) => request.url.includes(route))
    ) {
        return redirect('/auth/login');
    }

    if (
        sharedAuthController.isUserAuthenticated &&
        !request.url.includes(sharedAuthController.redirectionLink)
    ) {
        console.debug({
            message: 'User is authenticated, redirecting to other page',
            requestUrl: request,
            redirectionLink: sharedAuthController.redirectionLink,
        });

        //return redirect(sharedAuthController.redirectionLink);
    }

    // TODO: We can store the url and use it after user re-authenticates.
    console.debug('User is authenticated but will not do anything.');

    // Provide default subdomain config for local development (localhost:5173)
    const isLocalhost = request.url.includes('localhost:5173');

    if (isLocalhost) {
        return {
            subdomainConfig: {
                id: 'local-dev',
                userPart: '', // Empty for localhost, will be determined by workspace logic
                authRoute: '/auth/login',
            },
        };
    }

    return null;
});

const App = observer((): React.JSX.Element => {
    const location = useLocation();
    const { pathname, hash, search } = location;
    const navigate = useNavigate();
    const params = useParams();

    // Initialize programmatic navigation for use in controllers
    useProgrammaticNavigation();

    const { isUserAuthenticated, authMode } = sharedAuthController;

    const matches = useMatches();
    const lastMatch = matches.at(-1);

    useEffect(() => {
        routeController.setMatches(matches);
        logger.info({
            message: 'Setting matches in route controller',
            additionalInfo: {
                matches,
            },
        });
    }, [matches]);

    useEffect(() => {
        routeController.setRouteContext({ params });
        logger.info({
            message: 'Setting route context in route controller',
            additionalInfo: {
                params,
            },
        });
    }, [params]);

    useEffect(() => {
        // TODO: Get the title from right place https://drata.atlassian.net/browse/ENG-67892
        const pageTitle =
            // @ts-expect-error -- this is getting follow up work
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access -- this is getting follow up work
            lastMatch?.handle?.overrides?.pageHeader?.title ?? 'pathname';

        // Track page view with Segment
        // TODO: Pass something as page category instead of Multiverse Page https://drata.atlassian.net/browse/ENG-67892
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument -- this is getting follow up work
        sharedAnalyticsController.page('Multiverse Page', pageTitle, pathname);
    }, [pathname, hash, search, lastMatch]);

    useEffect(() => {
        if (
            !isUserAuthenticated &&
            !loginRoutes.some((route) => pathname.includes(route))
        ) {
            navigate('/auth/login', { replace: true });
        }
    }, [isUserAuthenticated, navigate, pathname, authMode]);

    return (
        <I18nProvider i18n={i18n} data-id="z_LABpL5">
            <ThemeProvider data-testid="App" data-id="QkR6kaax">
                <Outlet />

                <ModalRoot />
                <SnackbarRoot />
            </ThemeProvider>
        </I18nProvider>
    );
});

export default App;
