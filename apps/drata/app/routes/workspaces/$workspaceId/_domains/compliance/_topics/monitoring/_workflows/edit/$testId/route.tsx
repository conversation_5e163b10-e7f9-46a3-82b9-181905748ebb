import { get, isEmpty } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { activeMonitoringNonV2Controller } from '@controllers/monitoring-details';
import { sharedProviderServicesController } from '@controllers/provider-services';
import { action, observer, when } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type {
    ClientLoaderFunction,
    ClientLoaderFunctionArgs,
} from '@remix-run/react';
import {
    MonitoringEditBuilderPageHeaderModel,
    MonitoringEditBuilderView,
} from '@views/monitoring-builder';

export const meta: MetaFunction = () => [{ title: 'Monitoring builder' }];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }: ClientLoaderFunctionArgs) => {
        const { testId } = params;

        sharedConnectionsController.allConfiguredConnectionsQuery.load();

        if (testId) {
            activeMonitoringNonV2Controller.loadMonitor(Number(testId));
        }

        when(
            () => !isEmpty(activeMonitoringNonV2Controller.monitorDetailsData),
            () => {
                const { monitorDetailsData } = activeMonitoringNonV2Controller;

                const provider: string = get(
                    monitorDetailsData,
                    'recipes[0].recipe.providers[0].provider',
                    '',
                );
                const resource: string = get(
                    monitorDetailsData,
                    'recipes[0].recipe.providers[0].resources[0].resource',
                    '',
                );

                if (provider === '' || resource === '') {
                    return;
                }

                sharedProviderServicesController.getProviderAttributes(
                    provider,
                    resource,
                );
            },
        );

        return {
            pageHeader: new MonitoringEditBuilderPageHeaderModel(),
        };
    },
);

const MonitoringEditBuilder = observer((): React.JSX.Element => {
    return (
        <MonitoringEditBuilderView
            data-testid="MonitoringEditBuilder"
            data-id="4oYwg9Du"
        />
    );
});

export default MonitoringEditBuilder;
