import { isNil } from 'lodash-es';
import type { ClientLoader, Tab } from '@app/types';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import {
    PoliciesBuilderBannersModel,
    PolicyBuilderHeaderModel,
    sharedPolicyBuilderModel,
} from '@models/policy-builder';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunctionArgs, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: t`Policy` }];
};
class PolicyBuilderContentNavModel {
    policyId: string;

    constructor(policyId: string) {
        this.policyId = policyId;
        makeAutoObservable(this);
    }

    get tabs(): Tab[] {
        const baseTabs: Tab[] = [
            {
                id: 'governance.policies.builder.overview',
                topicPath: `governance/policies/builder/${this.policyId}/overview`,
                label: t`Overview`,
            },
            {
                id: 'governance.policies.builder.policy',
                topicPath: `governance/policies/builder/${this.policyId}/policy`,
                label: t`Policy`,
            },
            {
                id: 'governance.policies.builder.controls',
                topicPath: `governance/policies/builder/${this.policyId}/controls`,
                label: t`Controls`,
            },
            {
                id: 'governance.policies.builder.version-history',
                topicPath: `governance/policies/builder/${this.policyId}/version-history`,
                label: t`Version history`,
            },
        ];

        const { isBambooHRProvider } = sharedPolicyBuilderModel;

        if (!isBambooHRProvider) {
            baseTabs.push({
                id: 'governance.policies.builder.workflows',
                topicPath: `governance/policies/builder/${this.policyId}/workflows`,
                label: t`Workflows`,
            });
        }

        return baseTabs;
    }
}

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): ClientLoader => {
        const { policyId } = params;

        if (isNil(policyId)) {
            throw new Error(t`Policy ID is required`);
        }

        const policyIdNumber = Number(policyId);

        sharedPolicyBuilderController.setPolicyId(policyIdNumber);

        // Load data for Policy Builder functionality (automatically loads policy details, controls, and frameworks)
        sharedPolicyBuilderController.loadPolicyWithAllData(policyIdNumber);
        sharedPolicyBuilderModel.startInitialLoadingWatcher();

        return {
            pageHeader: new PolicyBuilderHeaderModel(),
            banners: new PoliciesBuilderBannersModel()
                .policiesBuilderViewBanners,
            contentNav: new PolicyBuilderContentNavModel(policyId),
        };
    },
);

const PolicyBuilder = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="PolicyBuilder"
            data-id="policy-builder"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default PolicyBuilder;
