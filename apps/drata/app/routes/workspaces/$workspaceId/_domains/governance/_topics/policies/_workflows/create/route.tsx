import type { ClientLoader, Tab } from '@app/types';
import { sharedPersonnelGroupController } from '@controllers/personnel-group-controller';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { PoliciesAddView } from '@views/policies-add';

export const meta: MetaFunction = () => [{ title: t`Create custom policy` }];

class PoliciesCreatePageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'policies-create';
    isCentered = true;

    get title(): string {
        return t`Create policy`;
    }
}

class PoliciesCreateContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tabs(): Tab[] {
        return [];
    }
}

export const clientLoader = action((): ClientLoader => {
    sharedPersonnelGroupController.loadPersonnelGroups();

    // Reset SLA flags when entering the wizard
    sharedPolicyBuilderController.resetAddingVersionSLAs();

    return {
        pageHeader: new PoliciesCreatePageHeaderModel(),
        contentNav: new PoliciesCreateContentNavModel(),
    };
});

const CreatePolicyWorkflow = (): React.JSX.Element => {
    return (
        <PoliciesAddView
            data-testid="CreatePolicyWorkflow"
            data-id="RPUzAeUB"
        />
    );
};

export default CreatePolicyWorkflow;
