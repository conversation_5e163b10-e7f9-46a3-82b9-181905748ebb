import type { ClientLoader, Tab } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedPersonnelGroupController } from '@controllers/personnel-group-controller';
import { sharedCreatePolicyController } from '@controllers/policies';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { PoliciesAddView } from '@views/policies-add';

export const meta: MetaFunction = () => [{ title: t`Import Policy` }];

class PoliciesImportPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'policies-import';
    isCentered = true;

    get title(): string {
        return t`Import Policy`;
    }
}

class PoliciesImportContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tabs(): Tab[] {
        return [];
    }
}

export const clientLoader = action((): ClientLoader => {
    sharedConnectionsController.allConfiguredConnectionsQuery.load();
    sharedPersonnelGroupController.loadPersonnelGroups();
    sharedCreatePolicyController.setExternal(true);

    // Reset SLA flags when entering the wizard
    sharedPolicyBuilderController.resetAddingVersionSLAs();

    return {
        pageHeader: new PoliciesImportPageHeaderModel(),
        contentNav: new PoliciesImportContentNavModel(),
    };
});

const ImportPolicy = (): React.JSX.Element => {
    return (
        <PoliciesAddView
            isExternal
            data-testid="ImportPolicy"
            data-id="RPUzAeUB"
        />
    );
};

export default ImportPolicy;
