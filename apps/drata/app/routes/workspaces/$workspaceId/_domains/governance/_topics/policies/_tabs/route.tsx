import type { ClientLoader } from '@app/types';
import { sharedPoliciesController } from '@controllers/policies';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import {
    PoliciesListViewBannersModel,
    sharedPoliciesPageHeaderModel,
} from '@models/policies';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: t`Policy List` }];
};

export const clientLoader = action((): ClientLoader => {
    sharedPoliciesController.loadExternalPolicyStatus();

    return {
        pageHeader: sharedPoliciesPageHeaderModel,
        banners: new PoliciesListViewBannersModel().policiesListViewBanners,
    };
});

const PolicyList = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="PolicyList"
            data-id="policy-List"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default PolicyList;
