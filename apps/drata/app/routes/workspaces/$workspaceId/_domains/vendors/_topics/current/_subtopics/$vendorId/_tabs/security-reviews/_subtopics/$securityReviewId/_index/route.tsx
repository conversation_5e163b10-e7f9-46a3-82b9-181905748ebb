import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { observer } from '@globals/mobx';
import { createSecurityReviewEmptyStateBanner } from '@models/vendors-profile';
import { type ClientLoaderFunction, redirect } from '@remix-run/react';
import { VendorsProfileSecurityReviewView } from '@views/vendors-profile-security-review';

export const clientLoader: ClientLoaderFunction = ({ request }) => {
    const url = new URL(request.url);
    const { hasVendorAssessmentAgentWorkflow } =
        sharedVendorsVrmAgentController;
    const redirectUrl = `${url.pathname}/assessment${url.search}${url.hash}`;

    return hasVendorAssessmentAgentWorkflow
        ? redirect(redirectUrl, 302)
        : // When there's no tab redirect, it means we will show only the documents content
          {
              banners: createSecurityReviewEmptyStateBanner,
          };
};

const VendorsProfileSecurityReviews = observer((): React.JSX.Element => {
    const { hasVendorAssessmentAgentWorkflow } =
        sharedVendorsVrmAgentController;

    return hasVendorAssessmentAgentWorkflow ? (
        <div
            data-testid="VendorsProfileSecurityReviews"
            data-id="aEYzdPCp"
        ></div>
    ) : (
        <VendorsProfileSecurityReviewView data-testid="VendorsProfileSecurityReviews" />
    );
});

export default VendorsProfileSecurityReviews;
