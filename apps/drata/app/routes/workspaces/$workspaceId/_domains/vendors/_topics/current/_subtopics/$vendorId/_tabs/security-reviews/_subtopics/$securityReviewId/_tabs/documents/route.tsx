import { t } from '@globals/i18n/macro';
import { createSecurityReviewEmptyStateBanner } from '@models/vendors-profile';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { VendorsProfileSecurityReviewFiles } from '@views/vendors-profile-security-review';

export const meta: MetaFunction = () => [
    { title: t`Security Review Documents` },
];

export const clientLoader: ClientLoaderFunction = () => {
    return {
        banners: createSecurityReviewEmptyStateBanner,
    };
};

const VendorsProfileSecurityReviewDocuments = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewFiles
            data-testid="VendorsProfileSecurityReviewDocuments"
            data-id="vendors-profile-security-review-documents"
        />
    );
};

export default VendorsProfileSecurityReviewDocuments;
