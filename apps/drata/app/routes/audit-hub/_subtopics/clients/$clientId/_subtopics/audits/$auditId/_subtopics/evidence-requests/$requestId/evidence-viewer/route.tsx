import { sharedAuditHubEvidenceViewerController } from '@controllers/audit-hub-evidence-viewer';
import { action } from '@globals/mobx';
import { EvidenceViewerPageHeaderModel } from '@models/evidence-viewer-page-header';
import { type ClientLoaderFunction, redirect } from '@remix-run/react';
import { AuditHubEvidenceViewerView } from '@views/audit-hub-evidence-viewer';

export const clientLoader: ClientLoaderFunction = action(() => {
    // Redirect to evidence tab if no evidence is selected
    if (!sharedAuditHubEvidenceViewerController.evidenceDocument) {
        const pageHeaderModel = new EvidenceViewerPageHeaderModel();
        const { evidenceTabUrl } = pageHeaderModel;

        return redirect(evidenceTabUrl);
    }

    return {
        pageHeader: new EvidenceViewerPageHeaderModel(),
        tabs: [], // Hide parent tabs on evidence viewer page
    };
});

const EvidenceViewer = (): React.JSX.Element => {
    return (
        <AuditHubEvidenceViewerView
            data-testid="EvidenceViewer"
            data-id="evidence-viewer-page"
        />
    );
};

export default EvidenceViewer;
