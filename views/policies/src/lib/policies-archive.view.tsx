import { routeController } from '@controllers/route';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { sharedPoliciesActiveViewModel } from './policies-view.controller';

export const PoliciesArchiveView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const {
        archivedPolicies,
        archivedColumns,
        loadArchivedPolicies,
        isArchivedPoliciesLoading,
        archivedPoliciesTotal,
        overviewFilter,
        filters,
        tableActions,
    } = sharedPoliciesActiveViewModel;

    const handleRowClick = ({ row }: { row: { id: number } }) => {
        navigate(
            `${routeController.userPartOfUrl}/governance/policies/builder/${row.id}/overview`,
        );
    };

    return (
        <Grid gap="lg" data-testid="PoliciesArchiveView" data-id="mGhO8JRX">
            <Datatable
                key={`datatable-archived-policies-${overviewFilter || 'none'}`}
                isLoading={isArchivedPoliciesLoading}
                tableId="datatable-archived-policies"
                data-id="datatable-archived-policies"
                data={archivedPolicies}
                columns={archivedColumns}
                total={archivedPoliciesTotal}
                filterProps={filters}
                tableActions={tableActions}
                emptyStateProps={sharedPoliciesActiveViewModel.getEmptyStateProps(
                    'archived',
                )}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t({
                        message: 'Search by name...',
                        comment: 'Placeholder text for policy name search',
                    }),
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t({
                            message: 'Pin filters to page',
                            comment: 'Label for pinning filters to page',
                        }),
                        toggleUnpinnedLabel: t({
                            message: 'Move filters to dropdown',
                            comment: 'Label for moving filters to dropdown',
                        }),
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadArchivedPolicies}
                onRowClick={handleRowClick}
            />
        </Grid>
    );
});
