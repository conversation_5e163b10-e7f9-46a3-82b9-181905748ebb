import {
    getVendorQuestionnaireCategoryOptions,
    getVendorQuestionnaireStatusOptions,
    getVendorRiskListOption,
} from '@components/vendor-questionnaires';
import type { FilterProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';

export const getVendorsQuestionnairesFilters = (): FilterProps => ({
    filters: [
        {
            filterType: 'select',
            id: 'vendors-questionnaires-table-filter-status',
            label: t`Status`,
            options: getVendorQuestionnaireStatusOptions(),
        },
        {
            filterType: 'select',
            id: 'vendors-questionnaires-table-filter-category',
            label: t`Category`,
            options: getVendorQuestionnaireCategoryOptions(),
        },
        {
            filterType: 'select',
            id: 'vendors-questionnaires-table-filter-risk',
            label: t`Risk level`,
            options: getVendorRiskListOption(),
        },
    ],
});
