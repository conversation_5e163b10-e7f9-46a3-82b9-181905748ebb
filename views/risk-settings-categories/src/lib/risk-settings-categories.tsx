import { useCallback, useState } from 'react';
import { sharedRiskCategoriesController } from '@controllers/risk';
import { But<PERSON> } from '@cosmos/components/button';
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { dimension2xl } from '@cosmos/constants/tokens';
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import type { RiskCategoryResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const RiskSettingsCategoriesView = observer((): React.JSX.Element => {
    const {
        paginatedCategories,
        paginatedTotal,
        isPaginatedLoading,
        hasPaginatedError,
        loadCategoriesPaginated,
        addCategoryWithValidation,
        updateNewCategoryName,
        deleteCategoryWithConfirmation,
        newCategoryName,
        validationError,
        isCreatingCategory,
        isDeletingSpecificCategory,
    } = sharedRiskCategoriesController;

    const { hasRiskManagePermission } = sharedFeatureAccessModel;

    const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE);
    const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        updateNewCategoryName(event.target.value);
    };

    const handleAddCategory = () => {
        addCategoryWithValidation();
    };

    const handleDeleteCategory = (categoryId: string, categoryName: string) => {
        deleteCategoryWithConfirmation(categoryId, categoryName);
    };

    const handlePageChange = useCallback(
        (page: number) => {
            setCurrentPage(page);
            loadCategoriesPaginated(page, pageSize);
        },
        [pageSize, loadCategoriesPaginated],
    );

    const handlePageSizeChange = useCallback(
        (newPageSize: number) => {
            setPageSize(newPageSize);
            setCurrentPage(DEFAULT_PAGE);
            loadCategoriesPaginated(DEFAULT_PAGE, newPageSize);
        },
        [loadCategoriesPaginated],
    );

    if (isPaginatedLoading) {
        return (
            <Stack
                direction="column"
                gap="6x"
                data-id="risk-settings-categories-view"
            >
                {hasRiskManagePermission && (
                    <Grid columns="1fr auto" gap="md" align="end">
                        <Skeleton
                            barCount={1}
                            width="100%"
                            barHeight={dimension2xl}
                        />
                        <Skeleton
                            barCount={1}
                            width="120px"
                            barHeight={dimension2xl}
                        />
                    </Grid>
                )}
                <Stack direction="column" gap="md">
                    <Skeleton
                        barCount={1}
                        width="100%"
                        barHeight={dimension2xl}
                    />
                    <Skeleton
                        barCount={1}
                        width="100%"
                        barHeight={dimension2xl}
                    />
                    <Skeleton
                        barCount={1}
                        width="100%"
                        barHeight={dimension2xl}
                    />
                </Stack>
            </Stack>
        );
    }

    if (hasPaginatedError) {
        return (
            <Stack
                direction="column"
                gap="6x"
                data-id="risk-settings-categories-view"
            >
                {hasRiskManagePermission && (
                    <Grid columns="1fr auto" gap="md" align="end">
                        <TextField
                            name="new-category"
                            label={t`New category`}
                            value={newCategoryName}
                            data-id="new-category-input"
                            formId="risk-categories-form"
                            onChange={handleInputChange}
                        />
                        <Button
                            label={t`Add category`}
                            level="secondary"
                            data-id="add-category-button"
                            isLoading={isCreatingCategory}
                            a11yLoadingLabel={t`Creating category...`}
                            onClick={handleAddCategory}
                        />
                    </Grid>
                )}
                <Stack direction="column" gap="md" align="center">
                    <Text colorScheme="critical">{t`Failed to load categories`}</Text>
                    <Text size="200" colorScheme="neutral">
                        {t`Please refresh the page to try again.`}
                    </Text>
                </Stack>
            </Stack>
        );
    }

    return (
        <Stack
            direction="column"
            gap="6x"
            data-id="risk-settings-categories-view"
        >
            {hasRiskManagePermission && (
                <Grid
                    columns="1fr auto"
                    gap="md"
                    align={validationError ? 'center' : 'end'}
                >
                    <TextField
                        name="new-category"
                        label={t`New category`}
                        value={newCategoryName}
                        data-id="new-category-input"
                        formId="risk-categories-form"
                        feedback={
                            validationError
                                ? {
                                      type: 'error',
                                      message: validationError,
                                  }
                                : undefined
                        }
                        onChange={handleInputChange}
                    />
                    <Button
                        level="secondary"
                        data-id="add-category-button"
                        isLoading={isCreatingCategory}
                        a11yLoadingLabel={t`Creating category...`}
                        label={
                            isCreatingCategory
                                ? t`Creating category...`
                                : t`Add category`
                        }
                        onClick={handleAddCategory}
                    />
                </Grid>
            )}

            <Stack direction="column" gap="lg">
                <StackedList
                    data-id="risk-categories-list"
                    aria-label={t`Risk categories`}
                    data-testid="CategoriesContent"
                >
                    {paginatedCategories.map(
                        (category: RiskCategoryResponseDto) => (
                            <StackedListItem
                                key={category.id}
                                data-id="risk-category-item"
                                primaryColumn={<Text>{category.name}</Text>}
                                action={
                                    hasRiskManagePermission && (
                                        <Button
                                            isIconOnly
                                            label={t`Delete category`}
                                            level="tertiary"
                                            startIconName="Trash"
                                            colorScheme="danger"
                                            a11yLoadingLabel={t`Deleting category...`}
                                            isLoading={isDeletingSpecificCategory(
                                                String(category.id),
                                            )}
                                            onClick={() => {
                                                handleDeleteCategory(
                                                    String(category.id),
                                                    category.name,
                                                );
                                            }}
                                        />
                                    )
                                }
                            />
                        ),
                    )}
                </StackedList>

                {paginatedTotal > pageSize && (
                    <PaginationControls
                        data-id="risk-categories-pagination"
                        total={paginatedTotal}
                        pageSize={pageSize}
                        initialPage={currentPage}
                        onPageChange={handlePageChange}
                        onPageSizeChange={handlePageSizeChange}
                    />
                )}
            </Stack>
        </Stack>
    );
});
