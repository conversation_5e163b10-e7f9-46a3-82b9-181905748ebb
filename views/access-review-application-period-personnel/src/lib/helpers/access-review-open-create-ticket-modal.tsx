import {
    type CreateTicketFn,
    type CreateTicketFormData,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { CreateTicketModalView } from '@views/create-ticket-modal';

export const openAccessReviewCreateTicketModal = action(
    (
        onCreateTicket: CreateTicketFn,
        intData?: Partial<CreateTicketFormData>,
    ): void => {
        sharedCreateTicketController.initialize(onCreateTicket, intData);
        modalController.openModal({
            id: 'access-review-create-ticket-modal',
            content: () => (
                <CreateTicketModalView data-id="utilities-ticketing-for-access-review-active-period-user-create-ticket-modal" />
            ),
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: true,
        });
    },
);
