import { isEmpty, isNil, isNumber, isObject, isString } from 'lodash-es';
import type { ComponentProps } from 'react';
import type { ExternalDatatableController } from '@components/app-datatable';
import { snackbarController } from '@controllers/snackbar';
import type {
    Datatable,
    DatatableProps,
    DatatableRef,
    FilterProps,
    GlobalFilterState,
} from '@cosmos/components/datatable';
import {
    groupControllerGetAllGroupsOptions,
    personnelControllerBulkActionRemindersMutation,
    personnelControllerListPersonnelOptions,
} from '@globals/api-sdk/queries';
import type {
    GroupControllerGetAllGroupsData,
    GroupResponseDto,
    PersonnelControllerListPersonnelData,
    PersonnelDetailsResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    toJS,
} from '@globals/mobx';
// Import helpers
import { COLUMN_SIZES } from '@helpers/table';
// Import cell components
import { AcknowledgedPoliciesCell } from '../cells/acknowledged-policies-cell';
import { AiAwarenessTrainingCell } from '../cells/ai-awareness-training-cell';
import { AntivirusEdrCell } from '../cells/antivirus-edr-cell';
import { AutoUpdatesCell } from '../cells/auto-updates-cell';
import { BgCheckCell } from '../cells/bg-check-cell';
import { ComplianceStatusCell } from '../cells/compliance-status-cell';
import { DeviceComplianceCell } from '../cells/device-compliance-cell';
import { DiskEncryptedCell } from '../cells/disk-encrypted-cell';
import { DrataAgentInstalledCell } from '../cells/drata-agent-installed-cell';
import { EmploymentStatusCell } from '../cells/employment-status-cell';
import { HipaaTrainingCell } from '../cells/hipaa-training-cell';
import { HireDateCell } from '../cells/hire-date-cell';
import { IdentityMfaCell } from '../cells/identity-mfa-cell';
import { InlineEmploymentStatusCell } from '../cells/inline-employment-status-cell';
import { LockScreenCell } from '../cells/lock-screen-cell';
import { OffboardingEvidenceCell } from '../cells/offboarding-evidence-cell';
import { PasswordManagerCell } from '../cells/password-manager-cell';
import { SecurityTrainingCell } from '../cells/security-training-cell';
import { SeparationDateCell } from '../cells/separation-date-cell';
import { SyncStatusCell } from '../cells/sync-status-cell';
import { UserNameCell } from '../cells/user-name-cell';
// Import filter helper functions
import {
    generateGroupFilterOptions,
    getPersonnelTableComplianceFilter,
    getPersonnelTablePersonnelStatusFilter,
    getPersonnelTableSyncStatusFilter,
} from '../constants/personnel.constants.ts';
import { personnelBulkActionsModel } from '../models/personnel-bulk-actions.model.ts';
import type { PersonnelFilters } from './personnel-filters.types.ts';

// Extend the auto-generated type to support array bracket notation for groupIds
type ExtendedPersonnelQuery = NonNullable<
    PersonnelControllerListPersonnelData['query']
> & {
    'groupIds[]'?: number[];
    /**
     * Missing from API contract.
     */
    useExclusions?: boolean;
};

export type PersonnelControllerListPersonnelDatatableQuery = Required<
    Parameters<typeof personnelControllerListPersonnelOptions>
>[0]['query'];

class PersonnelDatatableController
    implements
        ExternalDatatableController<
            PersonnelDetailsResponseDto,
            PersonnelControllerListPersonnelDatatableQuery
        >
{
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Data fetching with ObservedQuery.
     */
    personnelQuery = new ObservedQuery(personnelControllerListPersonnelOptions);

    /**
     * Groups data fetching.
     */
    groupsQuery = new ObservedQuery(groupControllerGetAllGroupsOptions);

    /**
     * Bulk actions mutations.
     */
    bulkReminderMutation = new ObservedMutation(
        personnelControllerBulkActionRemindersMutation,
        {
            onSuccess: () => {
                // Reload the table to reflect any changes
                this.personnelQuery.invalidate();

                // Show success snackbar
                snackbarController.addSnackbar({
                    id: 'bulk-reminder-success',
                    props: {
                        title: t`Reminder emails sent successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Clear row selection and reset bulk actions model state
                personnelBulkActionsModel.selectedPersonnelIds = [];
                personnelBulkActionsModel.isAllRowsSelected = false;

                // Reset datatable row selection if ref is available
                if (this.imperativeHandleRef?.current) {
                    this.imperativeHandleRef.current.resetRowSelection();
                }
            },
            onError: () => {
                // Show error snackbar
                snackbarController.addSnackbar({
                    id: 'bulk-reminder-error',
                    props: {
                        title: t`Failed to send reminder emails`,
                        description: t`An error occurred while sending reminder emails. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    /**
     * Computed values.
     */
    get data(): PersonnelDetailsResponseDto[] {
        return this.personnelQuery.data?.data ?? [];
    }

    get total(): number {
        return this.personnelQuery.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return this.personnelQuery.isLoading || this.groupsQuery.isLoading;
    }

    get error(): Error | null {
        return this.personnelQuery.error;
    }

    get hasError(): boolean {
        return this.personnelQuery.hasError;
    }

    get groups(): GroupResponseDto[] {
        return this.groupsQuery.data?.groups ?? [];
    }

    /**
     * Table columns configuration.
     */
    get columns(): DatatableProps<PersonnelDetailsResponseDto>['columns'] {
        return [
            {
                id: 'NAME',
                header: t`Name`,
                accessorFn: (row) =>
                    (
                        row.user as
                            | PersonnelDetailsResponseDto['user']
                            | undefined
                    )?.firstName || '', // Safe access with fallback
                enableSorting: true,
                minSize: COLUMN_SIZES.XLARGE,
                cell: UserNameCell,
            },
            {
                id: 'FULL_COMPLIANCE',
                header: t`Compliance status`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: ComplianceStatusCell,
            },
            {
                id: 'EMPLOYMENT_STATUS',
                header: t`Status`,
                accessorKey: 'employmentStatus',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: this.hasPersonnelManagementPermissions()
                    ? InlineEmploymentStatusCell
                    : EmploymentStatusCell,
                meta: this.hasPersonnelManagementPermissions()
                    ? { shouldIgnoreRowClick: true }
                    : undefined,
            },
            // Conditional columns based on feature flags and view mode
            // These will be filtered based on permissions, feature flags, and isSummaryViewActive
            {
                id: 'START_DATE',
                header: t`Hire Date`,
                accessorKey: 'startDate',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: HireDateCell,
            },
            {
                id: 'SEPARATION_DATE',
                header: t`Separation Date`,
                accessorKey: 'separationDate',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: SeparationDateCell,
            },
            {
                id: 'DEVICE_COMPLIANCE',
                header: t`Device Compliance`,
                accessorKey: 'devices',
                enableSorting: false,
                minSize: COLUMN_SIZES.LARGE,
                cell: DeviceComplianceCell,
            },
            {
                id: 'ACCEPTED_POLICIES',
                header: t`Acknowledged Policies`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: AcknowledgedPoliciesCell,
            },
            {
                id: 'IDENTITY_MFA',
                header: t`Identity MFA`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: IdentityMfaCell,
            },
            {
                id: 'BG_CHECK',
                header: t`BG Check`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: BgCheckCell,
            },
            {
                id: 'SECURITY_TRAINING',
                header: t`Security Training`,
                accessorKey: 'securityTrainingComplianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: SecurityTrainingCell,
            },
            {
                id: 'OFFBOARDING',
                header: t`Offboarding Evidence`,
                accessorKey: 'complianceChecks',
                enableSorting: false,
                minSize: COLUMN_SIZES.LARGE,
                cell: OffboardingEvidenceCell,
            },
            {
                id: 'SYNC_STATUS',
                header: t`Sync Status`,
                accessorFn: (row) =>
                    (
                        row.user as
                            | PersonnelDetailsResponseDto['user']
                            | undefined
                    )?.identities ?? [],
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: SyncStatusCell,
            },
            {
                id: 'AGENT_INSTALLED',
                header: t`Drata Agent Installed`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: DrataAgentInstalledCell,
            },
            {
                id: 'HIPAA_TRAINING',
                header: t`HIPAA Training`,
                accessorKey: 'hipaaTrainingComplianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: HipaaTrainingCell,
            },
            {
                id: 'NIST_AI_TRAINING',
                header: t`AI Awareness Training`,
                accessorKey: 'nistAiTrainingComplianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: AiAwarenessTrainingCell,
            },
            {
                id: 'PASSWORD_MANAGER',
                header: t`Password Manager`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: PasswordManagerCell,
            },
            {
                id: 'AUTO_UPDATES',
                header: t`Auto Updates`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: AutoUpdatesCell,
            },
            {
                id: 'HDD_ENCRYPTION',
                header: t`Disk Encrypted`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: DiskEncryptedCell,
            },
            {
                id: 'ANTIVIRUS',
                header: t`Antivirus/EDR`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: AntivirusEdrCell,
            },
            {
                id: 'LOCK_SCREEN',
                header: t`Lock Screen`,
                accessorKey: 'complianceChecks',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                cell: LockScreenCell,
            },
        ];
    }

    /**
     * Base filter configuration - will be enhanced with dynamic options.
     */
    get baseFilters(): FilterProps {
        return {
            clearAllButtonLabel: t`Reset`,
            triggerLabel: t`Filters`,
            filters: [
                {
                    filterType: 'combobox',
                    id: 'employmentStatus',
                    label: t`Employment Status`,
                    options: getPersonnelTablePersonnelStatusFilter(),
                    isMultiSelect: true,
                    defaultSelectedOptions: [],
                },
                {
                    filterType: 'combobox',
                    id: 'compliance',
                    label: t`Compliance`,
                    options: getPersonnelTableComplianceFilter(),
                    isMultiSelect: false, // Single select for compliance
                    defaultSelectedOptions: [],
                },
                {
                    filterType: 'combobox',
                    id: 'syncStatus',
                    label: t`Sync Status`,
                    options: getPersonnelTableSyncStatusFilter(),
                    isMultiSelect: false, // Single select for sync status
                    defaultSelectedOptions: [],
                },
                {
                    filterType: 'combobox',
                    id: 'groupIds',
                    label: t`Groups`,
                    options: generateGroupFilterOptions(this.groups),
                    isMultiSelect: true,
                    defaultSelectedOptions: [],
                },
            ],
        };
    }

    /**
     * Dynamic filter configuration with group options populated from API data.
     */
    get filterProps(): FilterProps {
        const filters = { ...this.baseFilters };

        // Update the groups filter with dynamic options
        const groupsFilterIndex = filters.filters.findIndex(
            (f) => f.id === 'groupIds',
        );

        if (groupsFilterIndex !== -1) {
            const groupsFilter = filters.filters[groupsFilterIndex];

            if (groupsFilter.filterType === 'combobox') {
                filters.filters[groupsFilterIndex] = {
                    ...groupsFilter,
                    options: generateGroupFilterOptions(this.groups),
                };
            }
        }

        return filters;
    }

    /**
     * Filter view mode configuration to pin filters by default.
     */
    get filterViewModeProps(): DatatableProps<PersonnelDetailsResponseDto>['filterViewModeProps'] {
        return {
            viewMode: 'toggleable',
            props: {
                initialSelectedOption: 'pinned',
                togglePinnedLabel: t`Pin filters to page`,
                toggleUnpinnedLabel: t`Move filters to dropdown`,
                selectedOption: 'pinned',
            },
        };
    }

    /**
     * Empty state configuration for different scenarios.
     */
    get emptyStateProps(): DatatableProps<PersonnelDetailsResponseDto>['emptyStateProps'] {
        // If we have an error, show error state
        if (this.hasError) {
            return {
                title: t`Unable to load personnel`,
                description: t`There was an error loading the personnel data. Please try again.`,
                illustrationName: 'Warning',
            };
        }

        // Default no results state
        return {
            title: t`No personnel found`,
            description: t`There are no personnel that match the current criteria.`,
            illustrationName: 'Personnel',
        };
    }

    /**
     * Table actions based on user permissions.
     */
    get tableActions(): ComponentProps<typeof Datatable>['tableActions'] {
        const { isDownloadControlEnabled } = sharedFeatureAccessModel;

        // Check permissions for different actions
        const canManagePersonnel = this.hasPersonnelManagementPermissions();
        const canDownloadPersonnelData =
            isDownloadControlEnabled &&
            (sharedCurrentUserController.hasUserPermission('Company', 'READ') ||
                canManagePersonnel);

        const actions: ComponentProps<typeof Datatable>['tableActions'] = [];

        // Settings dropdown with permission-based items
        const settingsItems: {
            id: string;
            label: string;
            disabled?: boolean;
        }[] = [];

        if (this.canAddFormerPersonnel()) {
            settingsItems.push({
                id: 'add-former-personnel',
                label: t`Add former personnel`,
            });
        }

        if (canDownloadPersonnelData) {
            settingsItems.push({
                id: 'download',
                label: t`Download`,
            });
        }

        // Always show view toggle (no permissions required for viewing)
        settingsItems.push({
            id: 'toggle-view',
            label: t`Toggle summary/detailed view`,
        });

        // Only show settings dropdown if there are items
        if (!isEmpty(settingsItems)) {
            actions.push({
                actionType: 'dropdown',
                id: 'settings',
                typeProps: {
                    label: t`Settings`,
                    isIconOnly: true,
                    side: 'left',
                    align: 'start',
                    colorScheme: 'neutral',
                    level: 'tertiary',
                    startIconName: 'Settings',
                    items: settingsItems,
                },
            });
        }

        return actions;
    }

    get getRowId(): DatatableProps<PersonnelDetailsResponseDto>['getRowId'] {
        return (row) => String(row.id);
    }

    /**
     * Row selection is enabled for bulk actions.
     */
    get isRowSelectionEnabled(): DatatableProps<PersonnelDetailsResponseDto>['isRowSelectionEnabled'] {
        return true;
    }

    /**
     * Bulk action dropdown items from the bulk actions model.
     */
    get bulkActionDropdownItems(): DatatableProps<PersonnelDetailsResponseDto>['bulkActionDropdownItems'] {
        return personnelBulkActionsModel.bulkActionDropdownItems;
    }

    /**
     * Handle row selection changes for bulk actions.
     */
    get onRowSelection(): DatatableProps<PersonnelDetailsResponseDto>['onRowSelection'] {
        return personnelBulkActionsModel.onRowSelection;
    }

    /**
     * Imperative handle ref for the datatable.
     */
    imperativeHandleRef?: React.RefObject<DatatableRef>;

    /**
     * Table ID for the datatable.
     */
    get tableId(): string {
        return 'personnel-datatable';
    }

    /**
     * Actions.
     */
    load(query?: PersonnelControllerListPersonnelDatatableQuery): void {
        const defaultQuery: ExtendedPersonnelQuery = {
            page: 1,
            limit: 10,
            sort: 'NAME',
            sortDir: 'ASC',
            // Missing from API contract
            useExclusions: true,
        };

        let finalQuery = { ...defaultQuery, ...query };

        // Apply custom filter transformations if filters are present in the query
        if (query) {
            finalQuery = this.applyCustomFilterTransformations(finalQuery);
        }

        this.personnelQuery.load({
            query: finalQuery,
        });
    }

    loadGroups(): void {
        const groupsParams: GroupControllerGetAllGroupsData = {
            url: '/groups/all',
            query: {
                includeIds: null, // Load all groups
                limit: 50, // Limit to 50 groups
                page: 1,
            },
        };

        this.groupsQuery.load(groupsParams);
    }

    /**
     * Process filters from the datatable into a format suitable for the API.
     * TODO Clean up these filters, this is reliant on the legacy API.
     */
    private processDataTableFilters(
        datatableFilters: GlobalFilterState['filters'],
    ): PersonnelFilters {
        const filters: PersonnelFilters = {};

        // Process employment status filter
        const employmentStatusFilter = datatableFilters.employmentStatus;

        if (employmentStatusFilter.value) {
            const filterValue = employmentStatusFilter.value;
            let extractedValues: string[] = [];

            // Handle different value formats that might come from the DataTable
            if (Array.isArray(filterValue)) {
                // Handle combobox multi-select (array of objects or strings)
                extractedValues = filterValue
                    .map((item) => {
                        if (isObject(item) && 'value' in item) {
                            return item.value as string;
                        }

                        return item as string;
                    })
                    .filter(Boolean);
            } else if (isString(filterValue) && filterValue.trim() !== '') {
                // Handle single string value
                extractedValues = [filterValue];
            } else if (isObject(filterValue) && 'value' in filterValue) {
                // Handle single object with value property
                extractedValues = [filterValue.value as string];
            }

            // Filter out empty values and set all employment statuses (including special ones)
            const validStatuses = extractedValues.filter(
                (status) => status !== '',
            );

            if (!isEmpty(validStatuses)) {
                filters.employmentStatuses = validStatuses;
            }
        }

        // Process compliance filter
        const complianceFilter = datatableFilters.compliance;

        if (complianceFilter.value && complianceFilter.value !== '') {
            let complianceValue: string | undefined;

            // Handle different value formats that might come from combobox filters
            if (isString(complianceFilter.value)) {
                complianceValue = complianceFilter.value;
            } else if (
                isObject(complianceFilter.value) &&
                'value' in complianceFilter.value
            ) {
                // Handle single object with value property (combobox single select)
                complianceValue = complianceFilter.value.value as string;
            } else if (
                Array.isArray(complianceFilter.value) &&
                !isEmpty(complianceFilter.value)
            ) {
                // Handle array format (shouldn't happen for single select, but just in case)
                const firstItem = complianceFilter.value[0];

                if (isString(firstItem)) {
                    complianceValue = firstItem;
                } else if (isObject(firstItem) && 'value' in firstItem) {
                    complianceValue = firstItem.value as string;
                }
                // If invalid format, complianceValue remains undefined and we skip processing
            }
            // If invalid format, complianceValue remains undefined and we skip processing

            // Map compliance filter values to API parameters only if we have a valid value
            if (complianceValue) {
                switch (complianceValue) {
                    case 'fullCompliant': {
                        filters.fullCompliance = true;
                        break;
                    }
                    case 'notFullCompliant': {
                        filters.fullCompliance = false;
                        break;
                    }
                    case 'policiesNonCompliant': {
                        filters.acceptedPoliciesCompliance = false;
                        break;
                    }
                    case 'identityMfaNonCompliant': {
                        filters.identityMfaCompliance = false;
                        break;
                    }
                    case 'bgCheckNonCompliant': {
                        filters.bgCheckCompliance = false;
                        break;
                    }
                    case 'securityTrainingNonCompliant': {
                        filters.securityTrainingCompliance = false;
                        break;
                    }
                    case 'hipaaTrainingNonCompliant': {
                        filters.hipaaTrainingCompliance = false;
                        break;
                    }
                    case 'aiTrainingNonCompliant': {
                        filters.nistaiTrainingCompliance = false;
                        break;
                    }
                    case 'deviceNonCompliant': {
                        filters.deviceCompliance = false;
                        break;
                    }
                    case 'agentNotInstalled': {
                        filters.agentInstalledCompliance = false;
                        break;
                    }
                    case 'passwordManagerNonCompliant': {
                        filters.passwordManagerCompliance = false;
                        break;
                    }
                    case 'autoUpdatesNonCompliant': {
                        filters.autoUpdatesCompliance = false;
                        break;
                    }
                    case 'diskEncryptionNonCompliant': {
                        filters.hdEncryptionCompliance = false;
                        break;
                    }
                    case 'antivirusNonCompliant': {
                        filters.antivirusCompliance = false;
                        break;
                    }
                    case 'lockScreenNonCompliant': {
                        filters.lockScreenCompliance = false;
                        break;
                    }
                    case 'offboardingEvidenceMissing': {
                        filters.offboardingEvidence = false;
                        break;
                    }
                }
            }
        }

        // Process sync status filter
        const syncStatusFilter = datatableFilters.syncStatus;

        if (syncStatusFilter.value && syncStatusFilter.value !== '') {
            let syncValue: string | undefined;

            // Handle different value formats that might come from combobox filters
            if (isString(syncStatusFilter.value)) {
                syncValue = syncStatusFilter.value;
            } else if (
                isObject(syncStatusFilter.value) &&
                'value' in syncStatusFilter.value
            ) {
                // Handle single object with value property (combobox single select)
                syncValue = syncStatusFilter.value.value as string;
            } else if (
                Array.isArray(syncStatusFilter.value) &&
                !isEmpty(syncStatusFilter.value)
            ) {
                // Handle array format (shouldn't happen for single select, but just in case)
                const firstItem = syncStatusFilter.value[0];

                if (isString(firstItem)) {
                    syncValue = firstItem;
                } else if (isObject(firstItem) && 'value' in firstItem) {
                    syncValue = firstItem.value as string;
                }
                // If invalid format, syncValue remains undefined and we skip processing
            }
            // If invalid format, syncValue remains undefined and we skip processing

            // Validate and set the sync status filter only if we have a valid value
            if (
                syncValue &&
                (syncValue === 'NOT_FOUND_IN_HRIS_IDP' ||
                    syncValue === 'FOUND_IN_HRIS_IDP')
            ) {
                filters.notFoundInHRISIDP = syncValue;
            }
        }

        // Process group IDs filter
        const groupIdsFilter = datatableFilters.groupIds;

        if (groupIdsFilter.value) {
            const filterValue = groupIdsFilter.value;
            let extractedValues: number[] = [];

            // Handle different value formats that might come from the DataTable
            if (Array.isArray(filterValue)) {
                // Handle combobox multi-select (array of objects or strings)
                extractedValues = filterValue
                    .map((item) => {
                        if (isObject(item) && 'value' in item) {
                            const { value } = item;

                            // Convert string IDs to numbers (group IDs come as strings from UI)
                            if (isNumber(value)) {
                                return value;
                            }
                            if (isString(value) && /^\d+$/.test(value)) {
                                return Number(value);
                            }

                            return NaN;
                        }
                        // Handle direct values
                        if (isNumber(item)) {
                            return item;
                        }
                        if (isString(item) && /^\d+$/.test(item)) {
                            return Number(item);
                        }

                        return NaN;
                    })
                    .filter((id) => !isNaN(id) && id > 0); // Ensure positive numbers only
            } else if (isString(filterValue) && /^\d+$/.test(filterValue)) {
                // Handle single string value
                const numericValue = Number(filterValue);

                if (numericValue > 0) {
                    extractedValues = [numericValue];
                }
            } else if (isNumber(filterValue) && filterValue > 0) {
                // Handle single numeric value
                extractedValues = [filterValue];
            } else if (isObject(filterValue) && 'value' in filterValue) {
                // Handle single object with value property
                const { value } = filterValue;

                if (isNumber(value) && value > 0) {
                    extractedValues = [value];
                } else if (isString(value) && /^\d+$/.test(value)) {
                    const numericValue = Number(value);

                    if (numericValue > 0) {
                        extractedValues = [numericValue];
                    }
                }
            }

            if (!isEmpty(extractedValues)) {
                filters.groupIds = extractedValues;
            }
        }

        return filters;
    }

    /**
     * Apply custom filter transformations to the query.
     * This method extracts filter data from the standard query parameters,
     * applies personnel-specific transformations, and returns the enhanced query.
     */
    private applyCustomFilterTransformations(
        query: ExtendedPersonnelQuery,
    ): ExtendedPersonnelQuery {
        // Access filter properties that DataTable may add to the query
        const queryWithFilters = query as Record<string, unknown>;

        // Create a mock GlobalFilterState from the query parameters
        // This allows us to reuse the existing processDataTableFilters logic
        const mockFilters: GlobalFilterState['filters'] = {
            employmentStatus: {
                filterType: 'combobox',
                value: queryWithFilters.employmentStatus as GlobalFilterState['filters'][string]['value'],
            },
            compliance: {
                filterType: 'combobox',
                value: queryWithFilters.compliance as GlobalFilterState['filters'][string]['value'],
            },
            syncStatus: {
                filterType: 'combobox',
                value: queryWithFilters.syncStatus as GlobalFilterState['filters'][string]['value'],
            },
            groupIds: {
                filterType: 'combobox',
                value: queryWithFilters.groupIds as GlobalFilterState['filters'][string]['value'],
            },
        };

        // Process the filters using our existing custom logic
        const processedFilters = this.processDataTableFilters(mockFilters);

        // Apply the processed filters to the query using existing logic
        this.applyFiltersToQuery(query, processedFilters);

        // Remove employmentStatus from query to ensure it's never sent to the API
        // We only want to send employmentStatuses[] (plural)
        if ('employmentStatus' in query) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars -- destructuring to remove employmentStatus from query
            const { employmentStatus, ...cleanQuery } = query as Record<
                string,
                unknown
            >;

            // Return a new query object without employmentStatus
            return cleanQuery as ExtendedPersonnelQuery;
        }

        return query;
    }

    /**
     * Private helper methods.
     */
    private applyFiltersToQuery(
        query: ExtendedPersonnelQuery,
        filters: PersonnelFilters,
    ): void {
        // Always set useExclusions for personnel table
        query.useExclusions = true;

        // Handle employment status filters (all values sent as array)
        if (
            filters.employmentStatuses &&
            !isEmpty(filters.employmentStatuses)
        ) {
            query['employmentStatuses[]'] =
                filters.employmentStatuses as NonNullable<
                    PersonnelControllerListPersonnelData['query']
                >['employmentStatuses[]'];
        }

        // Handle compliance filters
        if (!isNil(filters.fullCompliance)) {
            query.fullCompliance = filters.fullCompliance;
        }
        if (!isNil(filters.acceptedPoliciesCompliance)) {
            query.acceptedPoliciesCompliance =
                filters.acceptedPoliciesCompliance;
        }
        if (!isNil(filters.identityMfaCompliance)) {
            query.identityMfaCompliance = filters.identityMfaCompliance;
        }
        if (!isNil(filters.bgCheckCompliance)) {
            query.bgCheckCompliance = filters.bgCheckCompliance;
        }
        if (!isNil(filters.agentInstalledCompliance)) {
            query.agentInstalledCompliance = filters.agentInstalledCompliance;
        }
        if (!isNil(filters.passwordManagerCompliance)) {
            query.passwordManagerCompliance = filters.passwordManagerCompliance;
        }
        if (!isNil(filters.autoUpdatesCompliance)) {
            query.autoUpdatesCompliance = filters.autoUpdatesCompliance;
        }
        if (!isNil(filters.hdEncryptionCompliance)) {
            query.hdEncryptionCompliance = filters.hdEncryptionCompliance;
        }
        if (!isNil(filters.antivirusCompliance)) {
            query.antivirusCompliance = filters.antivirusCompliance;
        }
        if (!isNil(filters.lockScreenCompliance)) {
            query.lockScreenCompliance = filters.lockScreenCompliance;
        }
        if (!isNil(filters.securityTrainingCompliance)) {
            query.securityTrainingCompliance =
                filters.securityTrainingCompliance;
        }
        if (!isNil(filters.hipaaTrainingCompliance)) {
            query.hipaaTrainingCompliance = filters.hipaaTrainingCompliance;
        }
        if (!isNil(filters.nistaiTrainingCompliance)) {
            query.nistaiTrainingCompliance = filters.nistaiTrainingCompliance;
        }
        if (!isNil(filters.deviceCompliance)) {
            query.deviceCompliance = filters.deviceCompliance;
        }
        if (!isNil(filters.offboardingEvidence)) {
            query.offboardingEvidence = filters.offboardingEvidence;
        }

        // Handle group filters
        if (filters.groupIds && !isEmpty(filters.groupIds)) {
            query['groupIds[]'] = toJS(filters.groupIds);
        }

        // Handle sync status filters
        if (filters.notFoundInHRISIDP) {
            query.notFoundInHRISIDP = filters.notFoundInHRISIDP;
        }
    }

    /**
     * Permission helper methods.
     */
    private hasPersonnelManagementPermissions(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'Company',
            'MANAGE',
        );
    }

    private canAddFormerPersonnel(): boolean {
        return this.hasPersonnelManagementPermissions();
    }
}

export const sharedPersonnelController = new PersonnelDatatableController();
