import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';

interface HireDateCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const HireDateCell = ({ row }: HireDateCellProps): JSX.Element => {
    const { startDate } = row.original;

    if (!startDate) {
        return <EmptyValue label="Start date of the hire is not available." />;
    }

    return (
        <DateTime
            date={startDate}
            format="table"
            data-id="HireDateCell"
            data-testid="HireDateCell"
        />
    );
};
