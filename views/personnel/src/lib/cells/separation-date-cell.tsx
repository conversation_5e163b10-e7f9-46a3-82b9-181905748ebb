import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';

interface SeparationDateCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const SeparationDateCell = ({
    row,
}: SeparationDateCellProps): React.JSX.Element | string => {
    const { separationDate } = row.original;

    if (!separationDate) {
        return (
            <EmptyValue label="Separation date of the hire is not available." />
        );
    }

    return (
        <DateTime
            date={separationDate}
            format="table"
            data-id="SeparationDateCell"
            data-testid="SeparationDateCell"
        />
    );
};
