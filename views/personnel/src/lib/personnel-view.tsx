import { useEffect, useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedPersonnelController } from '@controllers/personnel';
import type { DatatableRef } from '@cosmos/components/datatable';
import { observer } from '@globals/mobx';
import { personnelBulkActionsModel } from './models/personnel-bulk-actions.model';

export const PersonnelView = observer((): React.JSX.Element => {
    const datatableRef = useRef<DatatableRef>(null);

    // Set the ref on both the model and controller after component mounts
    useEffect(() => {
        personnelBulkActionsModel.setDatatableRef(datatableRef);
        sharedPersonnelController.imperativeHandleRef = datatableRef;
    }, []);

    const { getSelectAllButtonText } = personnelBulkActionsModel;

    return (
        <AppDatatable
            imperativeHandleRef={datatableRef}
            data-id="personnel-datatable"
            controller={sharedPersonnelController}
            tableId="personnel-table"
            getSelectAllButtonText={getSelectAllButtonText}
        />
    );
});
