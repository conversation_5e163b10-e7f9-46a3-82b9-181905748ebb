import { isEmpty } from 'lodash-es';
import type { RefObject } from 'react';
import { sharedPersonnelController } from '@controllers/personnel';
import type {
    DatatableProps,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction, toJS } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

export class PersonnelBulkActionsModel {
    selectedPersonnelIds: string[] = [];
    isAllRowsSelected = false;
    datatableRef: RefObject<DatatableRef> | null = null;
    constructor() {
        makeAutoObservable(this, {
            // Exclude the React ref from MobX observation
            datatableRef: false,
        });
    }

    /**
     * Set the datatable ref after component mounts.
     */
    setDatatableRef(ref: RefObject<DatatableRef>): void {
        this.datatableRef = ref;
    }

    /**
     * Bulk actions dropdown configuration.
     */
    get bulkActionDropdownItems(): DatatableProps<PersonnelDetailsResponseDto>['bulkActionDropdownItems'] {
        return [
            {
                id: 'send-reminder',
                actionType: 'tooltipButton',
                typeProps: {
                    button: {
                        label: t`Send reminder email`,
                        level: 'tertiary',
                        onClick: () => {
                            runInAction(() => {
                                this.sendBulkReminder();
                            });
                        },
                    },
                    tooltip: {
                        text: t`Send reminder emails to all selected users.`,
                        isInteractive: true,
                    },
                },
            },
        ];
    }

    /**
     * Generate select all button text based on selection state.
     */
    getSelectAllButtonText = ({
        rowCount,
    }: {
        rowCount: number | undefined;
    }): string => {
        if (rowCount === undefined) {
            return t`Select all`;
        }

        return t`Select all ${rowCount}`;
    };

    /**
     * Handle row selection changes.
     */
    onRowSelection = (selection: DatatableRowSelectionState): void => {
        runInAction(() => {
            this.isAllRowsSelected = selection.isAllRowsSelected;
            this.selectedPersonnelIds = Object.keys(selection.selectedRows);
        });
    };

    /**
     * Send bulk reminder emails to selected personnel.
     */
    private sendBulkReminder(): void {
        if (isEmpty(this.selectedPersonnelIds) && !this.isAllRowsSelected) {
            return;
        }

        let count;

        if (this.isAllRowsSelected) {
            count = sharedPersonnelController.total;
        } else {
            count = this.selectedPersonnelIds.length;
        }

        openConfirmationModal({
            title: t`Send Reminder Emails`,
            body: t`You are about to send ${count} emails, are you sure?`,
            confirmText: t`Send Emails`,
            cancelText: t`Cancel`,
            type: 'primary',
            size: 'md',
            onConfirm: () => {
                runInAction(() => {
                    sharedPersonnelController.bulkReminderMutation.mutate({
                        body: {
                            targetIds: toJS(this.selectedPersonnelIds),
                            selectAll: this.isAllRowsSelected,
                        },
                    });
                    closeConfirmationModal();
                });
            },
            onCancel: closeConfirmationModal,
        });
    }

    /**
     * Success callback called after mutation completes successfully.
     */
    onSuccess = (): void => {
        // Clear the local state first
        this.selectedPersonnelIds = [];
        this.isAllRowsSelected = false;

        this.datatableRef?.current?.resetRowSelection();
    };
}

export const personnelBulkActionsModel = new PersonnelBulkActionsModel();
