import { isEmpty, isNil, isObject } from 'lodash-es';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { AttributeRequestDto } from '@globals/api-sdk/types';
import type { Attribute } from '../types/form-value.type';
import { iterateConditionSets } from './form-values-reader.helper';

const convertValue = (value: string | ListBoxItemData): string | boolean => {
    if (isObject(value) && 'value' in value) {
        if (value.value === 'true' || value.value === 'false') {
            return value.value === 'true';
        }

        return String(value.value);
    }

    return value as string;
};

export const buildAttributes = (
    attributes: Attribute[],
    index: number,
): AttributeRequestDto[] => {
    let attributeAllAny: AttributeRequestDto = {};
    const attributesDto: AttributeRequestDto[] = [];

    if (
        !isEmpty(attributes[index].subFilteringCriteriaSets) &&
        !isNil(attributes[index].subFilteringCriteriaSets)
    ) {
        attributeAllAny = iterateConditionSets(
            attributes[index].subFilteringCriteriaSets,
        );
    }

    attributes.forEach((attribute) => {
        if (isEmpty(attribute.subFilteringCriteriaSets)) {
            attributesDto.push({
                fact: attribute.attribute?.value as string,
                operator: attribute.operator?.value as string,
                value: convertValue(attribute.value as string),
            } as AttributeRequestDto);
        } else {
            attributesDto.push(attributeAllAny);
        }
    });

    return attributesDto;
};
