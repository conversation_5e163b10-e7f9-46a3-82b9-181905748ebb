import type { ListBoxItemData } from '@cosmos/components/list-box';

export interface Attribute {
    attribute?: ListBoxItemData;
    operator?: ListBoxItemData;
    value?: string | ListBoxItemData;
    subFilteringCriteriaSets?: ConditionSet[];
}

export interface ConditionSet {
    operatorLogical?: 'AND' | 'OR';
    attributes: Attribute[];
}

export interface ConditionGroup {
    service?: ListBoxItemData;
    resource?: ListBoxItemData;
    hasFilteringCriteria?: 'true' | 'false';
    criteria: string;
    count: number;
    conditionSets: ConditionSet[];
    filteringCriteriaSets: ConditionSet[];
}
