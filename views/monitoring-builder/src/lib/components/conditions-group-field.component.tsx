import { useFieldArray } from 'react-hook-form';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { AppButton } from '@ui/app-button';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import { CONDITION_GROUP_DEFAULT_VALUE } from '../constants/condition-group-default-value.constant';
import { MAX_CONDITIONS_GROUP } from '../constants/max-conditions.constant';
import { openMaxConditionsGroupLimitModal } from '../helpers/open-max-conditions-modal';
import { FilteringCriteriaField } from './filtering-criteria-field.component';
import { ServiceResourceSelect } from './service-resource-select.component';

export const ConditionsGroupField = ({
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { fields, append, remove } = useFieldArray({ name });

    const handleAddConditionGroup = (): void => {
        if (fields.length >= MAX_CONDITIONS_GROUP) {
            openMaxConditionsGroupLimitModal();

            return;
        }

        append([CONDITION_GROUP_DEFAULT_VALUE]);
    };

    return (
        <Stack
            direction="column"
            gap="lg"
            data-testid="ConditionsGroupField"
            data-id="HrSROruU"
        >
            <Stack direction="column" gap="md">
                {fields.map((field, index) => (
                    <Box
                        key={field.id}
                        data-id="8H7uukm5"
                        borderColor="neutralBorderFaded"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadiusLg"
                        p="3xl"
                    >
                        <Stack direction="column" gap="lg">
                            <Stack
                                direction="row"
                                justify="between"
                                align="center"
                            >
                                <Text type="body" size="200">
                                    {t`Condition Group`}
                                </Text>

                                {index >= 1 && (
                                    <AppButton
                                        startIconName="Trash"
                                        label={t`Delete condition group`}
                                        colorScheme="danger"
                                        level="tertiary"
                                        size="sm"
                                        onClick={() => {
                                            remove(index);
                                        }}
                                    />
                                )}
                            </Stack>

                            <Stack direction="row" gap="md">
                                <ServiceResourceSelect
                                    basePath={name}
                                    index={index}
                                    formId={formId}
                                    data-id="service-resource-select"
                                />
                            </Stack>

                            <UniversalFormField
                                name={`${name}[${index}].conditionSets`}
                                formId={formId}
                                data-id="condition-sets"
                            />

                            <FilteringCriteriaField
                                name={`${name}[${index}]`}
                                formId={formId}
                                data-id="filtering-criteria-field"
                            />
                        </Stack>
                    </Box>
                ))}
            </Stack>

            <Box gap="md">
                <AppButton
                    startIconName="Plus"
                    label={t`Add condition group`}
                    level="secondary"
                    size="sm"
                    onClick={handleAddConditionGroup}
                />
            </Box>
        </Stack>
    );
};
