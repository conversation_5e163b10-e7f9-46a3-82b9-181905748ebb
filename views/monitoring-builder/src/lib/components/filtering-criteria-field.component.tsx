import { isEmpty } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { UniversalFormField, useUniversalFieldController } from '@ui/forms';
import { openMaxConditionsPerGroupLimitModal } from '../helpers/open-max-conditions-modal';
import { useAttributeCounter } from '../hooks/use-attribute-counter.hook';

interface FilteringCriteriaFieldProps {
    name: string;
    formId: string;
    'data-id': string;
}

export const FilteringCriteriaField = ({
    name,
    formId,
    'data-id': dataId,
}: FilteringCriteriaFieldProps): React.JSX.Element => {
    const [criteriaField] = useUniversalFieldController<'radioGroup'>(
        `${name}.criteria`,
    );
    const [hasFilteringCriteriaField] =
        useUniversalFieldController<'radioGroup'>(
            `${name}.hasFilteringCriteria`,
        );
    const [filteringCriteriaSetsField] = useUniversalFieldController(
        `${name}.filteringCriteriaSets`,
    );

    const { incrementCount, decrementCount, isAtLimit } =
        useAttributeCounter(name);

    const handleAddFilteringCriteria = () => {
        if (isAtLimit) {
            openMaxConditionsPerGroupLimitModal();

            return;
        }

        hasFilteringCriteriaField.setValue('true');
        criteriaField.setValue('exclusion');

        // Initialize filteringCriteriaSets if it's empty or undefined
        if (
            !filteringCriteriaSetsField.value ||
            isEmpty(filteringCriteriaSetsField.value)
        ) {
            filteringCriteriaSetsField.setValue([
                {
                    operatorLogical: 'AND',
                    attributes: [
                        {
                            attribute: '',
                            operator: '',
                            value: '',
                        },
                    ],
                },
            ] as never[]); // TODO: Type the array of objects correctly so it works without 'as never[]'
        }

        incrementCount();
    };

    const handleRemoveFilteringCriteria = () => {
        hasFilteringCriteriaField.setValue('false');
        criteriaField.setValue('');
        filteringCriteriaSetsField.setValue([
            {
                operatorLogical: 'AND',
                attributes: [
                    {
                        attribute: '',
                        operator: '',
                        value: '',
                    },
                ],
            },
        ] as never[]); // TODO: Type the array of objects correctly so it works without 'as never[]'

        decrementCount();
    };

    if (criteriaField.value && hasFilteringCriteriaField.value === 'true') {
        return (
            <Stack direction="column" gap="lg">
                <Stack direction="row" justify="between" align="center">
                    <Text type="body" size="200">
                        {t`Filtering criteria`}
                    </Text>

                    <Button
                        isIconOnly
                        startIconName="Trash"
                        label={t`Delete filtering criteria`}
                        colorScheme="danger"
                        level="tertiary"
                        onClick={handleRemoveFilteringCriteria}
                    />
                </Stack>

                <UniversalFormField
                    name={`${name}.criteria`}
                    formId={formId}
                    data-id={`${dataId}-criteria`}
                />

                <UniversalFormField
                    name={`${name}.filteringCriteriaSets`}
                    formId={formId}
                    data-id={`${dataId}-filtering-criteria-sets`}
                />
            </Stack>
        );
    }

    return (
        <Box data-testid="FilteringCriteriaField" data-id="vDmMrU89">
            <Button
                startIconName="Plus"
                label={t`Add filtering criteria`}
                level="tertiary"
                data-testid="FilteringCriteriaField"
                data-id="cfnwkuTm"
                size="md"
                onClick={handleAddFilteringCriteria}
            />
        </Box>
    );
};
