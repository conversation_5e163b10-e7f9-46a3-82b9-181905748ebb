import { Box } from '@cosmos/components/box';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { breakpointMd } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedMonitoringCreateTestModel } from '@models/monitoring-create-test';
import { AppButton } from '@ui/app-button';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { MONITORING_BUILDER_FORM_ID } from './constants/monitoring-builder.constant';
import { sharedMonitoringBuilderFormModel } from './models/monitoring-builder-form.model';

export const MonitoringBuilderView = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action((values: FormValues) => {
        sharedMonitoringBuilderFormModel.createCustomTest(values);
    });

    return (
        <Stack
            data-testid="MonitoringBuilderView"
            data-id="EUSbJW_8"
            justify="center"
            gap="xl"
        >
            <Stack gap="xl" direction="column">
                <Stack direction="column" gap="2xl">
                    <Text type="title" size="500">
                        {t`Create custom test`}
                    </Text>
                    <Stack direction="column" gap="xl">
                        <Text type="subheadline" size="300">{t`Details`}</Text>
                        <Text size="200">{t`[Name and description are editable after completion].`}</Text>
                    </Stack>
                    <KeyValuePair
                        label={t`Test name`}
                        value={sharedMonitoringCreateTestModel.testName}
                    />
                    <KeyValuePair
                        label={t`Description`}
                        value={sharedMonitoringCreateTestModel.testDescription}
                    />
                </Stack>
                <Box width={breakpointMd}>
                    <Form
                        hasExternalSubmitButton
                        data-id="monitoring-builder-form"
                        ref={formRef}
                        formId={MONITORING_BUILDER_FORM_ID}
                        schema={sharedMonitoringBuilderFormModel.formSchema}
                        onSubmit={handleSubmit}
                    />
                    <Stack gap="md" direction="row" align="end" py="xl">
                        <AppButton
                            type="button"
                            data-id="monitoring-builder-form-submit"
                            colorScheme="primary"
                            label={t`Save draft test`}
                            level="primary"
                            onClick={() => {
                                triggerSubmit();
                            }}
                        />
                        <AppButton
                            type="button"
                            data-id="monitoring-builder-cancel"
                            colorScheme="primary"
                            label={t`Cancel`}
                            level="secondary"
                            onClick={() => {
                                triggerSubmit();
                            }}
                        />
                    </Stack>
                </Box>
            </Stack>
        </Stack>
    );
});
