import { noop } from 'lodash-es';
import { useEffect, useRef } from 'react';
import { observer, runInAction } from '@globals/mobx';
import { sharedResourceGuideModel } from '@models/resource-guide';
import {
    type FormSchema,
    FormWrapper,
    UniversalFormField,
    useUniversalFieldController,
} from '@ui/forms';

const ResourceGuideFormFieldChangeDetector = observer((): null => {
    const [providerField] = useUniversalFieldController<'select'>('provider');
    const [serviceField] = useUniversalFieldController<'select'>('service');
    const [resourceField] = useUniversalFieldController<'select'>('resource');
    const previousProviderRef = useRef(providerField.value);
    const previousServiceRef = useRef(serviceField.value);
    const previousResourceRef = useRef(resourceField.value);
    const { handleProviderChange, handleServiceChange, handleResourceChange } =
        sharedResourceGuideModel;

    useEffect(() => {
        if (
            !providerField.value ||
            providerField.value.value === previousProviderRef.current?.value
        ) {
            previousProviderRef.current = providerField.value;

            return;
        }

        runInAction(() => {
            handleProviderChange(providerField.value);

            if (serviceField.value) {
                serviceField.setValue({
                    id: 'all-services',
                    label: 'All services',
                    value: 'all-services',
                });
            }
        });

        previousProviderRef.current = providerField.value;
    }, [
        handleProviderChange,
        providerField.value,
        serviceField,
        resourceField,
    ]);

    useEffect(() => {
        if (
            !serviceField.value ||
            serviceField.value.value === previousServiceRef.current?.value
        ) {
            previousServiceRef.current = serviceField.value;

            return;
        }

        runInAction(() => {
            handleServiceChange(serviceField.value);
            handleResourceChange(undefined);
        });

        previousServiceRef.current = serviceField.value;
    }, [
        serviceField.value,
        handleServiceChange,
        resourceField,
        handleResourceChange,
    ]);

    useEffect(() => {
        if (
            !resourceField.value ||
            resourceField.value.value === previousResourceRef.current?.value
        ) {
            previousResourceRef.current = resourceField.value;

            return;
        }

        runInAction(() => {
            handleResourceChange(resourceField.value);
        });

        previousResourceRef.current = resourceField.value;
    }, [resourceField.value, handleResourceChange]);

    return null;
});

export const ResourceGuideFormFields = observer(
    ({ formSchema }: { formSchema: FormSchema }): React.JSX.Element => {
        return (
            <FormWrapper
                formId="resource-guide-form"
                schema={formSchema}
                data-id="resource-guide-form"
                onSubmit={noop}
            >
                <ResourceGuideFormFieldChangeDetector />

                <UniversalFormField
                    formId="resource-guide-form"
                    name="provider"
                    data-id="provider-select-aside"
                />

                <UniversalFormField
                    formId="resource-guide-form"
                    name="service"
                    data-id="service-select-aside"
                />

                <UniversalFormField
                    formId="resource-guide-form"
                    name="resource"
                    data-id="resource-select-aside"
                />
            </FormWrapper>
        );
    },
);
