import type { ClientTypeEnum } from '@globals/api-sdk/types';
import type { ProviderType } from '@globals/providers';

export const ALLOWED_PROVIDER_TYPES: ProviderType[] = [
    'INFRASTRUCTURE',
    'CUSTOM',
];

export const ALLOWED_INFRASTRUCTURE_PROVIDERS: ClientTypeEnum[] = [
    'AWS',
    'GCP',
    'AZURE_ORG_UNITS',
];

export const ALLOWED_PROVIDERS: ClientTypeEnum[] = [
    ...ALLOWED_INFRASTRUCTURE_PROVIDERS,
    'CUSTOM',
];
