import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export class MonitoringEditBuilderPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'monitoring-edit-builder';

    get title(): string {
        return t`Edit test`;
    }

    tabs = [];

    utilities = {
        utilitiesList: ['resource_guide_for_monitoring_tests'],
    };
}
