import { isEmpty, isNil, isObject, isString } from 'lodash-es';
import { activeMonitoringNonV2Controller } from '@controllers/monitoring-details';
import { sharedProviderServicesController } from '@controllers/provider-services';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { monitorsControllerUpdateDraftTestMutation } from '@globals/api-sdk/queries';
import type {
    ClientTypeEnum,
    CreateCustomTestRequestDto,
    CustomTestRecipeRequestDto,
    ProviderResourceAttributesResponseDto,
    RecipeDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { ALLOWED_INFRASTRUCTURE_PROVIDERS } from '../constants/allowed-providers.constant';
import type {
    Attribute,
    ConditionGroup,
    ConditionSet,
} from '../types/form-value.type';

/**
 * Model for editing existing custom tests.
 * Extracts data from monitorDetailsData and provides clean values for form hydration.
 */
export class MonitoringEditFormModel {
    isDataSet = false;

    currentOnSuccessCallback?: () => void;

    updateCustomTestMutation = new ObservedMutation(
        monitorsControllerUpdateDraftTestMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'custom-test-updated',
                    props: {
                        title: t`Test updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Refresh the monitor data
                activeMonitoringNonV2Controller.refresh();

                // Call the custom onSuccess callback if provided
                this.currentOnSuccessCallback?.();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'custom-test-update-error',
                    props: {
                        title: t`Failed to update test`,
                        description: t`An error occurred while updating the test. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Gets the evaluation threshold value from the existing test data.
     */
    get evaluationThreshold(): ListBoxItemData | undefined {
        const monitorData = activeMonitoringNonV2Controller.monitorDetailsData;

        if (!monitorData?.recipes || isEmpty(monitorData.recipes)) {
            return undefined;
        }

        const recipe = monitorData.recipes[0];
        const assertionValue = recipe.recipe?.assertion;

        if (!assertionValue) {
            return undefined;
        }

        // Map assertion values to evaluation threshold options
        const evaluationOptions = [
            {
                id: 'all-pass',
                label: t`All results must pass`,
                value: 'nofail',
            },
            {
                id: 'at-least-one-pass',
                label: t`At least one result must pass`,
                value: 'hasonepass',
            },
            {
                id: 'only-one-fail',
                label: t`Only one result may fail`,
                value: 'failonce',
            },
        ];

        return evaluationOptions.find(
            (option) => option.value === assertionValue,
        );
    }

    /**
     * Gets the category value from the existing test data.
     */
    get category(): string | undefined {
        const monitorData = activeMonitoringNonV2Controller.monitorDetailsData;

        if (
            !monitorData?.availableConnections ||
            isEmpty(monitorData.availableConnections)
        ) {
            return 'CUSTOM';
        }

        // Check if any of the available connections are infrastructure providers (AWS, GCP, Azure)
        const hasInfrastructureConnection =
            monitorData.availableConnections.some((connection) =>
                ALLOWED_INFRASTRUCTURE_PROVIDERS.includes(
                    connection.clientType as ClientTypeEnum,
                ),
            );

        return hasInfrastructureConnection ? 'INFRASTRUCTURE' : 'CUSTOM';
    }

    /**
     * Gets the provider value from the existing test data.
     */
    get provider(): string | undefined {
        const monitorData = activeMonitoringNonV2Controller.monitorDetailsData;

        if (!monitorData?.recipes || isEmpty(monitorData.recipes)) {
            return undefined;
        }

        const recipe = monitorData.recipes[0];
        const recipeData = recipe.recipe as RecipeDto | null;

        if (!recipeData?.providers || isEmpty(recipeData.providers)) {
            return undefined;
        }

        const provider = recipeData.providers[0];

        return provider.provider;
    }

    /**
     * Gets the accounts configuration from the existing test data.
     */
    get accounts():
        | { allConnections: boolean; connectionIds: number[] }
        | undefined {
        const monitorData = activeMonitoringNonV2Controller.monitorDetailsData;

        if (!monitorData?.recipes || isEmpty(monitorData.recipes)) {
            return undefined;
        }

        const recipe = monitorData.recipes[0];

        return {
            allConnections: recipe.allConnections || false,
            connectionIds: recipe.connectionIds,
        };
    }

    /**
     * Checks if we have valid data to edit.
     */
    get hasValidData(): boolean {
        const monitorData = activeMonitoringNonV2Controller.monitorDetailsData;

        return Boolean(monitorData?.recipes && !isEmpty(monitorData.recipes));
    }

    /**
     * Gets the condition groups (services, resources, and conditions) from the existing test data.
     */
    get conditionGroups(): ConditionGroup[] {
        const monitorData = activeMonitoringNonV2Controller.monitorDetailsData;

        if (!monitorData?.recipes || isEmpty(monitorData.recipes)) {
            return [];
        }

        const recipe = monitorData.recipes[0];
        const recipeData = recipe.recipe as RecipeDto | null;

        if (!recipeData?.providers || isEmpty(recipeData.providers)) {
            return [];
        }

        // Convert each provider's resources into condition groups
        const conditionGroups: ConditionGroup[] = [];

        recipeData.providers.forEach((provider) => {
            if (!isEmpty(provider.resources)) {
                provider.resources.forEach((resource) => {
                    const recipeProvider: string = provider.provider;
                    const recipeResource: string = resource.resource;

                    const attributes =
                        sharedProviderServicesController.getProviderAttributes(
                            recipeProvider,
                            recipeResource,
                        );

                    // Type guard to check if resource has filteringCriteria
                    const hasFilteringCriteria =
                        'filteringCriteria' in resource &&
                        resource.filteringCriteria;
                    const filteringCriteria = hasFilteringCriteria
                        ? (resource.filteringCriteria as Record<
                              string,
                              unknown
                          >)
                        : null;

                    const conditionGroup: ConditionGroup = {
                        service: {
                            id:
                                attributes?.resource.service ??
                                'no-resource-found',
                            label:
                                attributes?.resource.service ??
                                'no-resource-found',
                            value:
                                attributes?.resource.service ??
                                'no-resource-found',
                        },
                        resource: {
                            id: resource.resource,
                            label: resource.resource,
                            value: resource.resource,
                        },
                        hasFilteringCriteria: hasFilteringCriteria
                            ? 'true'
                            : 'false',
                        criteria:
                            filteringCriteria &&
                            'mode' in filteringCriteria &&
                            isString(filteringCriteria.mode)
                                ? filteringCriteria.mode
                                : 'inclusion',
                        count: 0, // This might need to be calculated based on conditions
                        conditionSets: this.extractConditionSets(
                            resource.evaluator,
                            attributes,
                        ),
                        filteringCriteriaSets:
                            filteringCriteria &&
                            'evaluator' in filteringCriteria
                                ? this.extractConditionSets(
                                      filteringCriteria.evaluator,
                                      attributes,
                                  )
                                : [],
                    };

                    conditionGroups.push(conditionGroup);
                });
            }
        });

        return conditionGroups;
    }

    /**
     * Extracts condition sets from a TopLevelCondition structure.
     */
    private extractConditionSets(
        evaluator: unknown,
        apiResponse?: ProviderResourceAttributesResponseDto | null,
    ): ConditionSet[] {
        if (!evaluator || !isObject(evaluator)) {
            return [];
        }

        const evaluatorObj = evaluator as Record<string, unknown>;

        // Handle 'all' conditions (AND logic)
        if (Array.isArray(evaluatorObj.all)) {
            return [
                {
                    operatorLogical: 'AND' as const,
                    attributes: this.extractAttributes(
                        evaluatorObj.all,
                        apiResponse,
                    ),
                },
            ];
        }

        // Handle 'any' conditions (OR logic)
        if (Array.isArray(evaluatorObj.any)) {
            return [
                {
                    operatorLogical: 'OR' as const,
                    attributes: this.extractAttributes(
                        evaluatorObj.any,
                        apiResponse,
                    ),
                },
            ];
        }

        // Handle direct condition properties
        if (
            isString(evaluatorObj.fact) &&
            isString(evaluatorObj.operator) &&
            evaluatorObj.value !== undefined
        ) {
            return [
                {
                    operatorLogical: 'AND' as const,
                    attributes: [
                        {
                            attribute: {
                                id: evaluatorObj.fact,
                                label: evaluatorObj.fact,
                                value: evaluatorObj.fact,
                            },
                            operator: {
                                id: evaluatorObj.operator,
                                label: evaluatorObj.operator,
                                value: evaluatorObj.operator,
                            },
                            value: this.formatValueForOperator(
                                apiResponse,
                                evaluatorObj.fact,
                                evaluatorObj.operator,
                                evaluatorObj.value as
                                    | string
                                    | boolean
                                    | number
                                    | null,
                            ),
                            subFilteringCriteriaSets: [],
                        },
                    ],
                },
            ];
        }

        return [];
    }

    /**
     * Extracts attributes from a nested condition array.
     */
    private extractAttributes(
        conditions: unknown[],
        apiResponse?: ProviderResourceAttributesResponseDto | null,
    ): Attribute[] {
        if (isEmpty(conditions)) {
            return [];
        }

        return conditions.map((condition: unknown) => {
            if (!condition || !isObject(condition)) {
                return {
                    attribute: undefined,
                    operator: undefined,
                    value: undefined,
                    subFilteringCriteriaSets: [],
                };
            }

            const conditionObj = condition as Record<string, unknown>;

            // Handle nested conditions
            if (conditionObj.all || conditionObj.any) {
                return {
                    attribute: undefined,
                    operator: undefined,
                    value: undefined,
                    subFilteringCriteriaSets: this.extractConditionSets(
                        condition,
                        apiResponse,
                    ),
                };
            }

            // Handle direct condition properties
            const fact = isString(conditionObj.fact) ? conditionObj.fact : '';
            const operator = isString(conditionObj.operator)
                ? conditionObj.operator
                : '';

            return {
                attribute: {
                    id: fact,
                    label: fact,
                    value: fact,
                },
                operator: {
                    id: operator,
                    label: operator,
                    value: operator,
                },
                value: this.formatValueForOperator(
                    apiResponse,
                    fact,
                    operator,
                    conditionObj.value as string | boolean | number | null,
                ),
                subFilteringCriteriaSets: [],
            };
        });
    }

    /**
     * Formats a value based on the operator's valueTypes from the API response.
     * Uses the same logic as selectComponentType in ValueComponent.
     */
    private formatValueForOperator(
        apiResponse: ProviderResourceAttributesResponseDto | null | undefined,
        attribute: string,
        operator: string,
        value: string | boolean | number | null | undefined,
    ): string | ListBoxItemData {
        // Convert value to string safely
        const stringValue = isNil(value) ? '' : String(value);

        // Type guard for API response structure
        if (!apiResponse) {
            return stringValue;
        }

        // Find the attribute in the API response
        if (!Array.isArray(apiResponse.properties)) {
            return stringValue;
        }

        const selectedProperty = apiResponse.properties.find(
            (property) => property.name === attribute,
        );

        if (!selectedProperty) {
            return stringValue;
        }

        if (!Array.isArray(selectedProperty.operators)) {
            return stringValue;
        }

        // Find the operator and get its valueTypes
        const selectedOperator = selectedProperty.operators.find(
            (op) => op.operator === operator,
        );

        if (!selectedOperator) {
            return stringValue;
        }

        const valueTypes = Array.isArray(selectedOperator.valueTypes)
            ? selectedOperator.valueTypes
            : [];

        // Use the same logic as selectComponentType from ValueComponent
        if (isEmpty(valueTypes)) {
            return stringValue;
        }

        if (valueTypes.includes('boolean') || valueTypes.includes('object')) {
            // Return as ListBoxItemData object for BooleanSelect fields
            return {
                id: stringValue,
                label: stringValue,
                value: stringValue,
            };
        }

        // Return as string for text/number/date inputs
        return stringValue;
    }

    /**
     * Gets all the extracted data in a single object for easy consumption.
     */
    get extractedData(): {
        evaluationThreshold: ListBoxItemData | undefined;
        category: string | undefined;
        provider: string | undefined;
        accounts:
            | { allConnections: boolean; connectionIds: number[] }
            | undefined;
        conditionGroups: ConditionGroup[];
    } {
        return {
            evaluationThreshold: this.evaluationThreshold,
            category: this.category,
            provider: this.provider,
            accounts: this.accounts,
            conditionGroups: this.conditionGroups,
        };
    }

    /**
     * Updates an existing custom test using the PUT endpoint.
     */
    updateCustomTest = (
        testId: number,
        data: CreateCustomTestRequestDto,
        onSuccess?: () => void,
    ): void => {
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!currentWorkspaceId) {
            return;
        }

        // Convert CreateCustomTestRequestDto to the expected format
        const updateDto = {
            allConnections: data.recipe.allConnections,
            connectionIds: data.connectionIds ?? [],
            providerType: data.providerType,
            recipe: data.recipe,
        };

        const { isLoading } = sharedWorkspacesController;

        // Store the callback for use in the mutation's onSuccess
        this.currentOnSuccessCallback = onSuccess;

        when(
            () => !isLoading,
            () => {
                this.updateCustomTestMutation.mutate({
                    path: {
                        testId,
                        xProductId: currentWorkspaceId,
                    },
                    body: updateDto as unknown as CustomTestRecipeRequestDto, // Type assertion needed due to API SDK type mismatch
                });
            },
        );
    };
}
