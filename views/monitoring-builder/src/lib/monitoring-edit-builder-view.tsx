import { useCallback, useEffect, useMemo } from 'react';
import { sharedConnectionsController } from '@controllers/connections';
import { activeMonitoringNonV2Controller } from '@controllers/monitoring-details';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import { logger } from '@globals/logger';
import { action, observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { MONITORING_EDIT_BUILDER_FORM_ID } from './constants/monitoring-builder.constant';
import { toCustomTestRequestDto } from './helpers/form-values-reader.helper';
import { sharedMonitoringBuilderFormModel } from './models/monitoring-builder-form.model';
import { MonitoringEditFormModel } from './models/monitoring-edit-form.model';

export const MonitoringEditBuilderView = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { testId } = useParams();

    const sharedMonitoringEditFormModel = useMemo(
        () => new MonitoringEditFormModel(),
        [],
    );

    const handleNavigateToOverview = useCallback(() => {
        if (!testId) {
            logger.error({
                message: 'Cannot navigate to overview: missing testId',
                additionalInfo: {
                    action: 'handleNavigateToOverview',
                    url: window.location.href,
                },
            });

            return;
        }

        const overviewUrl = `${routeController.userPartOfUrl}/compliance/monitoring/production/${testId}/overview`;

        sharedProgrammaticNavigationController.navigateTo(overviewUrl);
    }, [testId]);

    const handleSubmit = action((values: FormValues) => {
        if (!testId) {
            logger.error({
                message: 'Missing testId in monitoring edit builder',
                additionalInfo: {
                    action: 'handleSubmit',
                    hasValues: Boolean(values),
                    url: window.location.href,
                },
            });

            return;
        }

        const dto = toCustomTestRequestDto(
            values,
            sharedMonitoringBuilderFormModel.accountsOptionsByProvider,
            '', // name not needed for update
            '', // description not needed for update
        );

        sharedMonitoringEditFormModel.updateCustomTest(
            Number(testId),
            dto,
            handleNavigateToOverview,
        );
    });

    const { isLoading } = activeMonitoringNonV2Controller;
    const { isLoading: isConnectionsLoading } = sharedConnectionsController;
    const { extractedData, isDataSet, hasValidData } =
        sharedMonitoringEditFormModel;

    // Hydrate the form when monitor data is loaded
    useEffect(() => {
        if (!hasValidData || isLoading || isConnectionsLoading) {
            return;
        }

        sharedMonitoringBuilderFormModel.setInitialValues(extractedData);
        sharedMonitoringEditFormModel.isDataSet = true;
    }, [
        extractedData,
        hasValidData,
        isConnectionsLoading,
        isLoading,
        sharedMonitoringEditFormModel,
    ]);

    if (isLoading || isConnectionsLoading || !isDataSet) {
        return <div>Loading...</div>;
    }

    return (
        <Stack
            data-testid="MonitoringEditBuilderView"
            data-id="EUSbJW_8"
            justify="center"
        >
            <Box width={breakpointMd}>
                <Form
                    hasExternalSubmitButton
                    formId={MONITORING_EDIT_BUILDER_FORM_ID}
                    data-id="monitoring-edit-builder-form"
                    ref={formRef}
                    schema={sharedMonitoringBuilderFormModel.formSchema}
                    onSubmit={handleSubmit}
                />
                <Button
                    type="button"
                    data-id="monitoring-edit-builder-form-submit"
                    colorScheme="primary"
                    label="Save draft"
                    level="primary"
                    onClick={triggerSubmit}
                />
            </Box>
        </Stack>
    );
});
