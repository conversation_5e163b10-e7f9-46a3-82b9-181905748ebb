import { isNil } from 'lodash-es';
import { useEffect } from 'react';
import { DocumentViewer } from '@components/document-viewer';
import { sharedAuditHubEvidenceViewerController } from '@controllers/audit-hub-evidence-viewer';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const AuditHubEvidenceViewerView = observer((): JSX.Element => {
    const { evidenceDocument, isGeneratingEvidence } =
        sharedAuditHubEvidenceViewerController;

    useEffect(() => {
        /** Cleanup when component unmounts. */
        return () => {
            sharedAuditHubEvidenceViewerController.clearSelectedEvidence();
        };
    }, []);

    const renderContent = (): React.JSX.Element => {
        if (isNil(evidenceDocument)) {
            return <EmptyState title={t`No evidence selected`} />;
        }

        if (isGeneratingEvidence) {
            return (
                <Loader
                    label={t`Generating evidence document...`}
                    data-testid="EvidenceGeneratingLoader"
                    data-id="evidence-generating-loader"
                />
            );
        }

        // Ensure we have either a signed URL or file data before rendering
        const hasViewableContent = Boolean(
            evidenceDocument.signedUrl ||
                (evidenceDocument.fileData && evidenceDocument.fileName),
        );

        if (!hasViewableContent) {
            // Trigger evidence generation if content is not available
            // The controller method handles checking if generation is already in progress
            sharedAuditHubEvidenceViewerController.generateEvidenceForViewing();

            return (
                <EmptyState
                    title={t`Evidence not available`}
                    description={t`The evidence document could not be loaded.`}
                />
            );
        }

        return (
            <DocumentViewer
                src={evidenceDocument.signedUrl || ''}
                data-id="evidence-viewer-iframe"
                data-testid="renderContent"
                label={evidenceDocument.name || t`Evidence Document`}
                fileType={evidenceDocument.fileType}
                fileData={evidenceDocument.fileData}
            />
        );
    };

    return (
        <Stack
            justify="center"
            align="center"
            width="100%"
            height="100%"
            data-testid="EvidenceViewerContainer"
            data-id="evidence-viewer-container"
        >
            {renderContent()}
        </Stack>
    );
});
