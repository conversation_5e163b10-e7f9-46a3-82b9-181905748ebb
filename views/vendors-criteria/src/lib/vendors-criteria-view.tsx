import { AppDatatable } from '@components/app-datatable';
import { vendorsCriteriaDatatableController } from '@controllers/vendors';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export const VendorsCriteriaView = observer(() => {
    const { currentWorkspace } = sharedWorkspacesController;

    if (!currentWorkspace) {
        return null;
    }

    if (!sharedFeatureAccessModel.isVrmAgentMvpEnabled) {
        return null;
    }

    return (
        <AppDatatable
            controller={vendorsCriteriaDatatableController}
            data-id="datatable-vendors-criteria-data-id"
            density="normal"
            defaultColumnOptions={{
                size: 'auto',
                maxSize: 'auto',
            }}
        />
    );
});
