import type {
    FailsByCategoryResponseDto,
    MonitorsV2ControllerListProdMonitorsData,
    MonitorTestInstanceResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getCheckStatusOptions = (): {
    label: string;
    value: MonitorTestInstanceResponseDto['checkStatus'];
}[] => [
    { label: t`Unused`, value: 'UNUSED' },
    { label: t`New`, value: 'NEW' },
    { label: t`Enabled`, value: 'ENABLED' },
    { label: t`Disabled`, value: 'DISABLED' },
    { label: t`Testing`, value: 'TESTING' },
];

/**
 * Get monitor check type options using the API type.
 */
export const getMonitorCheckTypeOptions = (): {
    label: string;
    value: FailsByCategoryResponseDto['category'];
}[] => [
    { label: t`Device`, value: 'AGENT' },
    { label: t`Identity Provider`, value: 'IDENTITY' },
    { label: t`In Drata`, value: 'IN_DRATA' },
    { label: t`Infrastructure`, value: 'INFRASTRUCTURE' },
    { label: t`Observability`, value: 'OBSERVABILITY' },
    { label: t`Policy`, value: 'POLICY' },
    { label: t`Version Control`, value: 'VERSION_CONTROL' },
    { label: t`Ticketing`, value: 'TICKETING' },
    { label: t`HRIS`, value: 'HRIS' },
    { label: t`Custom`, value: 'CUSTOM' },
];

/**
 * Get test type options using the API type.
 */
export const getTestTypeOptions = (): {
    label: string;
    value: NonNullable<
        NonNullable<
            MonitorsV2ControllerListProdMonitorsData['query']
        >['allowedTestSources']
    >[number];
}[] => [
    { label: t`Drata`, value: 'DRATA' },
    { label: t`Custom (published)`, value: 'CUSTOM_PUBLISHED' },
    { label: t`Custom (draft)`, value: 'CUSTOM_DRAFT' },
];
