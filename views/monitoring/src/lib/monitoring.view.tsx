import { AppDatatable } from '@components/app-datatable';
import { sharedMonitoringController } from '@controllers/monitoring';
import type { RowActionItem } from '@cosmos/components/datatable';
import { Icon } from '@cosmos/components/icon';
import type { MonitorTestInstanceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringFiltersModel } from '@models/monitoring';
import { useNavigate } from '@remix-run/react';
import { MonitoringHeader } from './components/monitoring-header.component';
import { getMonitoringColumns } from './constants/monitoring-columns.constant';
import { MonitoringModel } from './models/monitoring.model';
import { MonitoringBulkActionsModel } from './models/monitoring-bulk-actions.model';

const getMonitoringRowActions = (
    row: MonitorTestInstanceResponseDto,
): RowActionItem[] => {
    const { testId, checkStatus, testType } = row;
    const actions: RowActionItem[] = [];

    // Test now - Only if test has status of "enabled"
    if (checkStatus === 'ENABLED') {
        actions.push({
            id: 'test-now',
            label: t`Test now`,
            onSelect: () => {
                sharedMonitoringController.handleTestNow(testId);
            },
        });
    }

    // Stop testing - Only if test has status of "Testing"
    if (checkStatus === 'TESTING') {
        actions.push({
            id: 'stop-testing',
            label: t`Stop testing`,
            onSelect: () => {
                sharedMonitoringController.handleStopTesting(testId);
            },
        });
    }

    // View help article - Only for Drata tests
    if (testType === 'DRATA') {
        actions.push({
            id: 'view-help',
            label: t`View help article`,
            endSlot: <Icon name="LinkOut" />,
            onSelect: () => {
                sharedMonitoringController.handleViewHelpArticle();
            },
        });
    }

    return actions;
};

export const MonitoringView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const { tableActions } = new MonitoringModel();
    const { bulkActions, handleRowSelection } =
        new MonitoringBulkActionsModel();

    const {
        monitoringListData,
        monitoringListTotal,
        monitoringListLoad,
        isLoading,
    } = sharedMonitoringController;

    const { filters } = sharedMonitoringFiltersModel;

    return (
        <>
            <MonitoringHeader />

            <AppDatatable
                isFullPageTable
                isRowSelectionEnabled
                isLoading={isLoading}
                tableId="datatable-monitoring"
                data-id="datatable-monitoring"
                data={monitoringListData}
                columns={getMonitoringColumns()}
                total={monitoringListTotal}
                filterProps={filters}
                bulkActionDropdownItems={bulkActions}
                getRowId={(row) => String(row.testId)}
                tableActions={tableActions}
                rowActionsProps={{
                    type: 'dropdown',
                    getRowActions: getMonitoringRowActions,
                }}
                emptyStateProps={{
                    title: t`No monitoring tests found`,
                    description: t`No monitoring tests found`,
                }}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t`Search`,
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={monitoringListLoad}
                onRowSelection={handleRowSelection}
                onRowClick={({ row }) => {
                    navigate(`${row.testId}/overview`);
                }}
            />
        </>
    );
});
