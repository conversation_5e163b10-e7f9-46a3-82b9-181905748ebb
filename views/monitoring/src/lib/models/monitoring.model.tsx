import {
    type ConnectionProps,
    sharedConnectionsController,
} from '@controllers/connections';
import { modalController } from '@controllers/modal';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import type { TableAction } from '@cosmos/components/datatable';
import { monitorsControllerGetMonitorTestMetadataOptions } from '@globals/api-sdk/queries';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { PreventCreateTestModal } from '../components/prevent-create-test-modal';
import { PREVENT_CREATE_TEST_MODAL_ID } from '../constants/prevent-create-test-modal.constant';
import { openCreateTestModal } from '../helpers/create-test-modal.helper';

export class MonitoringModel {
    constructor() {
        makeAutoObservable(this);
    }

    monitorTestMetadataQuery = new ObservedQuery(
        monitorsControllerGetMonitorTestMetadataOptions,
    );

    get isLimitReached(): boolean {
        return this.monitorTestMetadataQuery.data?.limitReached ?? false;
    }

    get testLimit(): number {
        return this.monitorTestMetadataQuery.data?.limit ?? 0;
    }

    hasCustomOrInfrastructureConnection = (
        connections: ConnectionProps[],
        workspaceId: number,
    ): boolean => {
        const supportedProviderTypes: ClientTypeEnum[] = [
            'AWS',
            'AWS_ORG_UNITS',
            'AZURE_ORG_UNITS',
            'AZURE',
            'CUSTOM',
            'GCP',
        ];

        return connections.some(
            (connection) =>
                connection.workspaces.some(
                    (workspace) => workspace.id === workspaceId,
                ) &&
                supportedProviderTypes.includes(connection.clientType) &&
                (connection.clientType === 'CUSTOM' ||
                    connection.providerTypes.some(
                        (providerType) =>
                            providerType.value === 'INFRASTRUCTURE' &&
                            providerType.isEnabled,
                    )),
        );
    };

    checkTestLimitAndNavigate = (connections: ConnectionProps[]): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace?.id) {
            return;
        }

        if (
            !sharedFeatureAccessModel.isCustomTestEnabled ||
            !sharedFeatureAccessModel.hasMonitorManagePermission
        ) {
            return;
        }

        // First check if user has a supported connection
        if (
            !this.hasCustomOrInfrastructureConnection(
                connections,
                currentWorkspace.id,
            )
        ) {
            this.openPreventCreateTestModal('missing-connection');

            return;
        }

        openCreateTestModal({
            onSuccess: () => {
                this.proceedWithTestCreation(currentWorkspace.id);
            },
        });
    };

    proceedWithTestCreation = (workspaceId: number): void => {
        // Load the latest metadata to check current limit status
        this.monitorTestMetadataQuery.load({
            path: {
                xProductId: workspaceId,
            },
        });

        // Wait for the API call to complete and then navigate
        when(
            () =>
                !this.monitorTestMetadataQuery.isLoading &&
                (Boolean(this.monitorTestMetadataQuery.data) ||
                    Boolean(this.monitorTestMetadataQuery.error)),
            () => {
                // Handle API error case
                if (this.monitorTestMetadataQuery.error) {
                    logger.error({
                        message: 'Failed to fetch monitor test metadata',
                        additionalInfo: {
                            action: 'checkTestLimitAndNavigate',
                            workspaceId,
                        },
                        errorObject: {
                            message: String(
                                this.monitorTestMetadataQuery.error,
                            ),
                            statusCode: 'unknown',
                        },
                    });

                    snackbarController.addSnackbar({
                        id: 'monitor-test-metadata-error',
                        props: {
                            title: t`Unable to check test limits`,
                            description: t`Please try again in a moment.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (this.isLimitReached) {
                    this.openPreventCreateTestModal('limit-reached');

                    return;
                }

                sharedProgrammaticNavigationController.navigateTo(
                    `${routeController.userPartOfUrl}/compliance/monitoring/create`,
                );
            },
        );
    };

    openPreventCreateTestModal = (
        scenario: 'limit-reached' | 'missing-connection',
    ): void => {
        modalController.openModal({
            id: PREVENT_CREATE_TEST_MODAL_ID,
            content: () => (
                <PreventCreateTestModal
                    scenario={scenario}
                    testLimit={this.testLimit}
                    data-id="JKej-he6"
                    onClose={() => {
                        modalController.closeModal(
                            PREVENT_CREATE_TEST_MODAL_ID,
                        );
                    }}
                />
            ),
            size: 'sm',
            centered: true,
        });
    };

    get tableActions(): TableAction[] {
        const actions: TableAction[] = [];

        const { allConfiguredConnections } = sharedConnectionsController;

        if (
            sharedFeatureAccessModel.hasCustomConnectionsAndTests &&
            sharedFeatureAccessModel.hasMonitorManagePermission
        ) {
            actions.push({
                actionType: 'button',
                id: 'create-button',
                typeProps: {
                    label: t`Create test`,
                    level: 'secondary',
                    onClick: action(() => {
                        this.checkTestLimitAndNavigate(
                            allConfiguredConnections,
                        );
                    }),
                },
            });
        }

        return actions;
    }
}
