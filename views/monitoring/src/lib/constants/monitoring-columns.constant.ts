import {
    MonitoringTableCellCategoryComponent,
    MonitoringTableCellConnectionsComponent,
    MonitoringTableCellFindingsComponent,
    MonitoringTableCellResultComponent,
    MonitoringTableCellStatusComponent,
    MonitoringTableCellTestNameComponent,
} from '@components/monitoring';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { MonitorTestInstanceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getMonitoringColumns =
    (): DatatableProps<MonitorTestInstanceResponseDto>['columns'] => [
        {
            accessorKey: 'testName',
            header: t`Name`,
            id: 'name',
            enableSorting: false,
            minSize: 300,
            cell: MonitoringTableCellTestNameComponent,
        },
        {
            accessorKey: 'connections',
            header: t`Result`,
            id: 'checkResultStatus',
            enableSorting: true,
            cell: MonitoringTableCellResultComponent,
        },
        {
            accessorKey: 'findingsCount',
            header: t`Findings`,
            id: 'findingsCount',
            enableSorting: true,
            cell: MonitoringTableCellFindingsComponent,
        },
        {
            accessorKey: 'connections',
            header: t`Status`,
            id: 'checkStatus',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: MonitoringTableCellStatusComponent,
        },
        {
            accessorKey: 'connections',
            header: t`Category`,
            id: 'category',
            enableSorting: true,
            cell: MonitoringTableCellCategoryComponent,
        },
        {
            accessorKey: 'connections',
            header: t`Active connections`,
            id: 'activeConnections',
            enableSorting: false,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: MonitoringTableCellConnectionsComponent,
        },
    ];
