import { sharedMonitoringStatsController } from '@controllers/monitoring';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const MonitoringHeader = observer((): React.JSX.Element => {
    const {
        monitoringPassingPercents,
        monitoringStatsFailedTests,
        monitoringStatsPassedTests,
        isLoadingStats,
    } = sharedMonitoringStatsController;

    if (isLoadingStats) {
        return (
            <Card
                title={t`Tests passed`}
                body={<Loader isSpinnerOnly label={t`Loading...`} />}
            />
        );
    }

    return (
        <Grid columns="3" gap="4x" pb="4x" data-id="2XDwd8IK">
            <Box
                borderColor="neutralBorderInitial"
                borderWidth="borderWidth1"
                borderRadius="borderRadius2x"
                p="4x"
            >
                <StatBlock
                    title={t`Tests Passed`}
                    statValue={`${monitoringPassingPercents} %`}
                    totalText={t`Percentage of tests passed, excludes draft tests`}
                />
            </Box>
            <Box
                borderColor="neutralBorderInitial"
                borderWidth="borderWidth1"
                borderRadius="borderRadius2x"
                p="4x"
            >
                <StatBlock
                    title={t`Failed tests`}
                    statValue={monitoringStatsFailedTests}
                    statIcon="NotReady"
                    statIconColor="critical"
                    totalText={t`Excludes draft tests`}
                />
            </Box>
            <Box
                borderColor="neutralBorderInitial"
                borderWidth="borderWidth1"
                borderRadius="borderRadius2x"
                p="4x"
            >
                <StatBlock
                    title={t`Passed Tests`}
                    statValue={monitoringStatsPassedTests}
                    statIcon="CheckCircle"
                    statIconColor="success"
                    totalText={t`Excludes draft tests`}
                />
            </Box>
        </Grid>
    );
});
