import { isNil } from 'lodash-es';
import { useCallback, useEffect, useMemo } from 'react';
import { sharedFrameworkRequirementsUploadController } from '@controllers/frameworks';
import { routeController } from '@controllers/route';
import { Callout } from '@cosmos-lab/components/callout';
import {
    Wizard,
    type WizardStepDataProps,
} from '@cosmos-lab/components/wizard';
import { useLingui } from '@globals/i18n/macro';
import { action, flowResult, observer, when } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { sharedFrameworkUploadRequirementModel } from '@models/framework-create';
import { useLocation, useNavigate, useParams } from '@remix-run/react';
import { FrameworkRequirementsUploadWizardConfirmStep } from './framework-requirements-upload-confirm-upload-step.component';
import { FrameworkRequirementsUploadRequirementsWizardStep } from './framework-requirements-upload-requirements-step.component';

export const FrameworkRequirementsUploadView = observer(
    (): React.JSX.Element => {
        const { t } = useLingui();
        const location = useLocation();
        const { pathname } = location;
        const parentRoute = getParentRoute(pathname);
        const navigate = useNavigate();
        const { frameworkId } = useParams();
        const { isLoadingWizard } = sharedFrameworkUploadRequirementModel;

        useEffect(() => {
            if (frameworkId) {
                sharedFrameworkRequirementsUploadController.setFrameworkId(
                    parseInt(frameworkId),
                );
            }

            return () => {
                sharedFrameworkRequirementsUploadController.resetFrameworkId();
            };
        }, [frameworkId]);

        const goBackToFrameworks = useCallback((): void => {
            action(() => {
                sharedFrameworkRequirementsUploadController.resetRequirements();
                navigate(parentRoute);
            })();
        }, [navigate, parentRoute]);

        const goToFrameworkDetails = useCallback((): void => {
            const { userPartOfUrl } = routeController;
            const { isPendingUpdateRequirement } =
                sharedFrameworkRequirementsUploadController;

            when(
                () => !isPendingUpdateRequirement && !isNil(frameworkId),
                () => {
                    sharedFrameworkRequirementsUploadController.resetRequirements();
                    navigate(
                        `${userPartOfUrl}/compliance/frameworks/all/current/${frameworkId}/requirements`,
                    );
                },
            );
        }, [navigate, frameworkId]);

        const steps: WizardStepDataProps[] = useMemo(
            () => [
                {
                    component:
                        FrameworkRequirementsUploadRequirementsWizardStep,
                    stepTitle: t`Upload requirements`,
                    isStepSkippable: false,
                    onStepChange: action(() =>
                        flowResult(
                            sharedFrameworkRequirementsUploadController.handleUploadStep(),
                        ),
                    ),
                },
                {
                    component: FrameworkRequirementsUploadWizardConfirmStep,
                    stepTitle: t`Confirm requirements`,
                    isStepSkippable: false,
                    onStepChange: action(() =>
                        flowResult(
                            sharedFrameworkRequirementsUploadController.handleUploadRequirements(),
                        ),
                    ),
                },
                {
                    component: () => (
                        <Callout
                            data-testid="CreateFrameworkWizardConfirmStep"
                            illustrationName="Highfive"
                            size="lg"
                            primaryLabelText={t`Congratulations! Your requirements are uploaded.`}
                            secondaryLabelText={t`Enjoy tracking your compliance with Drata! You can map controls and add more requirements at any time.`}
                            data-id="mvHHAMF0"
                        />
                    ),
                    stepTitle: t`Finish`,
                    isStepSkippable: false,
                    canGoBack: false,
                },
            ],
            [t],
        );

        return (
            <Wizard
                steps={steps}
                isLoading={isLoadingWizard}
                data-testid="CreateFrameworkView"
                data-id="Rloh_HB8"
                completeButtonLabel={t`Done`}
                onCancel={goBackToFrameworks}
                onComplete={goToFrameworkDetails}
            />
        );
    },
);
