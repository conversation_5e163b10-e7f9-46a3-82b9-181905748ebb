import { sharedAuditCreationWizardController } from '@controllers/audit-creation-wizard';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedAuditCreationWizardSamplesModel } from '@models/audit-creation-wizard';
import { Form } from '@ui/forms';
import type { WizardStepProps } from '../types';

export const AuditSamplesStep = observer(({ formRef }: WizardStepProps) => {
    const formId = 'audit-samples-step';
    const { handleAuditSamplesSubmit } = sharedAuditCreationWizardController;
    const { auditSamplesFormSchema } = sharedAuditCreationWizardSamplesModel;

    return (
        <Stack
            direction="column"
            gap="5xl"
            data-id="audit-samples-step"
            data-testid="AuditSamplesStep"
        >
            <Stack direction="column" align="start" gap="md">
                <Trans>
                    <Text as="h1" type="headline" size="600">
                        Audit samples
                    </Text>
                    <Text as="p" type="subheadline" size="100">
                        Set your audit samples. Samples and audit evidence will
                        be included up to today. You can update your evidence
                        package later if you need more evidence.
                    </Text>
                </Trans>
            </Stack>

            <Form
                hasExternalSubmitButton
                formId={formId}
                ref={formRef}
                data-testid="AuditSamplesForm"
                data-id="audit-samples-form"
                schema={auditSamplesFormSchema}
                onSubmit={handleAuditSamplesSubmit}
            />
        </Stack>
    );
});
