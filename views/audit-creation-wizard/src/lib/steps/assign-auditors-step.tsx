import { sharedAuditCreationWizardController } from '@controllers/audit-creation-wizard';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedAssignAuditorsStepModel } from '@models/audit-creation-wizard';
import { AppLink } from '@ui/app-link';
import { Form } from '@ui/forms';
import type { WizardStepProps } from '../types';

export const AssignAuditorsStep = observer(({ formRef }: WizardStepProps) => {
    const { handleAssignAuditorsSubmit } = sharedAuditCreationWizardController;
    const { formSchema } = sharedAssignAuditorsStepModel;

    return (
        <Stack
            direction="column"
            gap="3xl"
            data-id="assign-auditors-step"
            data-testid="AssignAuditorsStep"
        >
            <Stack direction="column" align="start" gap="md">
                <Text as="h1" type="headline" size="600">
                    {t`Assign auditors`}
                </Text>
                <Text as="h2" type="subheadline" size="100" align="center">
                    {t`Select an existing auditor or add a new one to this audit.`}
                </Text>
            </Stack>

            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="assign-auditors-form"
                data-id="assign-auditors-form"
                schema={formSchema}
                onSubmit={handleAssignAuditorsSubmit}
            />

            <Text colorScheme="neutral">
                <Trans>
                    Can&apos;t find them?
                    <AppLink href="/auditors/new">Add new auditor</AppLink>
                </Trans>
            </Text>
        </Stack>
    );
});
