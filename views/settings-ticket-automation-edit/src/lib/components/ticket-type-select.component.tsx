import { useMemo } from 'react';
import { sharedTicketAutomationIssueTypesController } from '@controllers/ticket-automation-issue-types';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { observer } from '@globals/mobx';
import { TICKET_FORM_ID } from '../constants/ticket-form-id.constant';

interface Props {
    projectId: string;
    connectionId: number;
    issueType?: string;
    issueTypeId?: string;
    onChange: (ticketType: string, ticketTypeId: string) => void;
}

export const TicketTypeSelectComponent = observer(
    ({
        projectId: currentProjectId,
        connectionId,
        issueType: currentIssueType,
        issueTypeId: currentIssueTypeId,
        onChange,
    }: Props): React.JSX.Element => {
        const {
            isLoading,
            hasError,
            ticketAutomationIssueTypeList,
            loadTicketAutomationIssueTypes,
        } = sharedTicketAutomationIssueTypesController;

        loadTicketAutomationIssueTypes({
            connectionId,
            projectId: currentProjectId,
        });

        const ticketTypeOptions = useMemo(
            () =>
                hasError
                    ? [
                          {
                              id: '',
                              label: 'Unable to get Ticket types.',
                              value: '',
                          },
                      ]
                    : ticketAutomationIssueTypeList.map((ticketType) => {
                          return {
                              id: ticketType.value,
                              label: ticketType.name,
                              value: ticketType.value,
                          };
                      }),
            [hasError, ticketAutomationIssueTypeList],
        );

        const changeTicketTypeId = ({
            label: ticketType,
            id: ticketTypeId,
        }: ListBoxItemData) => {
            onChange(ticketType, ticketTypeId);
        };

        return (
            <SelectField
                label="Ticket type"
                formId={TICKET_FORM_ID}
                loaderLabel="Loading types"
                isLoading={isLoading}
                name={'type'}
                data-testid="TypeSelect"
                data-id={`${TICKET_FORM_ID}-type-tf`}
                options={ticketTypeOptions}
                value={{
                    id: currentIssueTypeId ?? '',
                    label: currentIssueType ?? 'Choose',
                    value: currentIssueTypeId ?? '',
                }}
                onChange={changeTicketTypeId}
            />
        );
    },
);
