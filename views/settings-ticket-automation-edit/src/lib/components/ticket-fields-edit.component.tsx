import { isNil } from 'lodash-es';
import { styled } from 'styled-components';
import {
    sharedTicketAutomationTicketFieldsController,
    type TicketFieldType,
} from '@controllers/ticket-automation-ticket-fields';
import { Loader } from '@cosmos/components/loader';
import type { TicketMonitoringFieldResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { TICKET_FORM_ID } from '../constants/ticket-form-id.constant';
import type { TicketFieldProps } from '../types/ticket-field-props.type';
import { TicketAssigneeSelectComponent } from './ticket-assignee-select.component';
import { TicketLabelSelectComponent } from './ticket-label-select.component';
import { TicketParentSelectComponent } from './ticket-parent-select.component';
import { TicketPrioritySelectComponent } from './ticket-priority-select.component';

interface Props {
    projectId: string;
    ticketTypeId?: string;
    connectionId: number;
    ticketMonitoringFields: TicketMonitoringFieldResponseDto[];
}

const FIELD_COMPONENT_MAP: Record<
    TicketFieldType,
    React.ComponentType<TicketFieldProps>
> = {
    assignee: TicketAssigneeSelectComponent,
    priority: TicketPrioritySelectComponent,
    labels: TicketLabelSelectComponent,
    parent: TicketParentSelectComponent,
};

const ScreenReaderOnlyContentDiv = styled.div`
    opacity: 0;
    height: 0;
    width: 0;
    overflow: hidden;
    position: absolute;
`;

export const TicketFieldsEditComponent = observer(
    ({
        connectionId,
        projectId,
        ticketTypeId,
        ticketMonitoringFields,
    }: Props): React.JSX.Element => {
        const {
            isLoading,
            ticketAutomationIssueTypeList,
            loadTicketAutomationFields,
        } = sharedTicketAutomationTicketFieldsController;

        if (isLoading) {
            return <Loader label={'Loading...'} />;
        }

        if (isNil(ticketTypeId)) {
            return (
                <ScreenReaderOnlyContentDiv>
                    Please select an issue type
                </ScreenReaderOnlyContentDiv>
            );
        }

        loadTicketAutomationFields({
            connectionId,
            projectId,
            issueTypeId: ticketTypeId,
        });

        return (
            <>
                {Object.entries(ticketAutomationIssueTypeList).map(
                    ([fieldType, field]) => {
                        const { label, value } =
                            ticketMonitoringFields.find(
                                (currentField) =>
                                    currentField.name === fieldType,
                            ) ?? {};

                        const FieldComponent =
                            FIELD_COMPONENT_MAP[fieldType as TicketFieldType];

                        return (
                            <FieldComponent
                                key={fieldType}
                                connectionId={connectionId}
                                projectId={projectId}
                                ticketTypeId={ticketTypeId}
                                field={field}
                                data-id={`${TICKET_FORM_ID}-${fieldType}-tf`}
                                currentLabel={label}
                                currentValue={value}
                            />
                        );
                    },
                )}
            </>
        );
    },
);
