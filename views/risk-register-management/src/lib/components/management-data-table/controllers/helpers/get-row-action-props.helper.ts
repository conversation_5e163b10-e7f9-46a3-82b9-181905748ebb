import { sharedRiskManagementMutationController } from '@controllers/risk';
import { snackbarController } from '@controllers/snackbar';
import type { DatatableProps } from '@cosmos/components/datatable';
import type {
    SchemaDropdownItemData,
    SchemaDropdownNestedItemData,
} from '@cosmos/components/schema-dropdown';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, when } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { managementDatatableController } from '../management-data-table.controller';
import { ManagementDataTableCellController } from '../management-data-table-cell.controller';

export function getRowActionProps(): DatatableProps<RiskWithCustomFieldsResponseDto>['rowActionsProps'] {
    return {
        type: 'dropdown',
        getRowActions: action((row: RiskWithCustomFieldsResponseDto) => {
            const managementDataTableCellController =
                new ManagementDataTableCellController(row);

            const actions: (
                | SchemaDropdownItemData
                | SchemaDropdownNestedItemData
            )[] = [
                {
                    id: 'add-risk-owners-row-action',
                    label: t`Add/remove risk owners`,
                    onSelect:
                        managementDataTableCellController.openRiskOwnerModal,
                },
                {
                    id: 'assign-risk-category-row-action',
                    label: t`Assign risk category`,
                    onSelect:
                        managementDataTableCellController.openRiskCategoriesModal,
                },
            ];

            const statusSubmenu: SchemaDropdownNestedItemData = {
                label: t`Status`,
                id: 'status-group',
                type: 'subMenu',
                items: [],
            };

            if (row.status !== 'ACTIVE') {
                statusSubmenu.items.push({
                    id: 'activate-row-action',
                    label: t`Activate`,
                    onSelect: () => {
                        managementDataTableCellController.updateRiskAndUpdateDataTable(
                            {
                                status: 'ACTIVE',
                            },
                        );

                        when(
                            () =>
                                !managementDataTableCellController.isMutationPending,
                            () => {
                                if (
                                    managementDataTableCellController.didMutationError
                                ) {
                                    return;
                                }

                                snackbarController.addSnackbar({
                                    id: 'risk-activate-success',
                                    props: {
                                        title: t`Risk activated`,
                                        severity: 'success',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });

                                closeConfirmationModal();
                            },
                        );
                    },
                });
            }

            if (row.status !== 'ARCHIVED') {
                statusSubmenu.items.push({
                    id: 'archive-row-action',
                    label: t`Archive`,
                    onSelect: () => {
                        managementDataTableCellController.updateRiskAndUpdateDataTable(
                            {
                                status: 'ARCHIVED',
                            },
                        );

                        when(
                            () =>
                                !managementDataTableCellController.isMutationPending,
                            () => {
                                if (
                                    managementDataTableCellController.didMutationError
                                ) {
                                    return;
                                }

                                snackbarController.addSnackbar({
                                    id: 'risk-archive-success',
                                    props: {
                                        title: t`Risk archived`,
                                        severity: 'success',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });

                                closeConfirmationModal();
                            },
                        );
                    },
                });
            }

            if (row.status !== 'CLOSED') {
                statusSubmenu.items.push({
                    id: 'close-row-action',
                    label: t`Close`,
                    onSelect: () => {
                        managementDataTableCellController.updateRiskAndUpdateDataTable(
                            {
                                status: 'CLOSED',
                            },
                        );

                        when(
                            () =>
                                !managementDataTableCellController.isMutationPending,
                            () => {
                                if (
                                    managementDataTableCellController.didMutationError
                                ) {
                                    return;
                                }

                                snackbarController.addSnackbar({
                                    id: 'risk-close-success',
                                    props: {
                                        title: t`Risk closed`,
                                        severity: 'success',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });

                                closeConfirmationModal();
                            },
                        );
                    },
                });
            }

            actions.push(statusSubmenu);

            if (sharedFeatureAccessModel.isReleaseClosedStatusForRiskEnabled) {
                actions.push({
                    id: 'delete-row-action',
                    label: t`Delete`,
                    colorScheme: 'critical',
                    onSelect: () => {
                        openConfirmationModal({
                            title: t`Are you sure?`,
                            body: t`You are about to delete 1 risk. This action will remove the risk data from your account permanently, and this action cannot be undone.`,
                            confirmText: t`Delete`,
                            cancelText: t`Cancel`,
                            type: 'danger',
                            onConfirm: () => {
                                sharedRiskManagementMutationController.deleteRisk(
                                    row.riskId,
                                );

                                when(
                                    () =>
                                        !sharedRiskManagementMutationController.isDeleteRiskPending,
                                    () => {
                                        if (
                                            sharedRiskManagementMutationController.hasError
                                        ) {
                                            return;
                                        }

                                        snackbarController.addSnackbar({
                                            id: 'risk-delete-success',
                                            props: {
                                                title: t`Risk deleted`,
                                                severity: 'success',
                                                closeButtonAriaLabel: t`Close`,
                                            },
                                        });

                                        managementDatatableController.invalidate();

                                        closeConfirmationModal();
                                    },
                                );
                            },
                            onCancel: closeConfirmationModal,
                            isLoading: () =>
                                sharedRiskManagementMutationController.isDeleteRiskPending,
                        });
                    },
                });
            }

            return actions as SchemaDropdownItemData[];
        }),
    };
}
