import { RiskCategoryCell } from '@components/risk-register';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { CompletionDateCell } from '../../components/completed-date-cell';
import { InherentImpactCell } from '../../components/inherent-impact-cell';
import { InherentLikelihoodCell } from '../../components/inherent-likelihood-cell';
import { InherentScoreCell } from '../../components/inherent-score-cell';
import { IsInternalCell } from '../../components/is-internal-cell';
import { MitigatingControlsCell } from '../../components/mitigating-controls-cell';
import { ResidualImpactCell } from '../../components/residual-impact-cell';
import { ResidualLikelihoodCell } from '../../components/residual-likelihood-cell';
import { ResidualScoreCell } from '../../components/residual-score-cell';
import { RiskDescriptionCell } from '../../components/risk-description-cell';
import { RiskIdCell } from '../../components/risk-id-cell';
import { RiskNameCell } from '../../components/risk-name-cell';
import { RiskOwnerCell } from '../../components/risk-owner-cell';
import { RiskReviewersCell } from '../../components/risk-reviewers-cell';
import { RiskStatusCell } from '../../components/risk-status-cell';
import { RiskTreatmentCell } from '../../components/risk-treatment-cell';
import { TargetedCompletionDateCell } from '../../components/targeted-completion-date-cell';

export function getColumns(): DatatableProps<RiskWithCustomFieldsResponseDto>['columns'] {
    return [
        {
            accessorKey: 'riskId',
            header: t`Risk ID`,
            id: 'ID',
            enableSorting: true,
            cell: RiskIdCell,
        },
        {
            accessorKey: 'title',
            header: t`Name`,
            id: 'NAME',
            enableSorting: true,
            cell: RiskNameCell,
            minSize: 200, // this value was found heuristically
        },
        {
            accessorKey: 'description',
            header: t`Description`,
            id: 'description',
            enableSorting: false,
            cell: RiskDescriptionCell,
            minSize: 250, // this value was found heuristically
        },
        {
            accessorKey: 'controls',
            header: t`Mitigating controls`,
            id: 'mitigating-ctrls',
            enableSorting: false,
            cell: MitigatingControlsCell,
            minSize: 200, // this value was found heuristically
        },
        {
            accessorKey: 'treatmentPlan',
            header: t`Treatment`,
            id: 'RISK_TREATMENT',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: RiskTreatmentCell,
        },
        {
            accessorKey: 'riskId',
            header: t`Inherent impact`,
            id: 'RISK_IMPACT',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: InherentImpactCell,
        },
        {
            accessorKey: 'riskId',
            header: t`Inherent likelihood`,
            id: 'RISK_LIKELIHOOD',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: InherentLikelihoodCell,
        },
        {
            accessorKey: 'score',
            header: t`Inherent score`,
            id: 'RISK_SCORE',
            enableSorting: true,
            cell: InherentScoreCell,
        },
        {
            header: t`Residual impact`,
            id: 'RISK_RESIDUAL_IMPACT',
            enableSorting: true,
            accessorFn: (row) => {
                return 'MITIGATE, TRANSFER'.includes(row.treatmentPlan)
                    ? 1
                    : null;
            },
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: ResidualImpactCell,
        },
        {
            accessorKey: 'riskId',
            header: t`Residual likelihood`,
            id: 'RISK_RESIDUAL_LIKELIHOOD',
            enableSorting: true,
            accessorFn: (row) => {
                return 'MITIGATE, TRANSFER'.includes(row.treatmentPlan)
                    ? 1
                    : null;
            },
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: ResidualLikelihoodCell,
        },
        {
            accessorKey: 'residualScore',
            header: t`Residual score`,
            id: 'RISK_RESIDUAL_SCORE',
            enableSorting: true,
            cell: ResidualScoreCell,
        },
        {
            accessorKey: 'owners',
            header: t`Owners`,
            id: 'owners',
            enableSorting: false,
            minSize: 220, // this value was found heuristically
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: RiskOwnerCell,
        },
        {
            accessorKey: 'type',
            header: t`Type`,
            id: 'RISK_TYPE',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: IsInternalCell,
        },
        {
            accessorKey: 'status',
            header: t`Status`,
            id: 'RISK_STATUS',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: RiskStatusCell,
        },
        {
            accessorKey: 'categories',
            header: t`Categories`,
            id: 'categories',
            enableSorting: false,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: RiskCategoryCell,
            minSize: 200, // this value was found heuristically
        },
        {
            accessorKey: 'anticipatedCompletionDate',
            header: t`Targeted completion date`,
            id: 'ANTICIPATED_COMPLETION_DATE',
            enableSorting: true,
            cell: TargetedCompletionDateCell,
            minSize: 200, // this value was found heuristically
        },
        {
            accessorKey: 'completionDate',
            header: t`Completed date`,
            id: 'RISK_COMPLETION_DATE',
            enableSorting: true,
            cell: CompletionDateCell,
        },
        {
            accessorKey: 'reviewers',
            header: t`Reviewers`,
            id: 'reviewers',
            enableSorting: false,
            cell: RiskReviewersCell,
            minSize: 220, // this value was found heuristically
        },
    ];
}
