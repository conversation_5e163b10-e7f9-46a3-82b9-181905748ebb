import { sharedConnectionsController } from '@controllers/connections';
import { sharedRiskManagementController } from '@controllers/risk';
import type { DatatableProps, FilterProps } from '@cosmos/components/datatable';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { managementDatatableController } from '../management-data-table.controller';

export function getFilterProps(): DatatableProps<RiskWithCustomFieldsResponseDto>['filterProps'] {
    const filterProps = {
        clearAllButtonLabel: t`Reset`,
        filters: [
            {
                filterType: 'checkbox',
                id: 'status[]',
                label: t`Risk status`,
                options: [
                    {
                        label: t`Active`,
                        value: 'ACTIVE',
                    },
                    {
                        label: t`Closed`,
                        value: 'CLOSED',
                    },
                    {
                        label: t`Archived`,
                        value: 'ARCHIVED',
                    },
                ],
            },
            {
                filterType: 'radio',
                id: 'isScored',
                label: t`Assessment`,
                options: [
                    {
                        label: t`Not scored`,
                        value: 'false',
                    },
                    {
                        label: t`Scored`,
                        value: 'true',
                    },
                ],
            },
            {
                filterType: 'checkbox',
                id: 'treatmentPlans[]',
                label: t`Treatment`,
                options: [
                    {
                        label: t`Accept`,
                        value: 'ACCEPT',
                    },
                    {
                        label: t`Mitigate`,
                        value: 'MITIGATE',
                    },
                    {
                        label: t`Avoid`,
                        value: 'AVOID',
                    },
                    {
                        label: t`Transfer`,
                        value: 'TRANSFER',
                    },
                    {
                        label: t`Untreated`,
                        value: 'UNTREATED',
                    },
                ],
            },
            {
                filterType: 'radio',
                id: 'riskFilter',
                label: t`Risks`,
                options: [
                    {
                        label: t`Needs attention`,
                        value: 'NEEDS_ATTENTION',
                    },
                    {
                        label: t`Custom risks`,
                        value: 'CUSTOM_ONLY',
                    },
                    ...(sharedFeatureAccessModel.isVendorRiskManagementProEnabled
                        ? [
                              {
                                  label: t`Internal risks`,
                                  value: 'INTERNAL_ONLY',
                              },
                              {
                                  label: t`External risks`,
                                  value: 'EXTERNAL_ONLY',
                              },
                          ]
                        : []),
                ],
            },
            {
                filterType: 'radio',
                id: 'hasTicketsDone',
                label: t`Tickets`,
                options: [
                    {
                        label: t`In progress`,
                        value: 'false',
                    },
                    {
                        label: t`Done`,
                        value: 'true',
                    },
                ],
            },
            {
                filterType: 'radio',
                id: 'isOwned',
                label: t`Risk owners`,
                options: [
                    {
                        label: t`Owners assigned`,
                        value: 'true',
                    },
                    {
                        label: t`No owners assigned`,
                        value: 'false',
                    },
                ],
            },
            {
                isMultiSelect: true,
                filterType: 'combobox',
                clearSelectedItemButtonLabel: t`Remove all`,
                id: 'ownersIds',
                label: t`Owners`,
                placeholder: 'Search',
                onFetchOptions:
                    managementDatatableController.loadOwnersIdsOptions,
                options: managementDatatableController.ownersIdsOptions,
                hasMore: sharedRiskManagementController.risksOwnersHasNextPage,
            },
            {
                isMultiSelect: true,
                filterType: 'combobox',
                id: 'categoriesIds',
                label: t`Categories`,
                placeholder: 'Search',
                onFetchOptions:
                    managementDatatableController.loadCategoriesIdsOptions,
                options: managementDatatableController.categoriesIdsOptions,
                hasMore:
                    sharedRiskManagementController.riskCategoriesHasNextPage,
            },
        ],
    } as const satisfies FilterProps;

    const filtersToExclude = new Set<string>();

    if (
        !sharedConnectionsController.hasTicketingConnectionWithWriteAccess ||
        !sharedFeatureAccessModel.isRiskManagementEnabled
    ) {
        filtersToExclude.add('hasTicketsDone');
    }

    if (sharedFeatureAccessModel.isRiskManagerWithRestrictedView) {
        filtersToExclude.add('isOwned');
        filtersToExclude.add('ownersIds');
    }

    return {
        ...filterProps,
        filters: filterProps.filters.filter(
            (filter) => !filtersToExclude.has(filter.id),
        ),
    };
}
