import {
    FlatfileEntityEnum,
    sharedFlatfileController,
} from '@controllers/flatfile';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { sharedConfigController } from '@globals/config';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

export const FirstUseEmptyState = observer((): JSX.Element => {
    const navigate = useNavigate();

    if (sharedFeatureAccessModel.isRiskManagementEnabled) {
        return (
            <EmptyState
                title={t`Stay on top of your organization's risk posture with risk assessment`}
                description={t`Evaluate potential security risks by analyzing their impact and assigning scores based on their severity. This process helps you understand your vulnerabilities and ensures you can proactively manage any potential threats. `}
                illustrationName="AddCircle"
                imageSize="md"
                data-testid="FirstUseEmptyState"
                data-id="Ryj36TpX"
                leftAction={
                    sharedFeatureAccessModel.isReleaseBulkImportRiskEnabled ? (
                        <SchemaDropdown
                            label={t`Create risk`}
                            level="primary"
                            endIconName="ChevronDown"
                            items={[
                                {
                                    id: 'create-risk',
                                    label: t`Create a single risk`,
                                    type: 'item',
                                    onClick: () => {
                                        runInAction(() => {
                                            sharedProgrammaticNavigationController.navigateTo(
                                                `${routeController.userPartOfUrl}/risk/management/registers/1/create-risk`,
                                            );
                                        });
                                    },
                                },
                                {
                                    id: 'import-risks',
                                    label: t`Create/update risks in bulk`,
                                    type: 'item',
                                    onClick: () => {
                                        runInAction(() => {
                                            sharedFlatfileController.createSpace(
                                                {
                                                    entityType:
                                                        FlatfileEntityEnum.RISK,
                                                },
                                            );
                                        });
                                    },
                                },
                            ]}
                        />
                    ) : (
                        <Button
                            label={t`Create risk`}
                            level="primary"
                            onClick={() => {
                                runInAction(() => {
                                    sharedProgrammaticNavigationController.navigateTo(
                                        `${routeController.userPartOfUrl}/risk/management/registers/1/create-risk`,
                                    );
                                });
                            }}
                        />
                    )
                }
                rightAction={
                    <AppLink
                        isExternal
                        href={`${sharedConfigController.configs.url?.help}/en/collections/10510591-risk-management`}
                    >
                        {t`Learn more about risk management`}
                    </AppLink>
                }
            />
        );
    }

    return (
        <EmptyState
            title={t`Stay on top of your organization's risk posture with risk assessment`}
            description={t`Get started building your risk register by adding a risk from our pre-populated Risk Library, adding a custom risk or with our get started guide.`}
            illustrationName="AddCircle"
            imageSize="md"
            data-testid="FirstUseEmptyState"
            data-id="Ryj36TpX"
            leftAction={
                <Button
                    label={t`Help me build my register`}
                    level="primary"
                    onClick={() => {
                        navigate(
                            `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks/onboarding/build-risk-register`,
                        );
                    }}
                />
            }
            rightAction={
                <Button
                    label={t`Teach me about risk management`}
                    level="tertiary"
                    onClick={() => {
                        navigate(
                            `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks/onboarding/learn-about-risk-management`,
                        );
                    }}
                />
            }
        />
    );
});
