import { useMemo } from 'react';
import { Loader } from '@cosmos/components/loader';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ManagementDataTableCellController } from '../controllers/management-data-table-cell.controller';

export const RiskTreatmentCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const managementDataTableCellController = useMemo(
            () => new ManagementDataTableCellController(original),
            [original],
        );

        if (managementDataTableCellController.isInstanceMutationPending) {
            return <Loader isSpinnerOnly label={t`Updating risk treatment`} />;
        }

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label={t`Treatment`}
                name="treatment"
                options={managementDataTableCellController.treatmentOptions}
                data-id="Vt1QF_Xa"
                data-testid="RiskTreatmentCell"
                isLoading={
                    managementDataTableCellController.isInstanceMutationPending
                }
                value={
                    managementDataTableCellController.treatmentSelectedOption
                }
                onChange={
                    managementDataTableCellController.handleTreatmentChange
                }
            />
        );
    },
);
