import { useMemo } from 'react';
import { Loader } from '@cosmos/components/loader';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ManagementDataTableCellController } from '../controllers/management-data-table-cell.controller';

export const ResidualLikelihoodCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const {
            residualLikelihoodOptions,
            residualLikelihoodValue,
            handleResidualLikelihoodChange,
            isInstanceMutationPending,
        } = useMemo(
            () => new ManagementDataTableCellController(original),
            [original],
        );

        if (isInstanceMutationPending) {
            return (
                <Loader
                    isSpinnerOnly
                    label={t`Updating risk residual likelihood score`}
                />
            );
        }

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label={t`Residual likelihood`}
                loaderLabel={t`Loading`}
                name="residualLikelihood"
                options={residualLikelihoodOptions}
                data-id="xGVrofI2"
                data-testid="ResidualLikelihoodCell"
                value={residualLikelihoodValue}
                onChange={handleResidualLikelihoodChange}
            />
        );
    },
);
