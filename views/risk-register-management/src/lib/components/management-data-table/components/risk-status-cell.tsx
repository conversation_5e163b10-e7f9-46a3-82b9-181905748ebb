import { useMemo } from 'react';
import { Loader } from '@cosmos/components/loader';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ManagementDataTableCellController } from '../controllers/management-data-table-cell.controller';

export const RiskStatusCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const managementDataTableCellController = useMemo(
            () => new ManagementDataTableCellController(original),
            [original],
        );

        if (managementDataTableCellController.isInstanceMutationPending) {
            return <Loader isSpinnerOnly label={t`Updating risk status`} />;
        }

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-status-form"
                label={t`Status`}
                name="status"
                data-testid="RiskStatusCell"
                data-id="Qow4Brqr"
                options={managementDataTableCellController.statusOptions}
                value={managementDataTableCellController.statusSelectedOption}
                isLoading={
                    managementDataTableCellController.isInstanceMutationPending
                }
                onChange={managementDataTableCellController.handleStatusChange}
            />
        );
    },
);
