import { useMemo } from 'react';
import { Loader } from '@cosmos/components/loader';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ManagementDataTableCellController } from '../controllers/management-data-table-cell.controller';

export const InherentLikelihoodCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const {
            inherentLikelihoodOptions,
            inherentLikelihoodValue,
            handleInherentLikelihoodChange,
            isInstanceMutationPending,
        } = useMemo(
            () => new ManagementDataTableCellController(original),
            [original],
        );

        if (isInstanceMutationPending) {
            return (
                <Loader
                    isSpinnerOnly
                    label={t`Updating risk inherent likelihood score`}
                />
            );
        }

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label={t`Inherent likelihood`}
                loaderLabel={t`Loading`}
                name="inherentLikelihood"
                options={inherentLikelihoodOptions}
                data-id="UNTsewHI"
                data-testid="InherentLikelihoodCell"
                value={inherentLikelihoodValue}
                onChange={handleInherentLikelihoodChange}
            />
        );
    },
);
