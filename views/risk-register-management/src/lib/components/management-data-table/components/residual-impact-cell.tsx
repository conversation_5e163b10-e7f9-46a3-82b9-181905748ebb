import { useMemo } from 'react';
import { Loader } from '@cosmos/components/loader';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ManagementDataTableCellController } from '../controllers/management-data-table-cell.controller';

export const ResidualImpactCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const {
            residualImpactOptions,
            residualImpactValue,
            handleResidualImpactChange,
            isInstanceMutationPending,
        } = useMemo(
            () => new ManagementDataTableCellController(original),
            [original],
        );

        if (isInstanceMutationPending) {
            return (
                <Loader
                    isSpinnerOnly
                    label={t`Updating risk residual impact score`}
                />
            );
        }

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label={t`Residual impact`}
                loaderLabel={t`Loading`}
                name="residualImpact"
                options={residualImpactOptions}
                data-id="eZVBfzUS"
                data-testid="ResidualImpactCell"
                value={residualImpactValue}
                onChange={handleResidualImpactChange}
            />
        );
    },
);
