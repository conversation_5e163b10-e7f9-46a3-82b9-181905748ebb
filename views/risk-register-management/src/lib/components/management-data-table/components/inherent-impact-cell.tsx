import { useMemo } from 'react';
import { Loader } from '@cosmos/components/loader';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ManagementDataTableCellController } from '../controllers/management-data-table-cell.controller';

export const InherentImpactCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const {
            inherentImpactOptions,
            inherentImpactValue,
            handleInherentImpactChange,
            isInstanceMutationPending,
        } = useMemo(
            () => new ManagementDataTableCellController(original),
            [original],
        );

        if (isInstanceMutationPending) {
            return (
                <Loader
                    isSpinnerOnly
                    label={t`Updating risk inherent impact score`}
                />
            );
        }

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label={t`Inherent impact`}
                loaderLabel={t`Loading`}
                name="inherentImpact"
                options={inherentImpactOptions}
                data-id="4yaKWuvy"
                data-testid="InherentImpactCell"
                value={inherentImpactValue}
                isLoading={isInstanceMutationPending}
                onChange={handleInherentImpactChange}
            />
        );
    },
);
