import { isNil, noop } from 'lodash-es';
import { useCallback } from 'react';
import { sharedUserSettingsController } from '@controllers/settings-user';
import { Box } from '@cosmos/components/box';
import { SelectField } from '@cosmos/components/select-field';
import { ToggleField } from '@cosmos/components/toggle-field';
import { getNotificationSettingFrequencyOptions } from '../helpers/get-notification-setting-frequency-options.helper';
import { getNotificationSettingLabel } from '../helpers/get-notification-setting-label.helper';
import type { NotificationSettingProps } from '../interfaces/notification-setting-props.interface';

const FORM_ID_PREFIX = 'setting-notification-form';

export const NotificationSetting = ({
    setting,
    hasFrequencyOptions,
}: NotificationSettingProps): React.JSX.Element | null => {
    const frequencyOptions = getNotificationSettingFrequencyOptions(
        setting.type,
    );
    const { isUpdating } = sharedUserSettingsController;

    const handleNotificationToggle = useCallback(
        (isEnabled: boolean) => {
            sharedUserSettingsController.updateFeatureSetting({
                featureId: setting.id,
                featureType: setting.type,
                isEnabled,
            });
        },
        [setting.id, setting.type],
    );

    return (
        <>
            <ToggleField
                label={getNotificationSettingLabel(setting.type)}
                checked={!isNil(setting.enabledAt)}
                name={`isNotificationEnabled-${setting.type}`}
                disabled={isUpdating}
                formId={`${FORM_ID_PREFIX}-${setting.id}`}
                data-id={`tgl-notification-${setting.id}`}
                data-testid="NotificationSettingToggleNotification"
                layout="input-left"
                onChange={handleNotificationToggle}
            />
            {hasFrequencyOptions && !isNil(setting.enabledAt) && (
                <Box pt="1x" pl="12x">
                    <SelectField
                        label="Frequency"
                        name={`notificationFrequency-${setting.type}`}
                        formId={`${FORM_ID_PREFIX}-${setting.id}`}
                        data-id={`sel-notification-${setting.id}`}
                        data-testid="NotificationSettingNotificationFrequency"
                        loaderLabel="Loading"
                        options={frequencyOptions}
                        value={
                            frequencyOptions.find(
                                (option) =>
                                    option.value ===
                                    Number(setting.value).toString(),
                            ) ?? frequencyOptions[0]
                        }
                        onChange={noop}
                    />
                </Box>
            )}
        </>
    );
};
