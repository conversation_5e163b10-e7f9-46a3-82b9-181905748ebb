import { sharedVendorsInsightsController } from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ErrorCardComponent } from './error-card-component';
import { NameTrendCellComponent } from './name-trend-cell-component';
import { TitleTrendComponent } from './title-trend-component';
import { ValueTrendCellComponent } from './value-trend-cell-component';

export const BusinessUnitsComponent = observer((): React.JSX.Element => {
    const { vendorStatsData, isLoading, wasBusinessUnitsError } =
        sharedVendorsInsightsController;

    if (isLoading) {
        return <Loader label="Loading..." />;
    }

    return (
        <>
            {wasBusinessUnitsError ? (
                <ErrorCardComponent title={t`Business units`} />
            ) : (
                <Card
                    isRaised
                    title={t`Business units`}
                    body={
                        <Stack direction="column" gap="xl">
                            <TitleTrendComponent
                                title="Total vendors"
                                trendsTotal={
                                    vendorStatsData?.businessUnitsTotal ?? 0
                                }
                            />
                            <Divider />
                            <StackedList data-id="business-units-stackedlist">
                                {(vendorStatsData?.businessUnits ?? []).map(
                                    (item) => (
                                        <StackedListItem
                                            key={item.name}
                                            data-id={`business-units-item-${item.name}`}
                                            primaryColumn={
                                                <NameTrendCellComponent
                                                    cellData={item.name}
                                                />
                                            }
                                            secondaryColumn={
                                                <ValueTrendCellComponent
                                                    cellData={item.value}
                                                />
                                            }
                                        />
                                    ),
                                )}
                            </StackedList>
                        </Stack>
                    }
                />
            )}
        </>
    );
});
