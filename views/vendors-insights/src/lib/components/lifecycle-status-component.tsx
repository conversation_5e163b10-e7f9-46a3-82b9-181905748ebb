import { sharedVendorsInsightsController } from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ErrorCardComponent } from './error-card-component';
import { NameTrendCellComponent } from './name-trend-cell-component';
import { TitleTrendComponent } from './title-trend-component';
import { ValueTrendCellComponent } from './value-trend-cell-component';

export const LifecycleStatusComponent = observer((): React.JSX.Element => {
    const { vendorStatsData, isLoading, wasLifecycleStatusError } =
        sharedVendorsInsightsController;

    if (isLoading) {
        return <Loader label="Loading..." />;
    }

    return (
        <>
            {wasLifecycleStatusError ? (
                <ErrorCardComponent title={t`Lifecycle status`} />
            ) : (
                <Card
                    isRaised
                    title={t`Lifecycle status`}
                    body={
                        <Stack direction="column" gap="xl">
                            <TitleTrendComponent
                                title={t`Total vendors`}
                                trendsTotal={
                                    vendorStatsData?.lifecycleStatusTotal ?? 0
                                }
                            />
                            <Divider />
                            <StackedList data-id="lifecycle-status-stackedlist">
                                {(vendorStatsData?.lifecycleStatus ?? []).map(
                                    (item) => (
                                        <StackedListItem
                                            key={item.name}
                                            data-id={`lifecycle-status-item-${item.name}`}
                                            primaryColumn={
                                                <NameTrendCellComponent
                                                    cellData={item.name}
                                                />
                                            }
                                            secondaryColumn={
                                                <ValueTrendCellComponent
                                                    cellData={item.value}
                                                />
                                            }
                                        />
                                    ),
                                )}
                            </StackedList>
                        </Stack>
                    }
                />
            )}
        </>
    );
});
