import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import {
    SchemaDropdown,
    type SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import type { CustomerRequestEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const OptionsTableActionCell = ({
    row,
    isFromAuditHub = true,
}: {
    row: { original: CustomerRequestEvidenceResponseDto };
    isFromAuditHub?: boolean;
}): React.JSX.Element => {
    const { id } = row.original;

    const items: SchemaDropdownItems = [];

    if (!isFromAuditHub) {
        items.push({
            id: 'add-group',
            type: 'group',
            items: [
                {
                    id: 'add-new-evidence-option',
                    label: t`Add new evidence`,
                    type: 'item',
                    onClick: () => {
                        // TODO: https://drata.atlassian.net/browse/ENG-73387
                    },
                },
                {
                    id: 'add-from-evidence-library-option',
                    label: t`Add from evidence library`,
                    type: 'item',
                    onClick: () => {
                        // TODO: https://drata.atlassian.net/browse/ENG-73389
                    },
                },
                {
                    id: 'add-policy-option',
                    label: t`Add policy`,
                    type: 'item',
                    onClick: () => {
                        // TODO: https://drata.atlassian.net/browse/ENG-73394
                    },
                },
                {
                    id: 'add-test-option',
                    label: t`Add test`,
                    type: 'item',
                    onClick: () => {
                        // TODO: https://drata.atlassian.net/browse/ENG-73447
                    },
                },
            ],
        });
    }

    items.push({
        id: 'download-group',
        type: 'group',
        items: [
            {
                id: 'download-evidence-option',
                label: t`Download`,
                type: 'item',
                onClick: (event: MouseEvent) => {
                    event.stopPropagation(); // Prevent row click event
                    sharedCustomerRequestDetailsController.generateRequestControlEvidencePackage(
                        { selectedControlIds: [Number(id)] },
                    );
                },
            },
        ],
    });

    return (
        <SchemaDropdown
            isIconOnly
            key={id}
            size="sm"
            startIconName="HorizontalMenu"
            level="tertiary"
            label={t`Horizontal menu`}
            colorScheme="neutral"
            data-id="6GvBWQ_R"
            data-testid="OptionsTableActionCell"
            items={items}
        />
    );
};
