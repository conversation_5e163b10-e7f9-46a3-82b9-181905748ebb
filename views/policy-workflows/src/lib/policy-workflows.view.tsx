import { isNil } from 'lodash-es';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { openPoliciesBuilderEditApprovalSettingsModal } from '@views/policies-builder-overview';
import { PolicyApprovalWorkflowsContent } from './components/policy-approval-workflows-content.component';
import { policyApprovalWorkflowsController } from './controllers/policy-approval-workflows.controller';

export const PolicyWorkflowsView = observer((): React.JSX.Element => {
    const { reviewGroups, isLoading } = policyApprovalWorkflowsController;
    const { shouldShowEditApprovalSettingsButton, latestPolicyVersion } =
        sharedPolicyBuilderModel;

    if (isNil(latestPolicyVersion) && !isLoading) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    return (
        <Box data-testid="PolicyWorkflowsView" data-id="policy-workflows-view">
            <Card
                title={t`Approval`}
                body={
                    <PolicyApprovalWorkflowsContent
                        reviewGroups={reviewGroups}
                        isLoading={isLoading}
                    />
                }
                actions={
                    shouldShowEditApprovalSettingsButton
                        ? [
                              {
                                  actionType: 'button',
                                  id: 'button-action-skeleton',
                                  typeProps: {
                                      label: t`Edit approval settings`,
                                      level: 'secondary',
                                      onClick:
                                          openPoliciesBuilderEditApprovalSettingsModal,
                                  },
                              },
                          ]
                        : []
                }
            />
        </Box>
    );
});
