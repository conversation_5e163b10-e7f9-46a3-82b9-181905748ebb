import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { PolicyApprovalReviewGroupResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { PolicyApprovalReviewer } from './policy-approval-reviewer.component';

export const PolicyApprovalTierContent = observer(
    ({
        reviewGroup,
    }: {
        reviewGroup: PolicyApprovalReviewGroupResponseDto;
    }): React.JSX.Element => {
        const { reviews, consensusRule, consensusReached } = reviewGroup;

        return (
            <Stack
                direction="column"
                gap="2xl"
                data-testid="PolicyApprovalTierContent"
                data-id="tier-content"
            >
                <Stack direction="column" gap="2x">
                    {reviews.map((review) => (
                        <PolicyApprovalReviewer
                            key={review.userId}
                            review={review}
                            consensusReached={consensusReached}
                            consensusRule={consensusRule}
                            isOverridden={review.isOverridden}
                            data-id="5uuyED3T"
                        />
                    ))}
                </Stack>

                {consensusRule === 'ANY' && (
                    <Text size="100" align="right">
                        * {t`One approval required`}
                    </Text>
                )}
            </Stack>
        );
    },
);
