import { Modal } from '@cosmos/components/modal';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { closePoliciesBuilderEditApprovalSettingsModal } from '../helpers/open-policies-builder-edit-approval-settings-modal.helper';

export const PoliciesBuilderEditApprovalSettingsModalSkeleton =
    (): React.JSX.Element => {
        return (
            <>
                <Modal.Header
                    title={t`Approval Settings`}
                    closeButtonAriaLabel={t`Close approval settings modal`}
                    data-id="approval-settings-modal-header"
                    onClose={closePoliciesBuilderEditApprovalSettingsModal}
                />

                <Modal.Body data-id="approval-settings-modal-body">
                    <Stack gap="lg" direction="column">
                        {/* Banner skeleton */}
                        <Skeleton
                            barCount={1}
                            width="100%"
                            barHeight="48px"
                            data-id="banner-skeleton"
                        />

                        {/* Form title/description skeleton */}
                        <Skeleton
                            barCount={2}
                            width="85%"
                            data-id="form-description-skeleton"
                        />

                        {/* Approval tiers skeleton */}
                        <Stack gap="xl" direction="column">
                            {/* First tier */}
                            <Stack gap="lg" direction="column">
                                {/* Approvers field */}
                                <Stack gap="sm" direction="column">
                                    <Skeleton
                                        barCount={1}
                                        width="80px"
                                        barHeight="16px"
                                        data-id="tier-1-approvers-label-skeleton"
                                    />
                                    <Skeleton
                                        barCount={1}
                                        width="100%"
                                        barHeight="40px"
                                        data-id="tier-1-approvers-input-skeleton"
                                    />
                                </Stack>

                                {/* Approval level field */}
                                <Stack gap="sm" direction="column">
                                    <Skeleton
                                        barCount={1}
                                        width="100px"
                                        barHeight="16px"
                                        data-id="tier-1-approval-level-label-skeleton"
                                    />
                                    <Skeleton
                                        barCount={1}
                                        width="200px"
                                        barHeight="40px"
                                        data-id="tier-1-approval-level-input-skeleton"
                                    />
                                </Stack>

                                {/* Days to approve field */}
                                <Stack gap="sm" direction="column" width="40%">
                                    <Skeleton
                                        barCount={1}
                                        width="120px"
                                        barHeight="16px"
                                        data-id="tier-1-days-label-skeleton"
                                    />
                                    <Skeleton
                                        barCount={1}
                                        width="100%"
                                        barHeight="40px"
                                        data-id="tier-1-days-input-skeleton"
                                    />
                                </Stack>
                            </Stack>

                            {/* Divider */}
                            <Skeleton
                                barCount={1}
                                width="100%"
                                barHeight="1px"
                                data-id="divider-skeleton"
                            />

                            {/* Add tier button */}
                            <Skeleton
                                barCount={1}
                                width="100px"
                                barHeight="36px"
                                data-id="add-tier-button-skeleton"
                            />
                        </Stack>
                    </Stack>
                </Modal.Body>

                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick:
                                closePoliciesBuilderEditApprovalSettingsModal,
                            'data-id': 'cancel-approval-settings',
                            cosmosUseWithCaution_isDisabled: true,
                        },
                        {
                            label: t`Save Settings`,
                            level: 'primary',
                            onClick:
                                closePoliciesBuilderEditApprovalSettingsModal,
                            'data-id': 'save-approval-settings',
                            cosmosUseWithCaution_isDisabled: true,
                        },
                    ]}
                />
            </>
        );
    };
