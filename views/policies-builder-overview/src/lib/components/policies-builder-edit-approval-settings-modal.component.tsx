import { useEffect } from 'react';
import { Banner } from '@cosmos/components/banner';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { Form, useFormSubmit } from '@ui/forms';
import { sharedPoliciesBuilderEditApprovalSettingsController } from '../controllers/policies-builder-edit-approval-settings.controller';
import { closePoliciesBuilderEditApprovalSettingsModal } from '../helpers/open-policies-builder-edit-approval-settings-modal.helper';
import { PoliciesBuilderEditApprovalSettingsModalSkeleton } from './policies-builder-edit-approval-settings-modal-skeleton.component';

export const PoliciesBuilderEditApprovalSettingsModal = observer(() => {
    const { formRef, triggerSubmit } = useFormSubmit();

    useEffect(() => {
        runInAction(() => {
            sharedPoliciesBuilderEditApprovalSettingsController.loadWorkflowConfiguration();
        });
    }, []);

    const {
        isSaveApprovalSettingsLoading,
        isWorkflowConfigurationLoading,
        displayNextCycleWarningBanner,
        formSchema,
        saveApprovalSettings,
    } = sharedPoliciesBuilderEditApprovalSettingsController;

    const handleClose = () => {
        if (isSaveApprovalSettingsLoading) {
            return;
        }
        closePoliciesBuilderEditApprovalSettingsModal();
    };

    if (isWorkflowConfigurationLoading) {
        return <PoliciesBuilderEditApprovalSettingsModalSkeleton />;
    }

    return (
        <>
            <Modal.Header
                title={t`Approval Settings`}
                closeButtonAriaLabel={t`Close approval settings modal`}
                data-id="approval-settings-modal-header"
                onClose={handleClose}
            />

            <Modal.Body data-id="approval-settings-modal-body">
                <Stack gap="lg" direction="column">
                    {displayNextCycleWarningBanner && (
                        <Banner
                            title={t`The current policy version is in or has completed an approval cycle; changes will apply in the next cycle.`}
                            severity="warning"
                            data-id="next-cycle-warning-banner"
                        />
                    )}

                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="approval-settings-form"
                        data-id="approval-settings-form"
                        schema={formSchema}
                        onSubmit={saveApprovalSettings}
                    />
                </Stack>
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: handleClose,
                        'data-id': 'cancel-approval-settings',
                        cosmosUseWithCaution_isDisabled:
                            isSaveApprovalSettingsLoading,
                    },
                    {
                        label: t`Save`,
                        level: 'primary',
                        onClick: triggerSubmit,
                        'data-id': 'save-approval-settings',
                        isLoading: isSaveApprovalSettingsLoading,
                    },
                ]}
            />
        </>
    );
});
