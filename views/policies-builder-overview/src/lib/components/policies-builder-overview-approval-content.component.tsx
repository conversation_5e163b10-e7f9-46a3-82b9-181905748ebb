import { isEmpty } from 'lodash-es';
import { PoliciesApprovalEmptyStateComponent } from '@components/policies';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { shouldShowNoApprovalMessage } from '../helpers/approval-display.helpers';
import { getApprovalStatusData } from '../helpers/get-approval-status-data.helper';
import { isReviewGroupPastDue } from '../helpers/review-group-status.helper';
import { PoliciesBuilderOverviewApprovalStatusBadge } from './policies-builder-overview-approval-status-badge.component';
import { PolicyApprovalAccordion } from './policy-approval-accordion.component';
import { PolicyApprovalChangeRequests } from './policy-approval-change-requests.component';
import { PolicyApprovalDeadline } from './policy-approval-deadline.component';
import { PolicyApprovalTierContent } from './policy-approval-tier-content.component';
import { PolicyNoApprovalRequired } from './policy-no-approval-required.component';

export const PoliciesBuilderOverviewApprovalContent = observer(
    (): React.JSX.Element => {
        const {
            needsApprovalConfiguration,
            currentApproval,
            hasMultipleTiers,
            firstReviewGroup,
            isApprovedOrPublished,
            clientType,
            approvedAt,
            activeReviewGroup,
            isApprovalReadyForReview,
            shouldShowChangeRequests,
            changeRequests,
        } = sharedPolicyBuilderModel;

        if (shouldShowNoApprovalMessage(isApprovedOrPublished, approvedAt)) {
            return <PolicyNoApprovalRequired clientType={clientType} />;
        }

        if (needsApprovalConfiguration || !currentApproval) {
            return <PoliciesApprovalEmptyStateComponent />;
        }

        const { hasChangeRequests, isPastDue, hasReviewersPending } =
            getApprovalStatusData(currentApproval);

        return (
            <Stack
                direction="column"
                gap="xl"
                data-testid="PoliciesBuilderOverviewApprovalContent"
                data-id="approval-content"
            >
                <PoliciesBuilderOverviewApprovalStatusBadge
                    approvalStatus={currentApproval.status}
                    hasChangeRequests={hasChangeRequests}
                    isPastDue={isPastDue}
                    hasReviewersPending={hasReviewersPending}
                />

                {hasMultipleTiers
                    ? currentApproval.reviewGroups.map((reviewGroup) => (
                          <PolicyApprovalAccordion
                              key={`accordion-tier-${reviewGroup.tier}-${reviewGroup.id}`}
                              reviewGroup={reviewGroup}
                              data-id="1jJYfGDB"
                          />
                      ))
                    : firstReviewGroup && (
                          <PolicyApprovalTierContent
                              reviewGroup={firstReviewGroup}
                          />
                      )}

                {activeReviewGroup &&
                    !isEmpty(activeReviewGroup.deadline) &&
                    isApprovalReadyForReview && (
                        <PolicyApprovalDeadline
                            deadline={activeReviewGroup.deadline}
                            isPastDue={isReviewGroupPastDue(
                                activeReviewGroup.consensusDecision,
                            )}
                            {...(hasMultipleTiers && {
                                reviewGroupName: activeReviewGroup.name,
                            })}
                        />
                    )}

                {shouldShowChangeRequests && (
                    <PolicyApprovalChangeRequests
                        changeRequests={changeRequests}
                    />
                )}
            </Stack>
        );
    },
);
