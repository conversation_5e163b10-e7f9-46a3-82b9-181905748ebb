import { isObject } from 'lodash-es';
import { useMemo } from 'react';
import { useFieldArray, useWatch } from 'react-hook-form';
import { Box } from '@cosmos/components/box';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { AppButton } from '@ui/app-button';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import { POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_APPROVERS_PER_TIER } from '../constants/policies-builder-approvals-settings-max-approvers-per-tier';
import { POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_TIER_COUNT } from '../constants/policies-builder-approvals-settings-max-tier-count';

export const PoliciesBuilderApprovalsGroupField = ({
    name,
    formId,
    'data-id': dataId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { fields, append, remove } = useFieldArray({
        name,
    });
    const approvalGroupsField = useWatch({
        name,
    });

    const hasReachedMaxApprovers = useMemo(() => {
        if (!Array.isArray(approvalGroupsField)) {
            return false;
        }

        return approvalGroupsField.some((field) => {
            if (!isObject(field) || !('approvers' in field)) {
                return false;
            }
            const { approvers } = field;

            return (
                Array.isArray(approvers) &&
                approvers.length >
                    POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_APPROVERS_PER_TIER
            );
        });
    }, [approvalGroupsField]);

    const hasMoreThanOneTier = fields.length > 1;
    const hasReachedMaxTiers =
        fields.length > POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_TIER_COUNT;

    const addReviewGroup = () => {
        if (hasReachedMaxTiers) {
            return;
        }

        append({
            approvers: [],
            approvalLevel: 'ALL',
            daysToApprove: '14',
            tierName: `Tier ${fields.length + 1}`,
        });
    };

    const removeReviewGroup = (index: number) => {
        remove(index);
    };

    return (
        <Stack
            gap="lg"
            direction="column"
            data-id={dataId}
            data-testid="PoliciesBuilderApprovalsGroupField"
        >
            {fields.map((field, index) => (
                <Stack
                    key={field.id}
                    gap="lg"
                    direction="column"
                    data-id={`review-group-${index}`}
                >
                    {hasMoreThanOneTier && (
                        <UniversalFormField
                            formId={formId}
                            name={`${name}[${index}].tierName`}
                            data-id={`${dataId}-tier-name-${index}`}
                        />
                    )}

                    <UniversalFormField
                        formId={formId}
                        name={`${name}[${index}].approvers`}
                        data-id={`${dataId}-approvers-${index}`}
                    />

                    <UniversalFormField
                        formId={formId}
                        name={`${name}[${index}].approvalLevel`}
                        data-id={`${dataId}-approval-level-${index}`}
                    />

                    <Stack gap="md" align="end" width="40%">
                        <UniversalFormField
                            formId={formId}
                            name={`${name}[${index}].daysToApprove`}
                            data-id={`${dataId}-days-to-approve-${index}`}
                        />
                    </Stack>

                    {hasMoreThanOneTier && index > 0 && (
                        <Box>
                            <AppButton
                                label={t`Remove Tier`}
                                level="tertiary"
                                colorScheme="danger"
                                data-id={`remove-tier-${index}`}
                                onClick={() => {
                                    removeReviewGroup(index);
                                }}
                            />
                        </Box>
                    )}

                    <Divider />
                </Stack>
            ))}

            {hasReachedMaxTiers || hasReachedMaxApprovers ? (
                <>
                    {hasReachedMaxTiers && (
                        <Feedback
                            title={t`Maximum number of tiers reached`}
                            description={t`You’ve reached the limit of ${POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_TIER_COUNT} approval tiers for this setup. To proceed, please remove an existing tier before adding a new one.`}
                            severity="warning"
                        />
                    )}

                    {hasReachedMaxApprovers && (
                        <Feedback
                            title={t`Maximum number of approvers reached`}
                            description={t`You’ve reached the limit of ${POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_APPROVERS_PER_TIER} approvers in one tier. To proceed, please remove an existing approver before adding a new one.`}
                            severity="warning"
                        />
                    )}
                </>
            ) : (
                <Box>
                    <AppButton
                        label={t`Add Tier`}
                        level="secondary"
                        data-id="add-tier"
                        onClick={addReviewGroup}
                    />
                </Box>
            )}
        </Stack>
    );
};
