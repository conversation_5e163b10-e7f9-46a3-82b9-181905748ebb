import { modalController } from '@controllers/modal';
import { PoliciesBuilderEditApprovalSettingsModal } from '../components/policies-builder-edit-approval-settings-modal.component';

const POLICIES_BUILDER_EDIT_APPROVAL_SETTINGS_MODAL_ID =
    'policies-builder-edit-approval-settings-modal';

export const openPoliciesBuilderEditApprovalSettingsModal = (): void => {
    modalController.openModal({
        id: POLICIES_BUILDER_EDIT_APPROVAL_SETTINGS_MODAL_ID,
        content: () => (
            <PoliciesBuilderEditApprovalSettingsModal data-id="policies-builder-edit-approval-settings-modal" />
        ),
        centered: true,
        disableClickOutsideToClose: true,
        size: 'lg',
    });
};

export const closePoliciesBuilderEditApprovalSettingsModal = (): void => {
    modalController.closeModal(
        POLICIES_BUILDER_EDIT_APPROVAL_SETTINGS_MODAL_ID,
    );
};
