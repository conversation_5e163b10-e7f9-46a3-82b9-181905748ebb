import { isEmpty } from 'lodash-es';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { snackbarController } from '@controllers/snackbar';
import { sharedUsersInfiniteController } from '@controllers/users';
import type {
    ListBoxItemData,
    ListBoxItems,
} from '@cosmos/components/list-box';
import {
    policyApprovalsControllerCreateApprovalConfigurationMutation,
    policyApprovalsControllerGetConfigurationOptions,
} from '@globals/api-sdk/queries';
import type {
    PolicyApprovalConfigurationPaginatedResponseDto,
    PolicyApprovalReviewGroupsRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import type { FormSchema, FormValues } from '@ui/forms';
import { PoliciesBuilderApprovalsGroupField } from '../components/policies-builder-approvals-group-field.component';
import { POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_APPROVERS_PER_TIER } from '../constants/policies-builder-approvals-settings-max-approvers-per-tier';
import { closePoliciesBuilderEditApprovalSettingsModal } from '../helpers/open-policies-builder-edit-approval-settings-modal.helper';

class PoliciesBuilderEditApprovalSettingsController {
    constructor() {
        makeAutoObservable(this);
    }

    workflowConfigurationQuery = new ObservedQuery(
        policyApprovalsControllerGetConfigurationOptions,
    );

    saveApprovalConfigurationMutation = new ObservedMutation(
        policyApprovalsControllerCreateApprovalConfigurationMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'approval-settings-save-success',
                    props: {
                        title: t`Approval settings saved`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closePoliciesBuilderEditApprovalSettingsModal();
                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to save approval settings',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        action: 'saveApprovalConfiguration',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'approval-settings-save-error',
                    props: {
                        title: t`Failed to save approval settings`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    loadWorkflowConfiguration = (): void => {
        this.workflowConfigurationQuery.load({
            path: {
                policyId: sharedPolicyBuilderModel.policyId,
            },
            query: {
                latest: true,
            },
        });
        this.workflowConfigurationQuery.invalidate();
    };

    get workflowConfiguration(): PolicyApprovalConfigurationPaginatedResponseDto | null {
        return this.workflowConfigurationQuery.data ?? null;
    }

    get approversOptions(): ListBoxItems {
        return sharedUsersInfiniteController.usersList.map(
            this.mapUserToComboboxOption,
        );
    }

    get isWorkflowConfigurationLoading(): boolean {
        return this.workflowConfigurationQuery.isLoading;
    }

    get isSaveApprovalSettingsLoading(): boolean {
        return this.saveApprovalConfigurationMutation.isPending;
    }

    saveApprovalSettings = (formData: FormValues): void => {
        const typedFormData = formData as {
            reviewGroups: {
                tierName: string;
                approvers: { id: string; label: string; value: string }[];
                approvalLevel: PolicyApprovalReviewGroupsRequestDto['consensusRule'];
                daysToApprove: string;
            }[];
        };

        const isMultiTier = typedFormData.reviewGroups.length > 1;
        const reviewGroups = typedFormData.reviewGroups.map(
            (group, index) =>
                ({
                    consensusRule: group.approvalLevel,
                    userIds: group.approvers.map((approver) =>
                        parseInt(approver.id),
                    ),
                    timeline: `${group.daysToApprove}D`,
                    hierarchyLevel: index + 1,
                    name: isMultiTier ? group.tierName : '',
                }) as PolicyApprovalReviewGroupsRequestDto,
        );

        this.saveApprovalConfigurationMutation.mutate({
            path: {
                policyId: sharedPolicyBuilderModel.policyId,
            },
            body: {
                reviewGroups,
            },
        });
    };

    get displayNextCycleWarningBanner(): boolean {
        const { policyApprovals, currentStatus } = sharedPolicyBuilderModel;

        if (isEmpty(policyApprovals)) {
            return false;
        }

        const currentApproval = policyApprovals[0];

        const approvalStatus = currentApproval.status;
        const approvalDeadline = currentApproval.deadline;

        switch (true) {
            case approvalStatus === 'APPROVED': {
                return true;
            }
            case approvalStatus === 'CHANGES_REQUESTED': {
                return true;
            }
            case approvalDeadline !== null &&
                new Date() > new Date(approvalDeadline) &&
                approvalStatus === 'APPROVED': {
                return true;
            }
            case approvalStatus === 'READY_FOR_REVIEWS' &&
                currentStatus === 'NEEDS_APPROVAL': {
                return true;
            }
            default: {
                return false;
            }
        }
    }

    get initialValues(): FormValues[] {
        const configuration = this.workflowConfiguration;

        if (!configuration?.data[0]?.reviewGroups) {
            return [];
        }

        return configuration.data[0].reviewGroups.map((group) => {
            return {
                tierName: group.name || `Tier ${group.tier}`,
                approvers: group.userAssignments.map(
                    this.mapUserToComboboxOption,
                ),
                approvalLevel: group.consensusRule,
                daysToApprove: parseInt(group.timeline).toString(),
            };
        });
    }

    get formSchema(): FormSchema {
        return {
            reviewGroups: {
                type: 'custom',
                label: t`Approval Levels`,
                render: PoliciesBuilderApprovalsGroupField,
                customType: 'arrayOfObjects',
                initialValue: this.initialValues,
                fields: {
                    tierName: {
                        type: 'text',
                        label: t`Tier name`,
                    },
                    approvers: {
                        type: 'combobox',
                        label: t`Approvers`,
                        isMultiSelect: true,
                        loaderLabel: t`Loading approvers...`,
                        removeAllSelectedItemsLabel: t`Clear all`,
                        getSearchEmptyState: () => t`No approvers found`,
                        placeholder: t`Search by name`,
                        options: sharedUsersInfiniteController.options,
                        isLoading: sharedUsersInfiniteController.isLoading,
                        disableFetchOnMount: true,
                        hasMore: sharedUsersInfiniteController.hasNextPage,
                        maxAllowed:
                            POLICIES_BUILDER_APPROVALS_SETTINGS_MAX_APPROVERS_PER_TIER,
                        onFetchOptions:
                            sharedUsersInfiniteController.loadNextPage,
                    },
                    approvalLevel: {
                        type: 'radioGroup',
                        label: t`Level of approval`,
                        options: [
                            {
                                label: t`All approvers must approve`,
                                value: 'ALL',
                            },
                            {
                                label: t`Only one approver must approve`,
                                value: 'ANY',
                            },
                        ],
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                    daysToApprove: {
                        type: 'text',
                        label: t`Time to approve (days)`,
                        helpText: t`Sets how many days after approval start the tier must be completed.`,
                    },
                },
            },
        };
    }

    mapUserToComboboxOption = (user: {
        id: number;
        firstName: string;
        lastName: string;
    }): ListBoxItemData => ({
        id: user.id.toString(),
        label: getFullName(user.firstName, user.lastName),
        value: user.id.toString(),
    });
}

export const sharedPoliciesBuilderEditApprovalSettingsController =
    new PoliciesBuilderEditApprovalSettingsController();
