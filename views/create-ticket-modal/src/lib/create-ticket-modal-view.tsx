import { sharedCreateTicketController } from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { CreateTicketForm } from './components/create-ticket-form.component';
import { CREATE_TICKET_FORM_ID } from './constants/create-ticket.constants';

export const CreateTicketModalView = observer((): React.JSX.Element => {
    const onSubmit = action((e: React.FormEvent<HTMLFormElement>) => {
        sharedCreateTicketController.createTicket(e);
    });

    const onCancel = action(() => {
        sharedCreateTicketController.resetForm();
        modalController.closeModal('create-ticket-modal');
    });

    return (
        <>
            <Modal.Header title={t`Ticket Details`} />
            <Modal.Body>
                <form id={CREATE_TICKET_FORM_ID} onSubmit={onSubmit}>
                    <CreateTicketForm />
                </form>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        cosmosUseWithCaution_isDisabled:
                            sharedCreateTicketController.isCreating,
                        onClick: onCancel,
                    },
                    {
                        label: t`Create Ticket`,
                        form: CREATE_TICKET_FORM_ID,
                        level: 'primary',
                        type: 'submit',
                        isLoading: sharedCreateTicketController.isCreating,
                        // As requested by product we are disabling the submit button while loading any dropdown state
                        //  to prevent situations where the customer submits the form prematurely
                        cosmosUseWithCaution_isDisabled:
                            sharedCreateTicketController.isLoading,
                    },
                ]}
            />
        </>
    );
});
