import { useMemo } from 'react';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldTeamProps {
    onChange?: (team: ListBoxItemData) => void;
}

export const CreateTicketFieldTeam = observer(
    ({ onChange }: CreateTicketFieldTeamProps) => {
        const { isTeamsLoading, teams, selectedTeam, setTeam, invalidProps } =
            sharedCreateTicketController;

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            if (!invalidProps.includes('teamId')) {
                return undefined;
            }

            return {
                id: `create-ticket-field-team-feedback`,
                type: 'error',
                message: t`Team selection is required`,
            };
        }, [invalidProps]);

        return (
            <SelectField
                required
                data-id="create-ticket-field-team"
                key={`create-ticket-field-team-${selectedTeam?.id}`}
                formId={CREATE_TICKET_FORM_ID}
                name="team"
                label={t`Team`}
                helpText={t`Select a team to show its projects`}
                value={toJS(selectedTeam)}
                options={teams}
                isLoading={isTeamsLoading}
                feedback={feedback}
                onChange={onChange ?? setTeam}
            />
        );
    },
);
