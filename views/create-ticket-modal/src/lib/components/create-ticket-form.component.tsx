import { sharedCreateTicketController } from '@controllers/create-ticket';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { Provider } from '@globals/providers';
import { CreateTicketFieldConnection } from './create-ticket-field-connection.component';
import { CreateTicketFormAsana } from './create-ticket-form-asana.component';
import { CreateTicketFormAzure } from './create-ticket-form-azure.component';
import { CreateTicketFormClickup } from './create-ticket-form-clickup.component';
import { CreateTicketFormGeneric } from './create-ticket-form-generic.component';
import { CreateTicketFormGitlab } from './create-ticket-form-gitlab.component';
import { CreateTicketFormJira } from './create-ticket-form-jira.component';
import { CreateTicketFormLinear } from './create-ticket-form-linear.component';
import { CreateTicketFormServiceNow } from './create-ticket-form-service-now.component';

const ProviderForm = observer(({ providerId }: { providerId: Provider }) => {
    switch (providerId) {
        case 'ASANA': {
            return <CreateTicketFormAsana />;
        }
        case 'AZURE_BOARDS': {
            return <CreateTicketFormAzure />;
        }
        case 'CLICKUP': {
            return <CreateTicketFormClickup />;
        }
        case 'JIRA':
        case 'MERGEDEV_JIRA_DATA_CENTER': {
            return <CreateTicketFormJira />;
        }
        case 'GITLAB_ISSUES':
        case '*********************': {
            return <CreateTicketFormGitlab />;
        }
        case 'LINEAR': {
            return <CreateTicketFormLinear />;
        }
        case 'MERGEDEV_SERVICENOW': {
            return <CreateTicketFormServiceNow />;
        }
        default: {
            return <CreateTicketFormGeneric />;
        }
    }
});

export const CreateTicketForm = observer(() => {
    const { isConnectionsLoading, showConnectionField, providerId } =
        sharedCreateTicketController;

    return isConnectionsLoading ? (
        <Loader label={t`Loading...`} />
    ) : (
        <Stack direction="column" gap="xl" data-id="Ru1Q9OsZ">
            {showConnectionField && <CreateTicketFieldConnection />}
            {providerId && <ProviderForm providerId={providerId} />}
        </Stack>
    );
});
