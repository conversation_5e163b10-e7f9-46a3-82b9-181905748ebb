import { useMemo } from 'react';
import {
    type CreateTicketDynamic<PERSON>ield,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldComboboxProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldGenericSelect = observer(
    ({ field }: CreateTicketFieldComboboxProps): React.JSX.Element => {
        const { fields, setField, invalidDynamicFields } =
            sharedCreateTicketController;
        const fieldValue = toJS(fields[field.name] ?? {});

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            const { name, label } = field;

            if (!invalidDynamicFields.includes(name)) {
                return undefined;
            }

            return {
                id: `create-ticket-field-${name}-feedback`,
                type: 'error',
                message: t`${label} is required`,
            };
        }, [invalidDynamicFields, field]);

        return (
            <SelectField
                key={`create-ticket-field-${field.name}-${fieldValue.item?.id}`}
                data-id={`create-ticket-field-${field.name}`}
                name={field.name}
                label={field.label}
                formId={CREATE_TICKET_FORM_ID}
                value={fieldValue.item}
                required={field.required}
                optionalText={field.required ? undefined : t`Optional`}
                feedback={feedback}
                options={field.allowedValues?.map((option) => ({
                    id: option.value,
                    label: option.label,
                    value: option.value,
                }))}
                onChange={(item) => {
                    setField({
                        name: field.name,
                        type: field.type,
                        item,
                    });
                }}
            />
        );
    },
);
