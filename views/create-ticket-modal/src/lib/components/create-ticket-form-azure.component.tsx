import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { action, observer } from '@globals/mobx';
import { CreateTicketDynamicFields } from './create-ticket-dynamic-fields.component';
import { CreateTicketFieldProject } from './create-ticket-field-project.component';
import { CreateTicketFieldTicketType } from './create-ticket-field-ticket-type.component';
import { CreateTicketFieldWorkspace } from './create-ticket-field-workspace.component';

export const CreateTicketFormAzure = observer(() => {
    const {
        loadProjects,
        loadTicketTypes,
        formData,
        connectionId,
        selectedWorkspace,
        selectedProject,
        selectedTicketType,
        loadAdditionalFields,
    } = sharedCreateTicketController;

    const handleWorkspaceChange = action((workspace: ListBoxItemData) => {
        formData.workspaceId = workspace.id;
        if (connectionId && workspace.id) {
            loadProjects(connectionId, workspace.id);
        }
    });

    const handleProjectChange = action((project: ListBoxItemData) => {
        const projectId = project.id;

        formData.projectId = projectId;
        if (connectionId && projectId) {
            const workspaceId = selectedWorkspace?.id;

            loadTicketTypes({ connectionId, workspaceId, projectId });
        }
    });

    const handleTicketTypeChange = action((ticketType: ListBoxItemData) => {
        const typeId = ticketType.id;

        formData.ticketTypeId = typeId;
        if (connectionId && selectedProject?.id && selectedWorkspace?.id) {
            loadAdditionalFields(typeId, {
                connectionId,
                projectId: selectedProject.id,
                workspaceId: selectedWorkspace.id,
            });
        }
    });

    return (
        <>
            {/* Workspace Field*/}
            <CreateTicketFieldWorkspace onChange={handleWorkspaceChange} />
            {/* Project Field */}
            {selectedWorkspace?.id && (
                <CreateTicketFieldProject onChange={handleProjectChange} />
            )}
            {/* Ticket Type Field */}
            {selectedProject?.id && (
                <CreateTicketFieldTicketType
                    onChange={handleTicketTypeChange}
                />
            )}
            {/* Additional Fields */}
            {selectedTicketType?.id && <CreateTicketDynamicFields />}
        </>
    );
});
