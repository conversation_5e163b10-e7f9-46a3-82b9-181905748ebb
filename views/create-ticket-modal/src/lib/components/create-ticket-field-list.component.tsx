import { useMemo } from 'react';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldListProps {
    onChange?: (list: ListBoxItemData) => void;
}

export const CreateTicketFieldList = observer(
    ({ onChange }: CreateTicketFieldListProps) => {
        const { isListsLoading, lists, selectedList, invalidProps } =
            sharedCreateTicketController;

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            if (!invalidProps.includes('listId')) {
                return undefined;
            }

            return {
                id: `create-ticket-field-list-feedback`,
                type: 'error',
                message: t`List selection is required`,
            };
        }, [invalidProps]);

        return (
            <SelectField
                required
                data-id="create-ticket-field-list"
                key={`create-ticket-field-list-${selectedList?.id}`}
                formId={CREATE_TICKET_FORM_ID}
                name="list"
                label={t`List`}
                helpText={t`You are selecting a list from the space "Space" directory.`}
                value={toJS(selectedList)}
                options={lists}
                isLoading={isListsLoading}
                feedback={feedback}
                onChange={onChange}
            />
        );
    },
);
