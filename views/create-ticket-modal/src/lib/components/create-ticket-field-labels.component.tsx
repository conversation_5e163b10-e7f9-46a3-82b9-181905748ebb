import {
    type CreateTicket<PERSON><PERSON><PERSON>ield,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { observer, toJS } from '@globals/mobx';
import { CreateTicketFieldGenericCombobox } from './create-ticket-field-generic-combobox';

interface CreateTicketFieldLabelsProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldLabels = observer(
    ({ field }: CreateTicketFieldLabelsProps) => {
        const { isLabelsLoading, labels, fetchLabels, hasMoreLabels } =
            sharedCreateTicketController;

        return (
            <CreateTicketFieldGenericCombobox
                data-id="create-ticket-field-labels"
                field={field}
                options={toJS(labels)}
                hasMore={hasMoreLabels}
                isLoading={isLabelsLoading}
                onFetchOptions={fetchLabels(field.dataEndpointParams)}
            />
        );
    },
);
