import {
    type CreateTicket<PERSON><PERSON><PERSON><PERSON>,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { observer, toJS } from '@globals/mobx';
import { CreateTicketFieldGenericCombobox } from './create-ticket-field-generic-combobox';

interface CreateTicketFieldUserProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldUser = observer(
    ({ field }: CreateTicketFieldUserProps) => {
        const { isUsersLoading, users, fetchUsers, hasMoreUsers } =
            sharedCreateTicketController;

        return (
            <CreateTicketFieldGenericCombobox
                data-id="create-ticket-field-user"
                field={field}
                options={toJS(users)}
                hasMore={hasMoreUsers}
                isLoading={isUsersLoading}
                onFetchOptions={fetchUsers(field.dataEndpointParams)}
            />
        );
    },
);
