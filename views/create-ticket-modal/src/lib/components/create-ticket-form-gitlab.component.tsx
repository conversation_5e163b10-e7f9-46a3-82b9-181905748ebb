import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { action, observer } from '@globals/mobx';
import { CreateTicketDynamicFields } from './create-ticket-dynamic-fields.component';
import { CreateTicketFieldProject } from './create-ticket-field-project.component';
import { CreateTicketFieldTicketType } from './create-ticket-field-ticket-type.component';

export const CreateTicketFormGitlab = observer(() => {
    const {
        formData,
        loadTicketTypes,
        connectionId,
        selectedProject,
        selectedTicketType,
        loadAdditionalFields,
    } = sharedCreateTicketController;

    const handleProjectChange = action((project: ListBoxItemData) => {
        const projectId = project.id;

        formData.projectId = projectId;
        if (connectionId && projectId) {
            loadTicketTypes({ connectionId, projectId });
        }
    });

    const handleTicketTypeChange = action((ticketType: ListBoxItemData) => {
        const typeId = ticketType.id;

        formData.ticketTypeId = typeId;
        if (connectionId && selectedProject?.value) {
            loadAdditionalFields(typeId, {
                connectionId,
                projectId: selectedProject.value,
            });
        }
    });

    return (
        <>
            {/* Project Field */}
            <CreateTicketFieldProject onChange={handleProjectChange} />
            {/* Issue Type Field */}
            {selectedProject?.id && (
                <CreateTicketFieldTicketType
                    onChange={handleTicketTypeChange}
                />
            )}
            {/* Additional Fields */}
            {selectedTicketType?.id && <CreateTicketDynamicFields />}
        </>
    );
});
