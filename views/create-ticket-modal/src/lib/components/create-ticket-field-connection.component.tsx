import { useMemo } from 'react';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

export const CreateTicketFieldConnection = observer(() => {
    const {
        isConnectionsLoading,
        availableConnections,
        selectedConnection,
        setConnection,
        invalidProps,
    } = sharedCreateTicketController;

    const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
        if (!invalidProps.includes('connectionId')) {
            return undefined;
        }

        return {
            id: `create-ticket-field-destination-feedback`,
            type: 'error',
            message: t`Destination is required`,
        };
    }, [invalidProps]);

    return (
        <SelectField
            key={`create-ticket-field-connection-${selectedConnection?.id}`}
            data-id="create-ticket-field-connection"
            formId={CREATE_TICKET_FORM_ID}
            name="ticket-provider"
            label={t`Destination`}
            value={toJS(selectedConnection)}
            helpText={t`We detected that you have access to multiple ticketing systems. Please select the one you want to use.`}
            options={availableConnections}
            isLoading={isConnectionsLoading}
            feedback={feedback}
            onChange={setConnection}
        />
    );
});
