import { isEmpty } from 'lodash-es';
import { useMemo } from 'react';
import {
    type CreateTicketDynamicField,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldComboboxProps {
    field: CreateTicketDynamicField;
    options?: ListBoxItemData[];
    hasMore?: boolean;
    isLoading?: boolean;
    onFetchOptions?: (params: {
        search?: string;
        increasePage?: boolean;
    }) => void;
}

export const CreateTicketFieldGenericCombobox = observer(
    ({
        field,
        options,
        hasMore,
        isLoading,
        onFetchOptions,
    }: CreateTicketFieldComboboxProps): React.JSX.Element => {
        const { fields, setField, invalidDynamicFields } =
            sharedCreateTicketController;

        const value = useMemo(
            () => toJS(fields[field.name] ?? {}),
            [fields, field.name],
        );

        const defaultValue = useMemo(() => {
            if (!value.item) {
                return undefined;
            }

            return options?.find(
                (option) => option.value === value.item?.value,
            );
        }, [value, options]);

        const defaultSelectedOptions = useMemo(() => {
            if (isEmpty(value.items)) {
                return undefined;
            }

            return options?.filter((option) =>
                value.items?.some((item) => item.value === option.value),
            );
        }, [value, options]);

        const isMultiSelect = useMemo(
            () => field.type === 'array' || field.type === 'multiselect',
            [field.type],
        );

        const keyValue = useMemo(
            () =>
                isMultiSelect
                    ? value.items?.map((f) => f.value).join('-')
                    : value.item?.value,
            [isMultiSelect, value],
        );

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            const { name, label } = field;

            if (!invalidDynamicFields.includes(name)) {
                return undefined;
            }

            return {
                id: `create-ticket-field-${name}-feedback`,
                type: 'error',
                message: t`${label} is required`,
            };
        }, [invalidDynamicFields, field]);

        return (
            <ComboboxField
                isMultiSelect={isMultiSelect}
                data-id={`create-ticket-field-${field.name}`}
                key={`create-ticket-field-${field.name}-${keyValue}`}
                formId={CREATE_TICKET_FORM_ID}
                name={field.name}
                label={field.label}
                defaultSelectedOptions={defaultSelectedOptions}
                defaultValue={defaultValue}
                optionalText={field.required ? undefined : t`Optional`}
                isLoading={isLoading}
                loaderLabel={t`Loading...`}
                hasMore={hasMore}
                feedback={feedback}
                options={
                    options ??
                    field.allowedValues?.map((option) => ({
                        id: option.value,
                        label: option.label,
                        value: option.value,
                    }))
                }
                onFetchOptions={onFetchOptions}
                onChange={(
                    val: ListBoxItemData | ListBoxItemData[] | undefined,
                ) => {
                    if (isMultiSelect) {
                        setField({
                            name: field.name,
                            type: field.type,
                            items: val as ListBoxItemData[],
                        });
                    } else {
                        setField({
                            name: field.name,
                            type: field.type,
                            item: val as ListBoxItemData,
                        });
                    }
                }}
            />
        );
    },
);
