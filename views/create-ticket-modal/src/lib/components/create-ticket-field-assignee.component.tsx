import {
    type CreateTicket<PERSON><PERSON><PERSON><PERSON>,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { observer, toJS } from '@globals/mobx';
import { CreateTicketFieldGenericCombobox } from './create-ticket-field-generic-combobox';

interface CreateTicketFieldAssigneeProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldAssignee = observer(
    ({ field }: CreateTicketFieldAssigneeProps) => {
        const {
            isAssigneesLoading,
            assignees,
            fetchAssignees,
            hasMoreAssignees,
        } = sharedCreateTicketController;

        return (
            <CreateTicketFieldGenericCombobox
                data-id="create-ticket-field-assignee"
                field={field}
                options={toJS(assignees)}
                hasMore={hasMoreAssignees}
                isLoading={isAssigneesLoading}
                onFetchOptions={fetchAssignees(field.dataEndpointParams)}
            />
        );
    },
);
