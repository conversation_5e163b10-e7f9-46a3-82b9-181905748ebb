import { useMemo } from 'react';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldWorkspaceProps {
    onChange?: (workspace: ListBoxItemData) => void;
}

export const CreateTicketFieldWorkspace = observer(
    ({ onChange }: CreateTicketFieldWorkspaceProps) => {
        const {
            isWorkspacesLoading,
            workspaces,
            selectedWorkspace,
            invalidProps,
        } = sharedCreateTicketController;

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            if (!invalidProps.includes('workspaceId')) {
                return undefined;
            }

            return {
                id: `create-ticket-field-workspace-feedback`,
                type: 'error',
                message: t`Workspace is required`,
            };
        }, [invalidProps]);

        return (
            <SelectField
                required
                data-id="create-ticket-field-workspace"
                key={`create-ticket-field-workspace-${selectedWorkspace?.id}`}
                formId={CREATE_TICKET_FORM_ID}
                name="workspace"
                label={t`Workspace`}
                helpText={t`Select a workspace to show its projects`}
                value={toJS(selectedWorkspace)}
                options={workspaces}
                isLoading={isWorkspacesLoading}
                feedback={feedback}
                onChange={onChange}
            />
        );
    },
);
