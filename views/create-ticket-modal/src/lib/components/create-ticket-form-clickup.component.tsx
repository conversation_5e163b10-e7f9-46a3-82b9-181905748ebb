import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { action, observer } from '@globals/mobx';
import { CreateTicketDynamicFields } from './create-ticket-dynamic-fields.component';
import { CreateTicketFieldFolder } from './create-ticket-field-folder.component';
import { CreateTicketFieldList } from './create-ticket-field-list.component';
import { CreateTicketFieldProject } from './create-ticket-field-project.component';
import { CreateTicketFieldTicketType } from './create-ticket-field-ticket-type.component';
import { CreateTicketFieldWorkspace } from './create-ticket-field-workspace.component';

export const CreateTicketFormClickup = observer(() => {
    const {
        loadProjects,
        loadFolders,
        loadLists,
        loadTicketTypes,
        formData,
        connectionId,
        selectedWorkspace,
        selectedProject,
        selectedList,
        selectedTicketType,
        loadAdditionalFields,
    } = sharedCreateTicketController;

    const handleWorkspaceChange = action((workspace: ListBoxItemData) => {
        formData.workspaceId = workspace.id;
        if (connectionId && workspace.id) {
            loadProjects(connectionId, workspace.id);
        }
    });

    const handleProjectChange = action((project: ListBoxItemData) => {
        const directoryId = project.id;

        formData.projectId = directoryId;

        if (connectionId && directoryId) {
            loadFolders(connectionId, directoryId);
            loadLists({ connectionId, directoryId });
        }
    });

    const handleFolderChange = action((folder: ListBoxItemData) => {
        const folderId = folder.id;

        formData.folderId = folderId;
        const directoryId = selectedProject?.id;

        if (connectionId && directoryId) {
            loadLists({ connectionId, directoryId, folderId });
        }
    });

    const handleListChange = action((list: ListBoxItemData) => {
        const listId = list.id;

        formData.listId = listId;
        if (connectionId && listId) {
            const workspaceId = selectedWorkspace?.id;

            loadTicketTypes({ connectionId, workspaceId, projectId: listId });
        }
    });

    const handleTicketTypeChange = action((ticketType: ListBoxItemData) => {
        const typeId = ticketType.id;

        formData.ticketTypeId = typeId;
        if (
            connectionId &&
            selectedProject?.id &&
            selectedList?.id &&
            selectedWorkspace?.id
        ) {
            loadAdditionalFields(typeId, {
                connectionId,
                projectId: selectedProject.id,
                projectName: selectedProject.label,
                organization: selectedWorkspace.id,
                listId: selectedList.id,
                workspaceId: selectedWorkspace.id,
            });
        }
    });

    return (
        <>
            {/* Workspace Field*/}
            <CreateTicketFieldWorkspace onChange={handleWorkspaceChange} />
            {/* Project Field */}
            {selectedWorkspace?.id && (
                <CreateTicketFieldProject onChange={handleProjectChange} />
            )}
            {/* Folder Field: Only used as a list filter */}
            {selectedProject?.id && (
                <CreateTicketFieldFolder onChange={handleFolderChange} />
            )}
            {/* List Field */}
            {selectedProject?.id && (
                <CreateTicketFieldList onChange={handleListChange} />
            )}
            {/* Ticket Type Field */}
            {selectedList?.id && (
                <CreateTicketFieldTicketType
                    onChange={handleTicketTypeChange}
                />
            )}
            {/* Additional Fields */}
            {selectedTicketType?.id && <CreateTicketDynamicFields />}
        </>
    );
});
