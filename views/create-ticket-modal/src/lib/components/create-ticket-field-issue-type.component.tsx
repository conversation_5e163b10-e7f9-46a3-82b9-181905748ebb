import { useMemo } from 'react';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldIssueTypeProps {
    onChange?: (issueType: ListBoxItemData) => void;
}

export const CreateTicketFieldIssueType = observer(
    ({ onChange }: CreateTicketFieldIssueTypeProps) => {
        const {
            isIssueTypesLoading,
            issueTypes,
            selectedIssueType,
            setIssueType,
            invalidProps,
        } = sharedCreateTicketController;

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            if (!invalidProps.includes('issueTypeId')) {
                return undefined;
            }

            return {
                id: `create-ticket-field-issue-type-feedback`,
                type: 'error',
                message: t`Issue type is required`,
            };
        }, [invalidProps]);

        return (
            <SelectField
                required
                key={`create-ticket-field-issue-type-${selectedIssueType?.id}`}
                data-id="create-ticket-field-issue-type"
                formId={CREATE_TICKET_FORM_ID}
                name="ticketType"
                label={t`Issue type`}
                value={toJS(selectedIssueType)}
                options={issueTypes}
                isLoading={isIssueTypesLoading}
                feedback={feedback}
                onChange={onChange ?? setIssueType}
            />
        );
    },
);
