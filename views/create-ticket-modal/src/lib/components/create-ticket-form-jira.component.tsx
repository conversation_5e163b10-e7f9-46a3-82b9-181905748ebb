import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { action, observer } from '@globals/mobx';
import { CreateTicketDynamicFields } from './create-ticket-dynamic-fields.component';
import { CreateTicketFieldIssueType } from './create-ticket-field-issue-type.component';
import { CreateTicketFieldProject } from './create-ticket-field-project.component';

export const CreateTicketFormJira = observer(() => {
    const {
        formData,
        loadIssueTypes,
        connectionId,
        selectedProject,
        selectedIssueType,
        loadAdditionalFields,
    } = sharedCreateTicketController;

    const handleProjectChange = action((project: ListBoxItemData) => {
        const projectId = project.id;

        formData.projectId = projectId;
        if (connectionId && projectId) {
            loadIssueTypes({ connectionId, projectId });
        }
    });

    const handleIssueTypeChange = action((issueType: ListBoxItemData) => {
        const typeId = issueType.id;

        formData.issueTypeId = typeId;
        if (connectionId && selectedProject?.id) {
            loadAdditionalFields(typeId, {
                connectionId,
                projectId: selectedProject.id,
            });
        }
    });

    return (
        <>
            {/* Project Field */}
            <CreateTicketFieldProject onChange={handleProjectChange} />
            {/* Issue Type Field */}
            {selectedProject?.id && (
                <CreateTicketFieldIssueType onChange={handleIssueTypeChange} />
            )}
            {/* Additional Fields */}
            {selectedIssueType?.id && <CreateTicketDynamicFields />}
        </>
    );
});
