import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { action, observer } from '@globals/mobx';
import { CreateTicketDynamicFields } from './create-ticket-dynamic-fields.component';
import { CreateTicketFieldTeam } from './create-ticket-field-team.component';

export const CreateTicketFormLinear = observer(() => {
    const { connectionId, formData, loadAdditionalFields, selectedTeam } =
        sharedCreateTicketController;

    const handleTeamChange = action((team: ListBoxItemData) => {
        const teamId = team.id;

        formData.teamId = teamId;

        if (connectionId && teamId) {
            loadAdditionalFields(teamId, {
                connectionId,
                teamId,
            });
        }
    });

    return (
        <>
            {/* Team Field */}
            <CreateTicketFieldTeam onChange={handleTeamChange} />
            {/* Additional Fields */}
            {selectedTeam?.id && <CreateTicketDynamicFields />}
        </>
    );
});
