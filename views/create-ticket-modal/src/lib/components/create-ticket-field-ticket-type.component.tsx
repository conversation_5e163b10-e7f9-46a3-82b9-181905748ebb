import { useMemo } from 'react';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import type { ListBoxItemData } from '../../../../../cosmos/components/list-box/src/lib/components/ListBoxItem';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldTicketTypeProps {
    onChange?: (ticketType: ListBoxItemData) => void;
}

export const CreateTicketFieldTicketType = observer(
    ({ onChange }: CreateTicketFieldTicketTypeProps) => {
        const {
            isTicketTypesLoading,
            ticketTypes,
            selectedTicketType,
            setTicketType,
            invalidProps,
        } = sharedCreateTicketController;

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            if (!invalidProps.includes('issueTypeId')) {
                return undefined;
            }

            return {
                id: `create-ticket-field-issue-type-feedback`,
                type: 'error',
                message: t`Ticket type is required`,
            };
        }, [invalidProps]);

        return (
            <SelectField
                required
                data-id="create-ticket-field-ticket-type"
                key={`create-ticket-field-ticket-type-${selectedTicketType?.id}`}
                formId={CREATE_TICKET_FORM_ID}
                name="ticketType"
                label={t`Ticket type`}
                value={toJS(selectedTicketType)}
                options={ticketTypes}
                isLoading={isTicketTypesLoading}
                feedback={feedback}
                onChange={onChange ?? setTicketType}
            />
        );
    },
);
