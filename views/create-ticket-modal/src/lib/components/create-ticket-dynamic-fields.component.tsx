import { isEmpty } from 'lodash-es';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { CreateTicketFieldAssignee } from './create-ticket-field-assignee.component';
import { CreateTicketFieldCycle } from './create-ticket-field-cycle.component';
import { CreateTicketFieldGenericCheckbox } from './create-ticket-field-generic-checkbox';
import { CreateTicketFieldGenericCombobox } from './create-ticket-field-generic-combobox';
import { CreateTicketFieldGenericDate } from './create-ticket-field-generic-date';
import { CreateTicketFieldGenericSelect } from './create-ticket-field-generic-select';
import { CreateTicketFieldGenericText } from './create-ticket-field-generic-text';
import { CreateTicketFieldGenericTextarea } from './create-ticket-field-generic-textarea';
import { CreateTicketFieldIssueType } from './create-ticket-field-issue-type.component';
import { CreateTicketFieldLabels } from './create-ticket-field-labels.component';
import { CreateTicketFieldParent } from './create-ticket-field-parent.component';
import { CreateTicketFieldProject } from './create-ticket-field-project.component';
import { CreateTicketFieldState } from './create-ticket-field-state.component';
import { CreateTicketFieldUser } from './create-ticket-field-user.component';

export const CreateTicketDynamicFields = observer(() => {
    const { isAdditionalFieldsLoading, dynamicFields } =
        sharedCreateTicketController;

    return isAdditionalFieldsLoading ? (
        <Stack direction="row" justify="center" p="md">
            <Loader label={t`Loading...`} />
        </Stack>
    ) : (
        dynamicFields.map((field) => {
            // Checkbox Field
            if (field.type === 'boolean') {
                return (
                    <CreateTicketFieldGenericCheckbox
                        key={field.name}
                        field={field}
                    />
                );
            }

            // Date Field
            if (field.type === 'date' || field.type === 'datetime') {
                return (
                    <CreateTicketFieldGenericDate
                        key={field.name}
                        field={field}
                    />
                );
            }

            // Textarea Field
            if (field.type === 'text') {
                return (
                    <CreateTicketFieldGenericTextarea
                        key={field.name}
                        field={field}
                    />
                );
            }

            // Multiselect field
            if (
                (field.type === 'multiselect' || field.type === 'array') &&
                field.allowedValues
            ) {
                return (
                    <CreateTicketFieldGenericCombobox
                        key={field.name}
                        field={field}
                    />
                );
            }

            // Select Field
            if (!isEmpty(field.allowedValues)) {
                return (
                    <CreateTicketFieldGenericSelect
                        key={field.name}
                        field={field}
                    />
                );
            }

            // Mapping specific endpoints first so we can fetch the dynamic options via the correct controllers
            // Endpoints map can be found here: https://github.com/drata/api/blob/release/src/app/apis/types/jira/ticket-field.helper.ts#L45
            // Assignee Field
            if (
                field.dataEndpoint?.startsWith('/ticket/assignable/projects/')
            ) {
                return (
                    <CreateTicketFieldAssignee key={field.name} field={field} />
                );
            }

            // User Field
            if (field.dataEndpoint?.startsWith('/ticket/users')) {
                return <CreateTicketFieldUser key={field.name} field={field} />;
            }

            // Parent Field
            if (field.dataEndpoint?.startsWith('/ticket/parent')) {
                return (
                    <CreateTicketFieldParent key={field.name} field={field} />
                );
            }

            // Status Field
            if (field.dataEndpoint?.startsWith('/ticket/status')) {
                return (
                    <CreateTicketFieldState key={field.name} field={field} />
                );
            }

            // Cycle Field
            if (field.dataEndpoint?.startsWith('/ticket/cycles')) {
                return (
                    <CreateTicketFieldCycle key={field.name} field={field} />
                );
            }

            // Labels Field
            if (field.dataEndpoint?.startsWith('/ticket/labels')) {
                return (
                    <CreateTicketFieldLabels key={field.name} field={field} />
                );
            }

            // Project Field
            if (field.name === 'project') {
                return <CreateTicketFieldProject key={field.name} />;
            }

            // Issue Type Field
            if (field.name === 'issuetype') {
                return <CreateTicketFieldIssueType key={field.name} />;
            }

            return (
                <CreateTicketFieldGenericText
                    key={field.name}
                    field={field}
                    data-id={`create-ticket-field-generic-${field.name}`}
                />
            );
        })
    );
});
