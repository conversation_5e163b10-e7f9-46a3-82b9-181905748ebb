import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldFolderProps {
    onChange?: (folder: ListBoxItemData) => void;
}

export const CreateTicketFieldFolder = observer(
    ({ onChange }: CreateTicketFieldFolderProps) => {
        const { isFoldersLoading, folders, selectedFolder, setFolder } =
            sharedCreateTicketController;

        return (
            <SelectField
                data-id="create-ticket-field-folder"
                key={`create-ticket-field-folder-${selectedFolder?.id}`}
                formId={CREATE_TICKET_FORM_ID}
                name="folder"
                label={t`Folder`}
                helpText={t`Select a folder to choose a list within it. If no folder is selected, you will select a list from the "Space" space.`}
                value={toJS(selectedFolder)}
                options={folders}
                optionalText={t`Optional`}
                isLoading={isFoldersLoading}
                onChange={onChange ?? setFolder}
            />
        );
    },
);
