import {
    type CreateTicket<PERSON><PERSON><PERSON>ield,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { observer, toJS } from '@globals/mobx';
import { CreateTicketFieldGenericCombobox } from './create-ticket-field-generic-combobox';

interface CreateTicketFieldStateProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldState = observer(
    ({ field }: CreateTicketFieldStateProps) => {
        const { isStatesLoading, states, fetchStates, hasMoreStates } =
            sharedCreateTicketController;

        return (
            <CreateTicketFieldGenericCombobox
                data-id="create-ticket-field-state"
                field={field}
                options={toJS(states)}
                hasMore={hasMoreStates}
                isLoading={isStatesLoading}
                onFetchOptions={fetchStates(field.dataEndpointParams)}
            />
        );
    },
);
