import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { action, observer } from '@globals/mobx';
import { CreateTicketDynamicFields } from './create-ticket-dynamic-fields.component';
import { CreateTicketFieldIssueType } from './create-ticket-field-issue-type.component';

export const CreateTicketFormServiceNow = observer(() => {
    const { formData, connectionId, selectedIssueType, loadAdditionalFields } =
        sharedCreateTicketController;

    const handleIssueTypeChange = action((issueType: ListBoxItemData) => {
        const typeId = issueType.id;

        formData.issueTypeId = typeId;
        if (connectionId) {
            loadAdditionalFields(typeId, { connectionId });
        }
    });

    return (
        <>
            {/* Issue Type field */}
            <CreateTicketFieldIssueType onChange={handleIssueTypeChange} />
            {/* Additional Fields */}
            {selectedIssueType?.id && <CreateTicketDynamicFields />}
        </>
    );
});
