import { useMemo } from 'react';
import {
    type CreateTicketD<PERSON><PERSON><PERSON>,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import {
    DatePickerField,
    type TDateISODate,
} from '@cosmos/components/date-picker-field';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldGenericProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldGenericDate = observer(
    ({ field }: CreateTicketFieldGenericProps): React.JSX.Element => {
        const { fields, setField, invalidDynamicFields } =
            sharedCreateTicketController;
        const fieldValue = toJS(fields[field.name] ?? {});

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            const { name, label } = field;

            if (!invalidDynamicFields.includes(name)) {
                return undefined;
            }

            return {
                id: `create-ticket-field-${name}-feedback`,
                type: 'error',
                message: t`${label} is required`,
            };
        }, [invalidDynamicFields, field]);

        return (
            <DatePickerField
                data-id={`create-ticket-field-${field.name}`}
                key={`create-ticket-field-${field.name}-${fieldValue.value}`}
                name={field.name}
                formId={CREATE_TICKET_FORM_ID}
                label={field.label}
                optionalText={field.required ? undefined : t`Optional`}
                locale="en-US"
                value={fieldValue.value as TDateISODate}
                monthSelectionFieldLabel={t`Select month`}
                yearSelectionFieldLabel={t`Select year`}
                feedback={feedback}
                onChange={(date) => {
                    setField({
                        name: field.name,
                        value: String(date),
                        type: field.type,
                    });
                }}
            />
        );
    },
);
