import {
    type CreateTicket<PERSON><PERSON><PERSON>ield,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { observer, toJS } from '@globals/mobx';
import { CreateTicketFieldGenericCombobox } from './create-ticket-field-generic-combobox';

interface CreateTicketFieldParentProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldParent = observer(
    ({ field }: CreateTicketFieldParentProps) => {
        const { isParentsLoading, parents, fetchParents, hasMoreParents } =
            sharedCreateTicketController;

        return (
            <CreateTicketFieldGenericCombobox
                data-id="create-ticket-field-parent"
                field={field}
                options={toJS(parents)}
                hasMore={hasMoreParents}
                isLoading={isParentsLoading}
                onFetchOptions={fetchParents(field.dataEndpointParams)}
            />
        );
    },
);
