import { useEffect } from 'react';
import {
    type CreateTicketClientType,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { observer } from '@globals/mobx';
import { CreateTicketDynamicFields } from './create-ticket-dynamic-fields.component';

export const CreateTicketFormGeneric = observer(() => {
    const { providerId, loadStructureFields } = sharedCreateTicketController;

    useEffect(() => {
        if (providerId) {
            loadStructureFields(providerId as CreateTicketClientType);
        }
    }, [providerId, loadStructureFields]);

    return (
        <CreateTicketDynamicFields
            data-id={`create-ticket-form-generic-${providerId}`}
        />
    );
});
