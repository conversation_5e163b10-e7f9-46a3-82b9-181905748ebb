import {
    type CreateTicketD<PERSON><PERSON>ield,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldGenericProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldGenericCheckbox = observer(
    ({ field }: CreateTicketFieldGenericProps): React.JSX.Element => {
        const { fields, setField } = sharedCreateTicketController;
        const fieldValue = toJS(fields[field.name] ?? {});

        return (
            <CheckboxField
                data-id={`create-ticket-field-${field.name}`}
                name={field.name}
                formId={CREATE_TICKET_FORM_ID}
                label={field.label}
                required={field.required}
                value={field.label}
                checked={fieldValue.checked || false}
                onChange={(checked) => {
                    setField({
                        name: field.name,
                        value: field.label,
                        type: field.type,
                        checked,
                    });
                }}
            />
        );
    },
);
