import { useMemo } from 'react';
import {
    type CreateTicketDynamic<PERSON>ield,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldGenericProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldGenericText = observer(
    ({ field }: CreateTicketFieldGenericProps): React.JSX.Element => {
        const { fields, setField, invalidDynamicFields } =
            sharedCreateTicketController;
        const fieldValue = toJS(fields[field.name] ?? {});

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            const { name, label } = field;

            if (!invalidDynamicFields.includes(name)) {
                return undefined;
            }

            return {
                id: `create-ticket-field-${name}-feedback`,
                type: 'error',
                message: t`${label} is required`,
            };
        }, [invalidDynamicFields, field]);

        return (
            <TextField
                data-id={`create-ticket-field-${field.name}`}
                name={field.name}
                formId={CREATE_TICKET_FORM_ID}
                label={field.label}
                optionalText={field.required ? undefined : t`Optional`}
                feedback={feedback}
                value={fieldValue.value || ''}
                onChange={(e) => {
                    setField({
                        name: field.name,
                        type: field.type,
                        value: e.target.value,
                    });
                }}
            />
        );
    },
);
