import { useMemo } from 'react';
import { sharedCreateTicketController } from '@controllers/create-ticket';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CREATE_TICKET_FORM_ID } from '../constants/create-ticket.constants';

interface CreateTicketFieldProjectProps {
    onChange?: (project: ListBoxItemData) => void;
}

export const CreateTicketFieldProject = observer(
    ({ onChange }: CreateTicketFieldProjectProps) => {
        const { isProjectsLoading, projects, selectedProject, invalidProps } =
            sharedCreateTicketController;

        const feedback = useMemo<FieldFeedbackProps | undefined>(() => {
            if (!invalidProps.includes('projectId')) {
                return undefined;
            }

            return {
                id: `create-ticket-field-project-feedback`,
                type: 'error',
                message: t`Project selection is required`,
            };
        }, [invalidProps]);

        return (
            <SelectField
                required
                data-id="create-ticket-field-project"
                key={`create-ticket-field-project-${selectedProject?.id}`}
                formId={CREATE_TICKET_FORM_ID}
                name="project"
                label={t`Project`}
                helpText={t`Form fields vary based on the projects in your organization`}
                value={toJS(selectedProject)}
                options={projects}
                isLoading={isProjectsLoading}
                feedback={feedback}
                onChange={onChange}
            />
        );
    },
);
