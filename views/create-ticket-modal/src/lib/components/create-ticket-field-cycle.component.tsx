import {
    type CreateTicket<PERSON><PERSON><PERSON>ield,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { observer, toJS } from '@globals/mobx';
import { CreateTicketFieldGenericCombobox } from './create-ticket-field-generic-combobox';

interface CreateTicketFieldCycleProps {
    field: CreateTicketDynamicField;
}

export const CreateTicketFieldCycle = observer(
    ({ field }: CreateTicketFieldCycleProps) => {
        const { isCyclesLoading, cycles, fetchCycles, hasMoreCycles } =
            sharedCreateTicketController;

        return (
            <CreateTicketFieldGenericCombobox
                data-id="create-ticket-field-cycle"
                field={field}
                options={toJS(cycles)}
                hasMore={hasMoreCycles}
                isLoading={isCyclesLoading}
                onFetchOptions={fetchCycles(field.dataEndpointParams)}
            />
        );
    },
);
