import { isNil } from 'lodash-es';
import { useEffect } from 'react';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { PoliciesEditorComponent } from '@components/policies-editor';
import {
    sharedPolicyBuilderController,
    sharedPolicyCkEditorController,
} from '@controllers/policy-builder';
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { useBlocker } from '@remix-run/react';
import { PoliciesBuilderPolicyLoading } from './policies-builder-policy-loading';

export const PoliciesBuilderPolicyView = observer((): React.JSX.Element => {
    const { policy, policyPdfSignedUrl, isPdfSignedUrlLoading } =
        sharedPolicyBuilderController;

    const {
        isPolicyBuilderFirstLoading,
        latestPolicyVersion,
        isAuthoredPolicy,
        isUploadedPolicy,
        isApproved,
        isExternalPolicy,
        isPolicyCkEditorInEditMode,
        policyId,
    } = sharedPolicyBuilderModel;

    const isFileBasedPolicy = isUploadedPolicy || isExternalPolicy;

    const shouldBlock = isApproved && isPolicyCkEditorInEditMode;

    const blocker = useBlocker(({ currentLocation, nextLocation }) => {
        return (
            shouldBlock && currentLocation.pathname !== nextLocation.pathname
        );
    });

    useEffect(() => {
        if (blocker.state === 'blocked') {
            sharedPolicyCkEditorController.handleNavigationAttempt(() => {
                blocker.proceed();
            });
        }
    }, [blocker]);

    useEffect(() => {
        if (isFileBasedPolicy && policyId) {
            sharedPolicyBuilderController.loadPolicyPdfDownload(policyId);
        }
    }, [isFileBasedPolicy, policyId]);

    if (isPolicyBuilderFirstLoading || isPdfSignedUrlLoading) {
        return <PoliciesBuilderPolicyLoading />;
    }

    if (isNil(latestPolicyVersion)) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    if (isFileBasedPolicy) {
        return (
            <PdfViewer
                src={policyPdfSignedUrl ?? ''}
                label={policy?.name ?? ''}
                data-id="EUID_Z0b"
            />
        );
    }

    if (isAuthoredPolicy) {
        return <PoliciesEditorComponent />;
    }

    return <PoliciesBuilderEmptyStateComponent data-id="G-EDULpZ" />;
});
