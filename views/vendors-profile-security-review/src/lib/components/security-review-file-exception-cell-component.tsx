import { isEmpty, isNil } from 'lodash-es';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

interface SecurityReviewFileExceptionCellComponentProps {
    cellData: string;
}

export const SecurityReviewFileExceptionCellComponent = observer(
    ({
        cellData,
    }: SecurityReviewFileExceptionCellComponentProps): React.JSX.Element => {
        if (isEmpty(cellData) || isNil(cellData)) {
            return (
                <Text type="body" size="100">
                    -
                </Text>
            );
        }

        const shouldShowAIIcon = cellData !== t`Ready for review`;

        return (
            <Stack
                direction="row"
                gap="sm"
                align="center"
                data-testid="SecurityReviewFileExceptionCellComponent"
                data-id="1DHspZ55"
            >
                {shouldShowAIIcon && (
                    <Icon name="AI" colorScheme="neutral" size="100" />
                )}
                <Text type="body" size="100">
                    {cellData}
                </Text>
            </Stack>
        );
    },
);
