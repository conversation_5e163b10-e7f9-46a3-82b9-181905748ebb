import type React from 'react';
import { modalController } from '@controllers/modal';
import { EmptyState } from '@cosmos/components/empty-state';
import { Metadata } from '@cosmos/components/metadata';
import { Modal } from '@cosmos/components/modal';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getInitials } from '@helpers/formatters';
import type { PolicyApproversModalController } from '../controllers/policy-approvers-modal.controller';

const handleClose = (): void => {
    modalController.closeModal('policy-approvers-modal');
};

interface PolicyApproversModalProps {
    controller: PolicyApproversModalController;
    formattedVersion: string;
    'data-id'?: string;
}

export const PolicyApproversModal = observer(
    ({
        controller,
        formattedVersion,
        'data-id': dataId = 'policy-approvers-modal',
    }: PolicyApproversModalProps): React.JSX.Element => {
        const { isLoading, hasError, approvers, isEmpty, emptyStateProps } =
            controller;

        const approvedLabel = t`Approved`;
        const closeLabel = t`Close`;

        return (
            <>
                <Modal.Header
                    title={t`Version ${formattedVersion} approval history`}
                    closeButtonAriaLabel={t`Close approval history modal`}
                    onClose={handleClose}
                />
                <Modal.Body>
                    {isLoading && <Skeleton />}
                    {!isLoading && (hasError || isEmpty) && (
                        <EmptyState {...emptyStateProps} />
                    )}
                    {!isLoading && !hasError && !isEmpty && (
                        <StackedList data-id={dataId}>
                            {approvers.map((approver, index) => {
                                const { name, imgUrl, statusUpdatedAt } =
                                    approver;
                                const fullName = name || '';
                                const initials = getInitials(fullName);
                                const uniqueKey = `${fullName}-${imgUrl || 'no-img'}-${index}`;

                                return (
                                    <StackedListItem
                                        key={uniqueKey}
                                        data-id={`approver-${index}`}
                                        primaryColumn={
                                            <AvatarIdentity
                                                primaryLabel={fullName}
                                                fallbackText={initials}
                                                imgSrc={imgUrl}
                                                size="sm"
                                            />
                                        }
                                        secondaryColumn={
                                            <Stack direction="column" gap="xs">
                                                <Metadata
                                                    label={approvedLabel}
                                                    type="status"
                                                    colorScheme="success"
                                                />
                                                <DateTime
                                                    date={statusUpdatedAt ?? ''}
                                                    format="sentence"
                                                    data-id={`approver-date-${index}`}
                                                />
                                            </Stack>
                                        }
                                    />
                                );
                            })}
                        </StackedList>
                    )}
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: closeLabel,
                            level: 'primary',
                            onClick: handleClose,
                        },
                    ]}
                />
            </>
        );
    },
);
