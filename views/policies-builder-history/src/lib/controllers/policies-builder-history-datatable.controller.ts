import { isEmpty, isNil } from 'lodash-es';
import type { ExternalDatatableController } from '@components/app-datatable';
import { PoliciesBuilderHistoryTableCellVersionComponent } from '@components/policies-builder-history';
import type {
    DatatableProps,
    RowActionItem,
} from '@cosmos/components/datatable';
import { policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions } from '@globals/api-sdk/queries';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { COLUMN_SIZES } from '@helpers/table';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { DateCell } from '../components/cells/date.cell';
import { ExplanationOfChangesCell } from '../components/cells/explanation-of-changes.cell';
import { UserCell } from '../components/cells/user.cell';
import { policiesBuilderHistoryActionsController } from './policies-builder-history-actions.controller';

export type PoliciesBuilderHistoryDatatableQuery = Required<
    Parameters<
        typeof policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions
    >
>[0]['query'];

export class PoliciesBuilderHistoryDatatableController
    implements
        ExternalDatatableController<
            PolicyTableVersionResponseDto,
            PoliciesBuilderHistoryDatatableQuery
        >
{
    policyId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    policyVersionHistoryQuery = new ObservedQuery(
        policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions,
    );

    load = (query: PoliciesBuilderHistoryDatatableQuery): void => {
        if (isNil(this.policyId)) {
            logger.warn(
                'PoliciesBuilderHistoryDatatableController: load method called before policyId has been set.',
            );

            return;
        }

        this.policyVersionHistoryQuery.load({
            path: { policyId: this.policyId },
            query,
        });
    };

    setPolicyId = (id: number): void => {
        this.policyId = id;
    };

    get hasError(): boolean {
        return this.policyVersionHistoryQuery.hasError;
    }

    get total(): DatatableProps<PolicyTableVersionResponseDto>['total'] {
        return this.policyVersionHistoryQuery.data?.total ?? 0;
    }

    get data(): DatatableProps<PolicyTableVersionResponseDto>['data'] {
        return this.policyVersionHistoryQuery.data?.data ?? [];
    }

    get isPolicyVersionHistoryQueryLoading(): boolean {
        return this.policyVersionHistoryQuery.isLoading;
    }

    get isLoading(): DatatableProps<PolicyTableVersionResponseDto>['isLoading'] {
        return (
            sharedPolicyBuilderModel.isPolicyBuilderFirstLoading ||
            (this.isPolicyVersionHistoryQueryLoading && isEmpty(this.data))
        );
    }

    get columns(): DatatableProps<PolicyTableVersionResponseDto>['columns'] {
        return [
            {
                id: 'ID',
                header: 'Version',
                enableSorting: true,
                accessorKey: 'formatted',
                cell: PoliciesBuilderHistoryTableCellVersionComponent,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'EXPLANATION_OF_CHANGES',
                header: 'Explanation of changes',
                enableSorting: false,
                accessorKey: 'changesExplanation',
                cell: ExplanationOfChangesCell,
                minSize: 400,
            },
            {
                id: 'OWNER',
                header: 'Owner',
                enableSorting: false,
                accessorKey: 'owner.firstName',
                cell: UserCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'APPROVER',
                header: 'Approver',
                enableSorting: false,
                accessorKey: 'approver.firstName',
                cell: UserCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'CREATED_AT',
                header: 'Created on',
                enableSorting: true,
                accessorKey: 'createdAt',
                cell: DateCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'APPROVED_AT',
                header: 'Approved on',
                enableSorting: true,
                accessorKey: 'approvedAt',
                cell: DateCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'PUBLISHED_AT',
                header: 'Published on',
                enableSorting: true,
                cell: DateCell,
                minSize: COLUMN_SIZES.LARGE,
            },
        ];
    }

    get filterProps(): DatatableProps<PolicyTableVersionResponseDto>['filterProps'] {
        return undefined;
    }

    get emptyStateProps(): DatatableProps<PolicyTableVersionResponseDto>['emptyStateProps'] {
        // If we have an error, show error state
        if (this.hasError) {
            return {
                title: 'Unable to load policy versions',
                description:
                    'There was an error loading the policy version history. Please try again.',
            };
        }

        // If this is the first time loading and no data, show first visit state
        // Check if query has never been called (query is null)
        if (
            this.policyVersionHistoryQuery.query === null &&
            isEmpty(this.data)
        ) {
            return {
                title: 'No policy versions yet',
                description:
                    "This policy doesn't have any versions yet. Versions will appear here once the policy is published.",
            };
        }

        // If we have filters/search applied and no results, show filtered state
        // TODO: Add logic to detect if filters are applied
        // For now, show generic no results state
        return {
            title: 'No policy versions found',
            description:
                'There are no policy versions that match the current criteria.',
        };
    }

    get tableSearchProps(): DatatableProps<PolicyTableVersionResponseDto>['tableSearchProps'] {
        return {
            hideSearch: true,
        };
    }

    get tableId(): DatatableProps<PolicyTableVersionResponseDto>['tableId'] {
        return 'datatable-policies-history';
    }

    get filterViewModeProps(): DatatableProps<PolicyTableVersionResponseDto>['filterViewModeProps'] {
        return {
            viewMode: 'toggleable',
            props: {
                initialSelectedOption: 'pinned',
                togglePinnedLabel: 'Pin filters to page',
                toggleUnpinnedLabel: 'Move filters to dropdown',
                selectedOption: 'pinned',
            },
        };
    }

    get rowActionsProps(): DatatableProps<PolicyTableVersionResponseDto>['rowActionsProps'] {
        return {
            type: 'dropdown',
            getRowActions: this.getRowActions,
        };
    }

    getRowActions = (row: PolicyTableVersionResponseDto): RowActionItem[] => {
        const actions: RowActionItem[] = [];

        if (policiesBuilderHistoryActionsController.canDownloadPolicies()) {
            actions.push({
                id: 'policies-builder-history-download-policy',
                label: 'Download policy',
                onSelect: () => {
                    if (isNil(this.policyId)) {
                        return;
                    }

                    const { version, subVersion } = this.parseVersion(
                        row.formatted,
                    );

                    policiesBuilderHistoryActionsController.downloadVersion({
                        policyId: this.policyId,
                        versionId: version,
                        subVersion,
                    });
                },
            });
        }

        actions.push({
            id: 'policies-builder-history-view-approval-history',
            label: 'View approval history',
            onSelect: () => {
                if (isNil(this.policyId)) {
                    return;
                }
                const { version } = this.parseVersion(row.formatted);

                policiesBuilderHistoryActionsController.openApproversModal({
                    policyId: this.policyId,
                    versionId: version,
                    formattedVersion: row.formatted,
                });
            },
        });

        return actions;
    };

    parseVersion(formatted: string): {
        version: number;
        subVersion: number;
    } {
        const [versionStr, subVersionStr] = (formatted || '1').split('.');
        const version = parseInt(versionStr, 10);
        const subVersion = subVersionStr ? parseInt(subVersionStr, 10) : 0;

        return { version, subVersion };
    }
}

export const policiesBuilderHistoryDatatableController =
    new PoliciesBuilderHistoryDatatableController();
