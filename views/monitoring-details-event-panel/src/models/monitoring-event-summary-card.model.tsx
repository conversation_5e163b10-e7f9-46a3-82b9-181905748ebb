import { isObject, isString } from 'lodash-es';
import type { AiFeedbackStatus } from '@components/ai-feedback';
import { sharedAiFeedbackController } from '@controllers/ai-feedback';
import { sharedEventsDetailsController } from '@controllers/events-details';
import { sharedAIExecutionController } from '@controllers/monitoring';
import { Feedback } from '@cosmos/components/feedback';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type {
    AiExecutionGroupResponseDto,
    AiExecutionResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { openAiFeedbackModal } from '@helpers/ai-feedback-modal';
import { openOptInModal } from '@helpers/ai-opt-in';
import { downloadBlob } from '@helpers/download-file';
import { AppButton } from '@ui/app-button';
import { AISummaryContent } from '../components/ai-summary-content.component';
import { formatAiExecutionsDataJsonToHtml } from '../helpers/ai-execution-data-formatter.helper';

class MonitoringEventSummaryCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return t`Summary of test failures`;
    }

    get eventId(): string | undefined {
        return sharedEventsDetailsController.eventsDetailsData?.id;
    }

    get aiSummaryData(): AiExecutionGroupResponseDto | null {
        if (!this.eventId) {
            return null;
        }

        // Force MobX to observe the entire array contents, not just the reference
        const executionGroups = [
            ...sharedAIExecutionController.executionGroups,
        ];

        return (
            executionGroups.find((group) => group.featureId === this.eventId) ??
            null
        );
    }

    openFeedbackModal(feedbackStatus: AiFeedbackStatus): void {
        const { handleSubmitAiFeedback } = sharedAiFeedbackController;

        openAiFeedbackModal({
            onSubmit: (formValues) => {
                handleSubmitAiFeedback({
                    formValues,
                    executionGroups:
                        sharedAIExecutionController.executionGroups,
                });
            },
            isLoading: false,
            initialFeedbackStatus: feedbackStatus,
        });
    }

    get canDownload(): boolean {
        return Boolean(this.aiSummaryData?.executions.length);
    }

    get testName(): string {
        const { eventsDetailsData } = sharedEventsDetailsController;

        return eventsDetailsData?.controlTestInstance?.name || 'Event';
    }

    get executions(): AiExecutionResponseDto[] {
        return this.aiSummaryData?.executions ?? [];
    }

    get executionsAsHtml(): string | undefined {
        return formatAiExecutionsDataJsonToHtml(this.executions);
    }

    downloadMetadataCode = (codeContent: string): void => {
        const blob = new Blob([codeContent], { type: 'text/plain' });

        downloadBlob(blob, `${this.testName}-code.txt`);
    };

    downloadAiSummary = (): void => {
        const { canDownload, aiSummaryData, testName } = this;

        if (!canDownload || !aiSummaryData?.executions) {
            return;
        }

        // Convert AI summary to text format
        const summaryText = aiSummaryData.executions
            .map((execution) => {
                try {
                    const data = isString(execution.data)
                        ? JSON.parse(execution.data)
                        : execution.data;

                    if (isString(data)) {
                        return data;
                    }

                    if (isObject(data)) {
                        return JSON.stringify(data, null, 2);
                    }

                    return String(data);
                } catch {
                    return isString(execution.data)
                        ? execution.data
                        : JSON.stringify(execution.data, null, 2);
                }
            })
            .filter(Boolean)
            .join('\n\n');

        const blob = new Blob([summaryText], { type: 'text/plain' });

        downloadBlob(blob, `${testName}-summary.txt`);
    };

    get isProcessing(): boolean {
        return sharedAIExecutionController.isProcessing;
    }

    get hasError(): boolean {
        return sharedAIExecutionController.hasError;
    }

    get isLoading(): boolean {
        return sharedAIExecutionController.isLoading;
    }

    get hasSummaryData(): boolean {
        return Boolean(this.aiSummaryData?.executions.length);
    }

    generateAiSummary = (): void => {
        if (sharedCurrentCompanyController.isSettingEnabled('AI_ENABLED')) {
            sharedEventsDetailsController.handleGenerateAiSummary();
        } else {
            openOptInModal();
        }
    };

    renderContent = (): React.JSX.Element => {
        if (this.isProcessing) {
            return (
                <Stack
                    gap="md"
                    direction="column"
                    data-id="mBmJo8-L"
                    data-testid="ai-summary-loading"
                >
                    <Skeleton barCount={3} width="100%" />
                </Stack>
            );
        }

        if (this.hasError) {
            return (
                <Feedback
                    severity="primary"
                    title={t`Drata AI couldn't generate a summary.`}
                    data-id="ai-summary-error-feedback"
                    data-testid="ai-summary-error-feedback"
                />
            );
        }

        if (this.hasSummaryData) {
            return <AISummaryContent />;
        }

        return (
            <>
                <Text
                    data-id="summary-description"
                    data-testid="summary-description"
                >
                    {t`List of each finding with associated account details, resource type, ID, and explanation of the failure.`}
                </Text>
                <Stack
                    direction="row"
                    justify="start"
                    pt="md"
                    data-id="generate-summary-stack"
                    data-testid="generate-summary-stack"
                >
                    <AppButton
                        label={t`Generate summary`}
                        startIconName="AI"
                        colorScheme="primary"
                        level="secondary"
                        size="sm"
                        data-testid="generate-ai-summary-button"
                        data-id="generate-ai-summary-button"
                        onClick={this.generateAiSummary}
                    />
                </Stack>
            </>
        );
    };
}

export const sharedMonitoringEventSummaryCardModel =
    new MonitoringEventSummaryCardModel();
