import type { DatatableProps } from '@cosmos/components/datatable';
import type { PolicyControlSummaryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    PolicyControlActionsCell,
    PolicyControlCodeCell,
    PolicyControlNameCell,
} from '../components';

export const getPolicyControlsColumns =
    (): DatatableProps<PolicyControlSummaryResponseDto>['columns'] =>
        [
            {
                id: 'actions',
                header: '',
                enableSorting: false,
                cell: PolicyControlActionsCell,
                isActionColumn: true,
                maxSize: 60,
                minSize: 60,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
            {
                id: 'CODE',
                header: t`Control code`,
                accessorKey: 'code',
                enableSorting: true,
                cell: PolicyControlCodeCell,
                minSize: 150,
                maxSize: 150,
            },
            {
                id: 'NAME',
                header: t`Name`,
                accessorKey: 'name',
                enableSorting: true,
                cell: PolicyControlNameCell,
                minSize: 400,
            },
            {
                id: 'description',
                header: t`Description`,
                accessorKey: 'description',
                enableSorting: false,
                minSize: 300,
            },
        ] as const satisfies DatatableProps<PolicyControlSummaryResponseDto>['columns'];
