import { isEmpty, isNil, noop } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import { openControlSelector } from '@components/controls';
import type { ObjectItem } from '@components/object-selector';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { sharedPolicyControlsController } from '@controllers/policy-controls';
import { DEFAULT_PAGE, type TableAction } from '@cosmos/components/datatable';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    observer,
    runInAction,
} from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { handleOpenEvidenceControlPanel } from './helpers/policy-control-panel.helper';

class PolicyControlsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get enhancedTableActions(): TableAction[] {
        const { policyControlsIds } = sharedPolicyControlsController;

        return [
            {
                actionType: 'button',
                id: 'link-controls-button',
                typeProps: {
                    label: t`Link Controls`,
                    level: 'secondary',
                    onClick: () => {
                        openControlSelector({
                            config: {
                                selectionMode: 'multi',
                                modal: {
                                    id: 'map-control-to-policies',
                                    title: t`Link Controls`,
                                    size: 'lg',
                                    confirmButtonLabel: t`Link`,
                                    cancelButtonLabel: t`Cancel`,
                                    showSelectedCount: true,
                                },
                                search: {
                                    placeholder: t`Search by control name...`,
                                    label: t`Search controls`,
                                    loaderLabel: t`Loading controls...`,
                                    emptyStateMessage: t`No controls found matching your search criteria.`,
                                    clearAllLabel: t`Clear all`,
                                },
                                filters: {
                                    // Exclude already selected policies
                                    excludeIds: policyControlsIds.map(String),
                                },
                            },
                            callbacks: {
                                onSelected: (
                                    selectedItems:
                                        | ObjectItem<ControlListResponseDto>[]
                                        | ObjectItem<ControlListResponseDto>,
                                ) => {
                                    // Handle both single and multi-select responses
                                    const items = Array.isArray(selectedItems)
                                        ? selectedItems
                                        : [selectedItems];

                                    // Add the selected policies to the controller
                                    const controls = items.map(
                                        (item) => item.objectData,
                                    );

                                    if (isEmpty(controls)) {
                                        return;
                                    }
                                    runInAction(() => {
                                        sharedPolicyControlsController.linkControlsToPolicy(
                                            controls,
                                        );
                                    });
                                },
                                onCancel: noop,
                            },
                        });
                    },
                },
            },
        ];
    }
}

const sharedPolicyControlsModel = new PolicyControlsModel();

export const PolicyControlsView = observer((): React.JSX.Element => {
    const { policyControls, total, isLoading, policyControlColumns } =
        sharedPolicyControlsController;

    const { latestPolicyVersion } = sharedPolicyBuilderModel;

    if (isNil(latestPolicyVersion) && !isLoading) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    return (
        <AppDatatable
            isLoading={isLoading}
            tableId="policy-controls-datatable"
            data={policyControls}
            columns={policyControlColumns}
            tableActions={sharedPolicyControlsModel.enhancedTableActions}
            data-id="policy-controls-table"
            total={total}
            emptyStateProps={{
                title: t`No results found`,
                description: t`Edit your filters or search and try again.`,
            }}
            onRowClick={({ row }) => {
                handleOpenEvidenceControlPanel({ controlId: row.id });
            }}
            onFetchData={action((data) => {
                sharedPolicyControlsController.setPagination({
                    page: data.pagination.page || DEFAULT_PAGE,
                    pageSize: data.pagination.pageSize,
                    search: data.globalFilter.search,
                });
                // No need to fetch data, as it's handled by the controller
            })}
        />
    );
});
