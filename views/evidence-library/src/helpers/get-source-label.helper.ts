import { t } from '@globals/i18n/macro';

const SOURCE_VALUES = ['FILE', 'URL', 'TICKET', 'TEST'] as const;

type SourceType = (typeof SOURCE_VALUES)[number];

function getSourceLabel(source: SourceType): string {
    switch (source) {
        case 'FILE': {
            return t`File`;
        }
        case 'URL': {
            return t`URL`;
        }
        case 'TICKET': {
            return t`Ticket`;
        }
        case 'TEST': {
            return t`Test`;
        }
        default: {
            return '';
        }
    }
}

export const getSourceOptions = (): { label: string; value: SourceType }[] =>
    SOURCE_VALUES.map((value) => ({
        label: getSourceLabel(value),
        value,
    }));
