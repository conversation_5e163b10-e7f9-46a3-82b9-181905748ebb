import { t } from '@globals/i18n/macro';

const STATUS_VALUES = ['NEEDS_ATTENTION', 'EXPIRING_SOON', 'READY'] as const;

type StatusValues = (typeof STATUS_VALUES)[number];

function getStatusLabel(status: StatusValues): string {
    switch (status) {
        case 'NEEDS_ATTENTION': {
            return t`Needs attention`;
        }
        case 'EXPIRING_SOON': {
            return t`Upcoming renewal`;
        }
        default: {
            return t`Ready`;
        }
    }
}

export const getStatusOptions = (): { label: string; value: StatusValues }[] =>
    STATUS_VALUES.map((value) => ({
        label: getStatusLabel(value),
        value,
    }));
