import { isEmpty } from 'lodash-es';
import { sharedUsersInfiniteController } from '@controllers/users';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getSourceOptions } from '../helpers/get-source-label.helper';
import { getStatusOptions } from '../helpers/get-status-label.helper';

export class EvidenceLibraryFiltersModel {
    constructor() {
        makeAutoObservable(this);
    }

    get statusFilter(): Filter {
        return {
            filterType: 'checkbox',
            id: 'status',
            label: t`Status`,
            options: getStatusOptions(),
        };
    }

    get sourceFilter(): Filter {
        return {
            filterType: 'checkbox',
            id: 'source',
            label: t`Source`,
            options: getSourceOptions(),
        };
    }

    get enabledFrameworks(): FrameworkResponseDto[] {
        const { workspaces, currentWorkspace } = sharedWorkspacesController;

        if (isEmpty(workspaces)) {
            return [];
        }

        return (
            currentWorkspace?.frameworks?.filter(
                ({ productFrameworkEnabled, frameworkEnabled }) =>
                    frameworkEnabled && productFrameworkEnabled,
            ) ?? []
        );
    }

    get frameworksFilter(): Filter {
        return {
            filterType: 'checkbox',
            id: 'frameworks',
            label: t`Framework`,
            options: this.enabledFrameworks.map(({ name, id }) => ({
                label: name,
                value: `${id}`,
            })),
        };
    }

    get evidenceOwnersFilter(): Filter {
        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;

        return {
            filterType: 'combobox',
            id: 'ownerId',
            label: t`Evidence owner`,
            options,
            hasMore: hasNextPage,
            isLoading: isFetching && isLoading,
            onFetchOptions: onFetchUsers,
            placeholder: t`Search by owner`,
        };
    }

    get filters(): FilterProps {
        return {
            filters: [
                this.statusFilter,
                this.sourceFilter,
                this.evidenceOwnersFilter,
                this.frameworksFilter,
            ],
            triggerLabel: t`Filters`,
        };
    }
}
