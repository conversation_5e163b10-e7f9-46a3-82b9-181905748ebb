import { sharedRiskDetailsController } from '@controllers/risk-details';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    getRiskSourceLabel,
    getRiskStatusLabel,
} from '../../helpers/risk-label-mapper.helper';

export const RiskSourceStatusReadOnlyContent = observer(
    (): React.JSX.Element => {
        const { riskDetails } = sharedRiskDetailsController;

        if (!riskDetails) {
            return <EmptyValue label={t`No risk details available`} />;
        }

        return (
            <Stack
                direction="column"
                gap="md"
                data-testid="RiskSourceStatusReadOnlyContent"
                data-id="1W_Nvib6"
            >
                {riskDetails.type === 'EXTERNAL' ? (
                    <>
                        <KeyValuePair
                            label={t`Risk Source`}
                            type="REACT_NODE"
                            value={
                                <Text>
                                    {getRiskSourceLabel(riskDetails.type)}
                                </Text>
                            }
                        />
                        <KeyValuePair
                            label={t`Vendor`}
                            type="REACT_NODE"
                            value={
                                riskDetails.vendor ? (
                                    <Text>{riskDetails.vendor.name}</Text>
                                ) : (
                                    <EmptyValue label={t`No vendor selected`} />
                                )
                            }
                        />
                    </>
                ) : (
                    <KeyValuePair
                        label={t`Risk Source`}
                        type="REACT_NODE"
                        value={
                            <Text>{getRiskSourceLabel(riskDetails.type)}</Text>
                        }
                    />
                )}

                <KeyValuePair
                    label={t`Status`}
                    type="REACT_NODE"
                    value={
                        <Text>{getRiskStatusLabel(riskDetails.status)}</Text>
                    }
                />
            </Stack>
        );
    },
);
