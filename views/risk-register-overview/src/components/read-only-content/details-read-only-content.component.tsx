import { isEmpty } from 'lodash-es';
import { sharedRiskCustomFieldsSubmissionsController } from '@controllers/risk';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type {
    DocumentResponseDto,
    RiskCategoryResponseDto,
    RiskResponseDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { sharedCustomFieldsManager } from '@models/custom-fields';

interface DetailsReadOnlyContentProps {
    riskDetails: RiskResponseDto | null;
}

export const DetailsReadOnlyContent = observer(
    ({ riskDetails }: DetailsReadOnlyContentProps) => {
        const {
            riskCustomFieldsDetailsSection,
            isLoading: isCustomFieldsLoading,
        } = sharedRiskCustomFieldsSubmissionsController;
        const { isRiskManagementEnabled } = sharedFeatureAccessModel;

        return (
            <Stack direction="column" align="start" gap="xl" data-id="u83CYjlf">
                <KeyValuePair
                    label={t`Title`}
                    type="TEXT"
                    value={
                        riskDetails?.title ?? <EmptyValue label={t`No Title`} />
                    }
                />
                <KeyValuePair
                    label={t`Description`}
                    type="TEXT"
                    value={
                        riskDetails?.description ?? (
                            <EmptyValue label={t`No Description`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Risk identified date`}
                    type="REACT_NODE"
                    value={
                        riskDetails?.identifiedAt ? (
                            <Text type="body" size="200" data-id="ApDN3WJV">
                                {formatDate(
                                    'sentence',
                                    riskDetails.identifiedAt.split('T')[0],
                                )}
                            </Text>
                        ) : (
                            <EmptyValue label={t`No identified date`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Categories`}
                    type="TEXT"
                    value={
                        riskDetails?.categories
                            ? riskDetails.categories
                                  .map(
                                      (category: RiskCategoryResponseDto) =>
                                          category.name,
                                  )
                                  .join(', ')
                            : '-'
                    }
                />
                {isRiskManagementEnabled && (
                    <KeyValuePair
                        label={t`File`}
                        type="REACT_NODE"
                        value={
                            riskDetails?.documents &&
                            !isEmpty(riskDetails.documents) ? (
                                <Stack direction="column" gap="sm">
                                    {riskDetails.documents.map(
                                        (document: DocumentResponseDto) => (
                                            <Text
                                                key={document.id}
                                                type="body"
                                                size="200"
                                                data-id="ApDN3WJV"
                                            >
                                                {document.name}
                                            </Text>
                                        ),
                                    )}
                                </Stack>
                            ) : (
                                <EmptyValue label={t`No Documents`} />
                            )
                        }
                    />
                )}

                {/* Custom Fields */}
                {isCustomFieldsLoading ? (
                    <Skeleton />
                ) : (
                    sharedCustomFieldsManager.renderReadOnlyCustomFields(
                        riskCustomFieldsDetailsSection?.customFields ?? [],
                    )
                )}
            </Stack>
        );
    },
);
