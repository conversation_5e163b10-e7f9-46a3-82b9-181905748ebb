import { isError } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedRiskPartiallyMutationController } from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { snackbarController } from '@controllers/snackbar';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import {
    getSectionConfig,
    type RiskSection,
} from '../helpers/risk-configuration.helper';
import { RiskSectionEditForm } from './risk-section-edit-form.component';
import { RiskSectionReadOnlyCard } from './risk-section-read-only-card.component';

interface RiskSectionViewEditCardProps {
    section: RiskSection;
}

export const RiskSectionViewEditCard = observer(
    ({ section }: RiskSectionViewEditCardProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        // Get section-specific loading and error states
        const isPending =
            sharedRiskPartiallyMutationController.isSectionPending(section);
        const hasError =
            sharedRiskPartiallyMutationController.hasSectionError(section);

        const {
            hasRiskManagePermission,
            isRiskManagerWithRestrictedView,
            hasRiskReadPermission,
        } = sharedFeatureAccessModel;
        const { isUserInReadOnlyMode, isAuditorInReadOnlyMode } =
            sharedCurrentUserController;
        const { isRiskManagementEnabled, isVendorRiskManagementProEnabled } =
            sharedEntitlementFlagController;
        const { riskDetails, isLoading } = sharedRiskDetailsController;

        const config = getSectionConfig(section);

        const permissionChecks = useMemo(() => {
            const hasBasicAccess = hasRiskReadPermission;
            const hasEntitlementAccess =
                isRiskManagementEnabled || isVendorRiskManagementProEnabled;

            const isInReadOnlyMode =
                isUserInReadOnlyMode || isAuditorInReadOnlyMode;

            const isRestrictedView = isRiskManagerWithRestrictedView;

            const isDataReady = !isLoading && riskDetails !== null;

            return {
                hasBasicAccess,
                hasEntitlementAccess,
                isInReadOnlyMode,
                isRestrictedView,
                isDataReady,
            };
        }, [
            hasRiskReadPermission,
            isRiskManagementEnabled,
            isVendorRiskManagementProEnabled,
            isUserInReadOnlyMode,
            isAuditorInReadOnlyMode,
            isRiskManagerWithRestrictedView,
            isLoading,
            riskDetails,
        ]);

        const canEdit = useMemo(() => {
            const {
                hasBasicAccess,
                isInReadOnlyMode,
                isRestrictedView,
                isDataReady,
            } = permissionChecks;

            if (!hasBasicAccess) {
                return false;
            }

            if (isInReadOnlyMode) {
                return false;
            }

            if (!hasRiskManagePermission) {
                return false;
            }

            if (isRestrictedView) {
                return section === 'owners' || section === 'treatment';
            }

            return isDataReady;
        }, [permissionChecks, hasRiskManagePermission, section]);

        const handleSave = useCallback(() => {
            if (!canEdit) {
                snackbarController.addSnackbar({
                    id: 'risk-section-save-blocked',
                    props: {
                        title: t`Unable to save changes`,
                        description: t`You don't have permission to edit this section.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return;
            }

            triggerSubmit().catch((error) => {
                logger.error({
                    message: 'Failed to save risk section changes',
                    errorObject: isError(error)
                        ? { message: error.message, statusCode: 'unknown' }
                        : undefined,
                    additionalInfo: { error },
                });
                snackbarController.addSnackbar({
                    id: 'risk-section-save-error',
                    props: {
                        title: t`Unable to save changes`,
                        description: t`An error occurred while saving. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
        }, [triggerSubmit, canEdit]);

        const { editButtonLabel, title, testId } = config;

        return (
            <ViewEditCardComponent
                title={title}
                data-testid={testId}
                isMutationPending={isPending}
                hasMutationError={hasError}
                data-can-edit={canEdit}
                data-has-manage-permission={hasRiskManagePermission}
                data-is-restricted-view={permissionChecks.isRestrictedView}
                data-is-read-only={permissionChecks.isInReadOnlyMode}
                data-id="sUy3_5exe"
                size="lg"
                data-section={section}
                editButtonLabel={
                    canEdit ? (editButtonLabel ?? undefined) : undefined
                }
                data-has-entitlement-access={
                    permissionChecks.hasEntitlementAccess
                }
                editComponent={
                    canEdit ? (
                        <RiskSectionEditForm
                            section={section}
                            formRef={formRef}
                        />
                    ) : null
                }
                readOnlyComponent={
                    <RiskSectionReadOnlyCard section={section} />
                }
                onSave={handleSave}
            />
        );
    },
);
