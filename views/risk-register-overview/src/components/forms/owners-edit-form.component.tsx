import { isEmpty, isNil, isNumber, isString } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import {
    RISK_OWNER_ROLES,
    sharedRiskCustomFieldsSubmissionsController,
    sharedRiskPartiallyMutationController,
} from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Stack } from '@cosmos/components/stack';
import type { CustomFieldSubmissionRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { FormWrapper, UniversalRenderFields } from '@ui/forms';
import type { OwnersFormValues } from '../../types/form-data.types';

interface OwnersEditFormProps {
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit?: (values: OwnersFormValues) => void | Promise<void>;
}

export const OwnersEditForm = observer(
    ({ formRef, onSubmit }: OwnersEditFormProps): React.JSX.Element => {
        const { riskDetails } = sharedRiskDetailsController;
        const {
            riskCustomFieldsDetailsSection,
            riskCustomFieldsAssessmentSection,
            riskCustomFieldsTreatmentSection,
        } = sharedRiskCustomFieldsSubmissionsController;

        const {
            onFetchUsers,
            hasNextPage,
            isLoading: isLoadingUsers,
            isFetching,
            options: userOptions,
        } = sharedUsersInfiniteController;

        const handleSubmit = useCallback(
            (values: OwnersFormValues) => {
                action(() => {
                    if (!riskDetails) {
                        return;
                    }

                    // If external onSubmit is provided, use it (for backward compatibility)
                    if (onSubmit) {
                        onSubmit(values);

                        return;
                    }

                    // Transform owners to the format expected by the API
                    const owners =
                        values.owners?.map((owner) => ({
                            id: parseInt(owner.id, 10),
                        })) ?? [];

                    // Collect ALL custom fields from all sections for backend validation
                    const allCustomFields = [
                        ...(riskCustomFieldsDetailsSection?.customFields ?? []),
                        ...(riskCustomFieldsAssessmentSection?.customFields ??
                            []),
                        ...(riskCustomFieldsTreatmentSection?.customFields ??
                            []),
                    ];

                    // Extract custom fields data (empty for owners form, but required for API)
                    const customFieldsData =
                        sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                            values as Record<string, unknown>,
                            [], // No custom fields in owners form
                        );

                    const extractedData =
                        (
                            customFieldsData as {
                                customFieldSubmissions: {
                                    customFieldId: number;
                                    customFieldName: string;
                                    value: string | number | undefined;
                                    customFieldLocationId: number;
                                }[];
                            } | null
                        )?.customFieldSubmissions ?? [];

                    // Create a map of form submissions by customFieldId for easy lookup
                    const formSubmissionsMap = new Map(
                        extractedData.map((submission) => [
                            submission.customFieldId,
                            submission,
                        ]),
                    );

                    // Include ALL custom fields from ALL sections (preserving existing values)
                    const customFieldSubmissions = allCustomFields.map(
                        (field) => {
                            const formSubmission = formSubmissionsMap.get(
                                field.customFieldId,
                            );

                            const submissionData: Record<string, unknown> = {
                                customFieldId: field.customFieldId,
                                customFieldName: field.name,
                                customFieldLocationId:
                                    field.customFieldLocationId,
                                value:
                                    formSubmission?.value ??
                                    field.submission.value ??
                                    null,
                            };

                            // Include submissionId if it exists (for updates)
                            if (field.submission.submissionId) {
                                submissionData.submissionId =
                                    field.submission.submissionId;
                            }

                            return submissionData;
                        },
                    ) as CustomFieldSubmissionRequestDto[];

                    // Create customFields object for the API (required by backend)
                    const customFields: Record<string, string> = {};

                    for (const submission of customFieldSubmissions) {
                        const { value, customFieldId } = submission as {
                            value: unknown;
                            customFieldId: number;
                        };

                        customFields[`field-${customFieldId}`] =
                            !isNil(value) &&
                            (isString(value) || isNumber(value))
                                ? String(value)
                                : '';
                    }

                    // Use the controller to update risk owners
                    sharedRiskPartiallyMutationController.updateRiskPartiallyDetails(
                        riskDetails.riskId,
                        {
                            owners,
                            ...(!isEmpty(customFieldSubmissions) && {
                                customFieldSubmissions,
                                customFields,
                            }),
                        },
                        'owners',
                    );

                    // Reload custom fields data after successful update
                    if (!isEmpty(customFieldSubmissions)) {
                        when(
                            () =>
                                !sharedRiskPartiallyMutationController.isPending,
                            () => {
                                if (
                                    !sharedRiskPartiallyMutationController.hasError
                                ) {
                                    sharedRiskCustomFieldsSubmissionsController.load(
                                        riskDetails.id,
                                    );
                                }
                            },
                        );
                    }
                })();
            },
            [
                riskDetails,
                onSubmit,
                riskCustomFieldsDetailsSection,
                riskCustomFieldsAssessmentSection,
                riskCustomFieldsTreatmentSection,
            ],
        );

        // Create form schema for owners field
        const formSchema = useMemo(() => {
            // Transform current owners to form format
            const currentOwners =
                riskDetails?.owners.map((owner) => ({
                    id: String(owner.id),
                    label: `${owner.firstName} ${owner.lastName}`,
                    value: String(owner.id),
                    description: owner.email,
                    avatar: {
                        fallbackText: `${owner.firstName.charAt(0)}${owner.lastName.charAt(0)}`,
                        imgSrc: owner.avatarUrl || undefined,
                        imgAlt: `${owner.firstName} ${owner.lastName}`,
                    },
                })) ?? [];

            return {
                owners: {
                    type: 'combobox' as const,
                    label: t`Risk owners`,
                    helpText: t`Select users who will be responsible for managing this risk`,
                    isMultiSelect: true,
                    isOptional: true,
                    initialValue: currentOwners,
                    options: userOptions,
                    hasMore: hasNextPage,
                    isLoading: isLoadingUsers || isFetching,
                    loaderLabel: t`Loading users...`,
                    onFetchOptions: (params: {
                        search?: string;
                        increasePage?: boolean;
                    }) => {
                        onFetchUsers({
                            ...params,
                            roles: RISK_OWNER_ROLES,
                            excludeReadOnlyUsers: true,
                        });
                    },
                    placeholder: t`Search by name`,
                    getSearchEmptyState: () => t`No users found`,
                    removeAllSelectedItemsLabel: t`Remove all owners`,
                    getRemoveIndividualSelectedItemClickLabel: ({
                        itemLabel,
                    }: {
                        itemLabel: string;
                    }) => t`Remove ${itemLabel}`,
                },
            };
        }, [
            riskDetails?.owners,
            userOptions,
            hasNextPage,
            isLoadingUsers,
            onFetchUsers,
        ]);

        return (
            <FormWrapper
                ref={formRef}
                schema={formSchema}
                data-id="owners-edit-form"
                formId="owners-edit-form"
                onSubmit={handleSubmit}
            >
                <Stack direction="column" gap="4x">
                    <UniversalRenderFields
                        fields={formSchema}
                        formId="owners-edit-form"
                        data-id="owners-edit-form-fields"
                    />
                </Stack>
            </FormWrapper>
        );
    },
);
