import { isEmpty } from 'lodash-es';
import { use<PERSON>allback, useMemo, useRef } from 'react';
import {
    sharedRiskCustomFieldsSubmissionsController,
    sharedRiskPartiallyMutationController,
} from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import { FileUploadField } from '@cosmos/components/file-upload-field';
import { Stack } from '@cosmos/components/stack';
import type { CustomFieldSubmissionRequestDto } from '@globals/api-sdk/types';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import {
    UPLOAD_FILES_ERROR_CODE_MESSAGES,
    UPLOAD_FILES_SUPPORTED_FORMATS,
} from '@helpers/upload-file';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { <PERSON><PERSON>rap<PERSON>, UniversalRenderFields } from '@ui/forms';
import { handleDocumentUpload } from '../../helpers/document-upload.helper';
import { convertDocumentsToCosmosFiles } from '../../helpers/risk-data-transformations.helper';
import { RISK_FORM_LIMITS } from '../../helpers/risk-form-schemas.helper';
import { sharedRiskDetailsOverviewModel } from '../../lib/models/risk-details-overview.model';
import type { DetailsFormValues } from '../../types/form-data.types';

interface DetailsEditFormProps {
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit?: (values: DetailsFormValues) => void | Promise<void>;
}

export const DetailsEditForm = observer(
    ({ formRef, onSubmit }: DetailsEditFormProps): React.JSX.Element => {
        const { riskDetails } = sharedRiskDetailsController;
        const { isRiskManagementEnabled } = sharedEntitlementFlagController;
        const {
            riskCustomFieldsDetailsSection,
            riskCustomFieldsAssessmentSection,
            riskCustomFieldsTreatmentSection,
        } = sharedRiskCustomFieldsSubmissionsController;

        // Track previous files to detect deletions
        const previousFilesRef = useRef<CosmosFileObject[]>([]);

        /**
         * Handle document upload and deletion using shared helper.
         */
        const handleDocumentUploadWrapper = (
            cosmosFiles: CosmosFileObject[],
        ) => {
            if (!riskDetails?.riskId) {
                return;
            }

            handleDocumentUpload(
                riskDetails.riskId,
                cosmosFiles,
                previousFilesRef,
            );
        };

        const handleSubmit = useCallback(
            (values: DetailsFormValues) => {
                action(() => {
                    if (!riskDetails) {
                        return;
                    }

                    // If external onSubmit is provided, use it (for backward compatibility)
                    if (onSubmit) {
                        onSubmit(values);

                        return;
                    }

                    // Collect ALL custom fields from all sections
                    // The backend validates that all required custom fields are present across all sections
                    const allCustomFields = [
                        ...(riskCustomFieldsDetailsSection?.customFields ?? []),
                        ...(riskCustomFieldsAssessmentSection?.customFields ??
                            []),
                        ...(riskCustomFieldsTreatmentSection?.customFields ??
                            []),
                    ];

                    // Extract and prepare custom fields data from details form
                    const customFieldsData =
                        sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                            values as Record<string, unknown>,
                            riskCustomFieldsDetailsSection?.customFields ?? [],
                        );

                    // Transform custom field submissions to match API expected structure
                    const extractedData =
                        (
                            customFieldsData as {
                                customFieldSubmissions: {
                                    customFieldId: number;
                                    customFieldName: string;
                                    value: string | number | undefined;
                                    customFieldLocationId: number;
                                }[];
                            } | null
                        )?.customFieldSubmissions ?? [];

                    // Create a map of form submissions by customFieldId for easy lookup
                    const formSubmissionsMap = new Map(
                        extractedData.map((submission) => [
                            submission.customFieldId,
                            submission,
                        ]),
                    );

                    // Include ALL custom fields from ALL sections, not just details
                    // The backend validates that all required custom fields are present
                    const customFieldSubmissions = allCustomFields.map(
                        (field) => {
                            // Get form submission data if it exists (only for details fields)
                            const formSubmission = formSubmissionsMap.get(
                                field.customFieldId,
                            );

                            // Extract the actual value from the submission object
                            // The API SDK types are incorrectly generated, so we need to cast
                            const submission = field.submission as unknown as {
                                value?: string | number | null;
                                submissionId?: number | null;
                            };
                            const submissionValue = submission.value ?? null;
                            const submissionId =
                                submission.submissionId ?? null;

                            const submissionData: CustomFieldSubmissionRequestDto =
                                {
                                    customFieldId: field.customFieldId,
                                    customFieldName: field.name,
                                    customFieldLocationId:
                                        field.customFieldLocationId,
                                    // Use form value for details fields, preserve existing value for others
                                    // The API expects { [key: string]: unknown } but actually wants string | number | null
                                    value: (formSubmission?.value ??
                                        submissionValue ??
                                        null) as {
                                        [key: string]: unknown;
                                    } | null,
                                };

                            // Include submissionId if it exists (for updates)
                            if (submissionId) {
                                submissionData.submissionId = submissionId;
                            }

                            return submissionData;
                        },
                    );

                    // Create customFields object for the API (required by backend)
                    const customFieldsForApi: Record<string, string> = {};

                    extractedData.forEach((submission) => {
                        const fieldKey = `field-${submission.customFieldId}`;

                        customFieldsForApi[fieldKey] = String(
                            submission.value ?? '',
                        );
                    });

                    // Compare categories to detect changes
                    const initialCategories = riskDetails.categories;
                    const currentCategories = values.categories ?? [];

                    const initialCategoryIds = new Set(
                        initialCategories.map((cat) => String(cat.id)),
                    );
                    const currentCategoryIds = new Set(
                        currentCategories.map((cat: { id: string }) => cat.id),
                    );

                    const isCategoriesEmpty =
                        initialCategoryIds.size === 0 &&
                        currentCategoryIds.size === 0;
                    const categoriesChanged =
                        !isCategoriesEmpty &&
                        (initialCategoryIds.size !== currentCategoryIds.size ||
                            [...initialCategoryIds].some(
                                (id) => !currentCategoryIds.has(id),
                            ) ||
                            [...currentCategoryIds].some(
                                (id) => !initialCategoryIds.has(id),
                            ));

                    // Build update payload
                    const updatePayload: {
                        title: string;
                        description: string;
                        identifiedAt: string | null;
                        categories?: { id: number; name: string }[];
                        customFieldSubmissions?: CustomFieldSubmissionRequestDto[];
                        customFields?: Record<string, string>;
                    } = {
                        title: values.title || '',
                        description: values.description || '',
                        identifiedAt: values.identifiedAt || null,
                    };

                    // Include categories only if changed and not empty
                    if (categoriesChanged && !isEmpty(currentCategories)) {
                        updatePayload.categories = currentCategories.map(
                            (category: { id: string; label: string }) => ({
                                id: parseInt(category.id, 10),
                                name: category.label,
                            }),
                        );
                    }

                    // Always include custom fields if any exist in the backend (even if empty)
                    // The backend validation requires ALL custom fields to be present
                    if (!isEmpty(allCustomFields)) {
                        updatePayload.customFieldSubmissions =
                            customFieldSubmissions;
                        updatePayload.customFields = customFieldsForApi;
                    }

                    // Use the controller directly
                    sharedRiskPartiallyMutationController.updateRiskPartiallyDetails(
                        riskDetails.riskId,
                        updatePayload,
                        'details',
                    );

                    // Reload custom fields data after successful submission
                    when(
                        () => !sharedRiskPartiallyMutationController.isPending,
                        () => {
                            if (
                                !sharedRiskPartiallyMutationController.hasError
                            ) {
                                // Reload custom fields submissions to reflect the changes
                                sharedRiskCustomFieldsSubmissionsController.load(
                                    riskDetails.id,
                                );
                            }
                        },
                    );
                })();
            },
            [
                riskDetails,
                onSubmit,
                riskCustomFieldsDetailsSection,
                riskCustomFieldsAssessmentSection,
                riskCustomFieldsTreatmentSection,
            ],
        );

        // Convert existing documents to CosmosFileObjects
        const initialFiles = useMemo(() => {
            const documents = riskDetails?.documents;

            if (!documents?.length) {
                return [];
            }

            const files = convertDocumentsToCosmosFiles(documents);

            // Initialize previousFilesRef directly in the memo
            previousFilesRef.current = [...files];

            return files;
        }, [riskDetails]);

        const formSchema = useMemo(() => {
            return sharedRiskDetailsOverviewModel.getDetailsFormSchema(
                riskDetails,
                riskCustomFieldsDetailsSection?.customFields,
            );
        }, [riskDetails, riskCustomFieldsDetailsSection]);

        const { mainFields, customFields } = useMemo(() => {
            const main: typeof formSchema = {};
            const custom: typeof formSchema = {};

            Object.entries(formSchema).forEach(([key, field]) => {
                if (key.startsWith('customField_')) {
                    custom[key] = field;
                } else {
                    main[key] = field;
                }
            });

            return { mainFields: main, customFields: custom };
        }, [formSchema]);

        return (
            <FormWrapper
                data-id="details-edit-form"
                formId="details-edit-form"
                schema={formSchema}
                ref={formRef}
                onSubmit={handleSubmit}
            >
                <Stack direction="column" gap="lg">
                    <UniversalRenderFields
                        data-id="details-edit-form-main-fields"
                        fields={mainFields}
                        formId="details-edit-form"
                    />

                    {isRiskManagementEnabled && (
                        <FileUploadField
                            isMulti
                            label={t`File`}
                            name="documents"
                            formId="risk-details-form"
                            initialFiles={initialFiles}
                            selectButtonText={t`Upload files`}
                            removeButtonText={t`Remove file`}
                            innerLabel={t`Or drop files here`}
                            errorCodeMessages={UPLOAD_FILES_ERROR_CODE_MESSAGES}
                            maxFileSizeInBytes={RISK_FORM_LIMITS.FILE_MAX_SIZE}
                            acceptedFormats={UPLOAD_FILES_SUPPORTED_FORMATS}
                            onUpdate={handleDocumentUploadWrapper}
                        />
                    )}

                    {!isEmpty(Object.keys(customFields)) && (
                        <UniversalRenderFields
                            data-id="details-edit-form-custom-fields"
                            fields={customFields}
                            formId="details-edit-form"
                        />
                    )}
                </Stack>
            </FormWrapper>
        );
    },
);
