import { isEmpty, isNil, isNumber, isString } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import {
    sharedRiskCustomFieldsSubmissionsController,
    sharedRiskPartiallyMutationController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Stack } from '@cosmos/components/stack';
import type { CustomFieldSubmissionRequestDto } from '@globals/api-sdk/types';
import { action, observer, when } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { FormWrapper, UniversalRenderFields } from '@ui/forms';
import { calculateRiskScore } from '../../helpers/risk-calculation.helper';
import { transformSelectToNumber } from '../../helpers/risk-data-transformation.helper';
import { getTreatmentFormSchema } from '../../helpers/risk-form-schemas.helper';
import type {
    RiskTreatmentPlanValue,
    TreatmentFormValues,
} from '../../types/form-data.types';
import { ConditionalResidualScoreSection } from './conditional-residual-score-section.component';

interface TreatmentEditFormProps {
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit?: (values: TreatmentFormValues) => void | Promise<void>;
}

export const TreatmentEditForm = observer(
    ({ formRef, onSubmit }: TreatmentEditFormProps): React.JSX.Element => {
        const { riskDetails } = sharedRiskDetailsController;
        const { riskSettings } = sharedRiskSettingsController;

        // Initialize users controller on mount for reviewers field
        useMemo(() => {
            sharedUsersInfiniteController.onFetchUsers({});
        }, []);

        const {
            riskCustomFieldsTreatmentSection,
            riskCustomFieldsAssessmentSection,
            riskCustomFieldsDetailsSection,
        } = sharedRiskCustomFieldsSubmissionsController;

        const handleSubmit = useCallback(
            (values: TreatmentFormValues) => {
                action(() => {
                    if (!riskDetails) {
                        return;
                    }

                    // Transform the select objects to numbers/null for residual impact and likelihood
                    const transformedResidualImpact = transformSelectToNumber(
                        values.residualImpact,
                    );
                    const transformedResidualLikelihood =
                        transformSelectToNumber(values.residualLikelihood);

                    // If external onSubmit is provided, use it (for backward compatibility)
                    if (onSubmit) {
                        onSubmit(values);

                        return;
                    }

                    const residualScore = calculateRiskScore(
                        transformedResidualImpact ?? null,
                        transformedResidualLikelihood ?? null,
                    );

                    const allCustomFields = [
                        ...(riskCustomFieldsDetailsSection?.customFields ?? []),
                        ...(riskCustomFieldsAssessmentSection?.customFields ??
                            []),
                        ...(riskCustomFieldsTreatmentSection?.customFields ??
                            []),
                    ];

                    const customFieldsData =
                        sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                            values as Record<string, unknown>,
                            riskCustomFieldsTreatmentSection?.customFields ??
                                [],
                        );

                    const extractedData =
                        (
                            customFieldsData as {
                                customFieldSubmissions: {
                                    customFieldId: number;
                                    customFieldName: string;
                                    value: string | number | undefined;
                                    customFieldLocationId: number;
                                }[];
                            } | null
                        )?.customFieldSubmissions ?? [];

                    // Create a map of form submissions by customFieldId for easy lookup
                    const formSubmissionsMap = new Map(
                        extractedData.map((submission) => [
                            submission.customFieldId,
                            submission,
                        ]),
                    );

                    const customFieldSubmissions = allCustomFields.map(
                        (field) => {
                            const formSubmission = formSubmissionsMap.get(
                                field.customFieldId,
                            );

                            const submissionData: Record<string, unknown> = {
                                customFieldId: field.customFieldId,
                                customFieldName: field.name,
                                customFieldLocationId:
                                    field.customFieldLocationId,
                                value:
                                    formSubmission?.value ??
                                    field.submission.value ??
                                    null,
                            };

                            // Include submissionId if it exists (for updates)
                            if (field.submission.submissionId) {
                                submissionData.submissionId =
                                    field.submission.submissionId;
                            }

                            return submissionData;
                        },
                    ) as CustomFieldSubmissionRequestDto[];

                    // Create customFields object for the API (required by backend)
                    const customFields: Record<string, string> = {};

                    for (const submission of customFieldSubmissions) {
                        const { value, customFieldId } = submission as {
                            value: unknown;
                            customFieldId: number;
                        };

                        customFields[`field-${customFieldId}`] =
                            !isNil(value) &&
                            (isString(value) || isNumber(value))
                                ? String(value)
                                : '';
                    }

                    const reviewerIds = (values.reviewers ?? []).map(
                        (reviewer) => ({
                            id: Number(reviewer.id),
                        }),
                    );

                    // Use the controller directly
                    sharedRiskPartiallyMutationController.updateRiskPartiallyDetails(
                        riskDetails.riskId,
                        {
                            treatmentPlan: values.treatmentPlan?.value as
                                | RiskTreatmentPlanValue
                                | undefined,
                            treatmentDetails: values.treatmentDetails,
                            residualImpact: transformedResidualImpact ?? null,
                            residualLikelihood:
                                transformedResidualLikelihood ?? null,
                            residualScore: residualScore ?? null,
                            anticipatedCompletionDate: values.treatmentDueDate,
                            completionDate: values.treatmentCompletionDate,
                            reviewers: reviewerIds,
                            ...(!isEmpty(customFieldSubmissions) && {
                                customFieldSubmissions,
                                customFields,
                            }),
                        },
                        'treatment',
                    );

                    // Reload custom fields data after successful update
                    if (!isEmpty(customFieldSubmissions)) {
                        when(
                            () =>
                                !sharedRiskPartiallyMutationController.isPending,
                            () => {
                                if (
                                    !sharedRiskPartiallyMutationController.hasError
                                ) {
                                    sharedRiskCustomFieldsSubmissionsController.load(
                                        riskDetails.id,
                                    );
                                }
                            },
                        );
                    }
                })();
            },
            [
                riskDetails,
                onSubmit,
                riskCustomFieldsTreatmentSection,
                riskCustomFieldsAssessmentSection,
                riskCustomFieldsDetailsSection,
            ],
        );

        const formSchema = useMemo(() => {
            return getTreatmentFormSchema(
                riskDetails,
                riskSettings,
                riskCustomFieldsTreatmentSection?.customFields,
            );
        }, [
            riskDetails,
            riskSettings,
            riskCustomFieldsTreatmentSection,
            // Add users controller state to dependencies to force schema regeneration
            sharedUsersInfiniteController.options.length,
            sharedUsersInfiniteController.isLoading,
        ]);

        // Separate fields by their intended order
        const {
            treatmentPlan,
            treatmentDetails,
            residualImpact,
            residualLikelihood,
            treatmentDueDate,
            treatmentCompletionDate,
            reviewers,
            ...otherFields
        } = formSchema;

        // Group fields in the desired order
        const topFields = { treatmentPlan, treatmentDetails };
        const residualFields = { residualImpact, residualLikelihood };
        const bottomFields = {
            treatmentDueDate,
            treatmentCompletionDate,
            reviewers,
        };

        return (
            <FormWrapper
                ref={formRef}
                schema={formSchema}
                data-id="treatment-edit-form"
                formId="treatment-edit-form"
                onSubmit={handleSubmit}
            >
                <Stack direction="column" gap="4x">
                    <UniversalRenderFields
                        fields={topFields}
                        formId="treatment-edit-form"
                        data-id="treatment-edit-form-top-fields"
                    />

                    <ConditionalResidualScoreSection
                        residualFields={residualFields}
                    />

                    <UniversalRenderFields
                        fields={bottomFields}
                        formId="treatment-edit-form"
                        data-id="treatment-edit-form-bottom-fields"
                    />

                    {!isEmpty(Object.keys(otherFields)) && (
                        <UniversalRenderFields
                            fields={otherFields}
                            formId="treatment-edit-form"
                            data-id="treatment-edit-form-other-fields"
                        />
                    )}
                </Stack>
            </FormWrapper>
        );
    },
);
