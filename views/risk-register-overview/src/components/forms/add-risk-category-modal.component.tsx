import { useCallback, useMemo } from 'react';
import { z } from 'zod';
import { sharedRiskCategoriesController } from '@controllers/risk';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { useLingui } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues as BaseFormValues,
    useFormSubmit,
} from '@ui/forms';
import { closeAddRiskCategoryModal } from './helpers/open-add-risk-category-modal.helper';

interface AddRiskCategoryModalProps {
    'data-id': string;
    onCategoryAdded?: (
        newCategory: ListBoxItemData,
        currentValue: unknown,
    ) => void;
    currentValue?: unknown;
}

const FORM_ID = 'add-risk-category-form';

export const AddRiskCategoryModal = observer(
    ({
        'data-id': dataId,
        onCategoryAdded,
        currentValue,
    }: AddRiskCategoryModalProps): React.JSX.Element => {
        const { t } = useLingui();
        const { formRef, triggerSubmit } = useFormSubmit();

        const { isCreatingCategory, createCategoryMutation, addCategory } =
            sharedRiskCategoriesController;

        const handleSubmit = useCallback(
            async (values: BaseFormValues) => {
                const { categoryName } = values as { categoryName: string };

                try {
                    // Use the Promise-based approach to get the actual response
                    await addCategory(categoryName);

                    // At this point, the mutation should be complete and response should be available
                    const newCategoryResponse = createCategoryMutation.response;

                    if (
                        createCategoryMutation.hasError ||
                        !newCategoryResponse
                    ) {
                        logger.error(
                            'Error creating category or no response received',
                        );

                        snackbarController.addSnackbar({
                            id: 'fail-create-category',
                            props: {
                                title: t`Failed to add risk category`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return;
                    }

                    if (onCategoryAdded) {
                        // Convert API response to ListBoxItemData format
                        const newCategory: ListBoxItemData = {
                            id: newCategoryResponse.id.toString(),
                            label: newCategoryResponse.name,
                            value: newCategoryResponse.id.toString(),
                        };

                        // Call the callback to update the form field
                        onCategoryAdded(newCategory, currentValue);
                    }

                    closeAddRiskCategoryModal();
                } catch (error) {
                    logger.error({
                        message: 'Failed to create category',
                        additionalInfo: { error },
                    });
                    snackbarController.addSnackbar({
                        id: 'fail-create-category',
                        props: {
                            title: t`Failed to add risk category`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
            [
                addCategory,
                onCategoryAdded,
                currentValue,
                createCategoryMutation.response,
                createCategoryMutation.hasError,
            ],
        );

        const handleAddClick = useCallback(() => {
            triggerSubmit().catch(() => {
                snackbarController.addSnackbar({
                    id: 'form-submit-error',
                    props: {
                        title: t`Form submission failed`,
                        description: t`Please check the form and try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
        }, [triggerSubmit, t]);

        const formSchema = useMemo(
            () =>
                ({
                    categoryName: {
                        type: 'text',
                        label: t`Category name`,
                        validator: z
                            .string()
                            .min(1, { message: t`Category name is required` })
                            .max(191, {
                                message: t`Category name must be 191 characters or less`,
                            }),
                        initialValue: '',
                    },
                }) as const satisfies FormSchema,
            [t],
        );

        const rightActionStack = useMemo(
            () => [
                {
                    label: t`Cancel`,
                    level: 'secondary' as const,
                    onClick: closeAddRiskCategoryModal,
                    isDisabled: isCreatingCategory,
                },
                {
                    label: t`Add category`,
                    level: 'primary' as const,
                    onClick: handleAddClick,
                    isLoading: isCreatingCategory,
                    isDisabled: isCreatingCategory,
                },
            ],
            [t, handleAddClick, isCreatingCategory],
        );

        return (
            <Modal data-id={dataId} onClose={closeAddRiskCategoryModal}>
                <Modal.Header
                    title={t`Add risk category`}
                    closeButtonAriaLabel={t`Close`}
                    onClose={closeAddRiskCategoryModal}
                />
                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId={FORM_ID}
                        schema={formSchema}
                        data-id="add-risk-category-form"
                        onSubmit={handleSubmit}
                    />
                </Modal.Body>
                <Modal.Footer rightActionStack={rightActionStack} />
            </Modal>
        );
    },
);
