import { isEmpty, isNil, isNumber, isString } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import {
    sharedRiskCustomFieldsSubmissionsController,
    sharedRiskPartiallyMutationController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Icon } from '@cosmos/components/icon';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { t } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { type FormSchema, FormWrapper, UniversalRenderFields } from '@ui/forms';
import { calculateRiskScore } from '../../helpers/risk-calculation.helper';
import { transformSelectToNumber } from '../../helpers/risk-data-transformation.helper';
import { getAssessmentFormSchema } from '../../helpers/risk-form-schemas.helper';
import type { AssessmentFormValues } from '../../types/form-data.types';
import { InherentScoreDisplay } from './inherent-score-display.component.tsx';

interface AssessmentEditFormProps {
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit?: (values: AssessmentFormValues) => void | Promise<void>;
}

export const AssessmentEditForm = observer(
    ({ formRef, onSubmit }: AssessmentEditFormProps): React.JSX.Element => {
        const { riskDetails } = sharedRiskDetailsController;
        const { riskSettings } = sharedRiskSettingsController;
        const {
            riskCustomFieldsAssessmentSection,
            riskCustomFieldsDetailsSection,
            riskCustomFieldsTreatmentSection,
        } = sharedRiskCustomFieldsSubmissionsController;

        const handleSubmit = useCallback(
            (values: AssessmentFormValues) => {
                action(() => {
                    if (!riskDetails) {
                        return;
                    }

                    const impact = transformSelectToNumber(values.impact);
                    const likelihood = transformSelectToNumber(
                        values.likelihood,
                    );

                    if (onSubmit) {
                        onSubmit(values);

                        return;
                    }

                    const score = calculateRiskScore(impact, likelihood);

                    const allCustomFields = [
                        ...(riskCustomFieldsDetailsSection?.customFields ?? []),
                        ...(riskCustomFieldsAssessmentSection?.customFields ??
                            []),
                        ...(riskCustomFieldsTreatmentSection?.customFields ??
                            []),
                    ];

                    const customFieldsData =
                        sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                            values as Record<string, unknown>,
                            riskCustomFieldsAssessmentSection?.customFields ??
                                [],
                        );

                    const extractedData =
                        (
                            customFieldsData as {
                                customFieldSubmissions: {
                                    customFieldId: number;
                                    customFieldName: string;
                                    value: string | number | undefined;
                                    customFieldLocationId: number;
                                }[];
                            } | null
                        )?.customFieldSubmissions ?? [];

                    const formSubmissionsMap = new Map(
                        extractedData.map((submission) => [
                            submission.customFieldId,
                            submission,
                        ]),
                    );

                    const customFieldSubmissions = allCustomFields.map(
                        (field) => {
                            const formSubmission = formSubmissionsMap.get(
                                field.customFieldId,
                            );

                            const submissionData: Record<string, unknown> = {
                                customFieldId: field.customFieldId,
                                customFieldName: field.name,
                                customFieldLocationId:
                                    field.customFieldLocationId,
                                value:
                                    formSubmission?.value ??
                                    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- submission can be undefined when no existing submission exists
                                    field.submission?.value ??
                                    null,
                            };

                            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- submission can be undefined when no existing submission exists
                            if (field.submission?.submissionId) {
                                submissionData.submissionId =
                                    field.submission.submissionId;
                            }

                            return submissionData;
                        },
                    );

                    const customFieldsMap: Record<string, string> = {};

                    for (const submission of customFieldSubmissions) {
                        const { value, customFieldId } = submission;

                        customFieldsMap[`field-${customFieldId as number}`] =
                            !isNil(value) &&
                            (isString(value) || isNumber(value))
                                ? String(value)
                                : '';
                    }

                    const updatePayload: Record<string, unknown> = {
                        impact: impact ?? null,
                        likelihood: likelihood ?? null,
                        score: score ?? null,
                    };

                    if (!isEmpty(allCustomFields)) {
                        updatePayload.customFieldSubmissions =
                            customFieldSubmissions;
                        updatePayload.customFields = customFieldsMap;
                    }

                    sharedRiskPartiallyMutationController.updateRiskPartiallyDetails(
                        riskDetails.riskId,
                        updatePayload,
                        'assessment',
                    );

                    when(
                        () => !sharedRiskPartiallyMutationController.isPending,
                        () => {
                            if (
                                !sharedRiskPartiallyMutationController.hasError
                            ) {
                                sharedRiskCustomFieldsSubmissionsController.load(
                                    riskDetails.id,
                                );
                            }
                        },
                    );
                })();
            },
            [
                riskDetails,
                onSubmit,
                riskCustomFieldsDetailsSection?.customFields,
                riskCustomFieldsAssessmentSection?.customFields,
                riskCustomFieldsTreatmentSection?.customFields,
            ],
        );

        const formSchema = useMemo(() => {
            return getAssessmentFormSchema(
                riskDetails,
                riskSettings,
                riskCustomFieldsAssessmentSection?.customFields,
            );
        }, [riskDetails, riskSettings, riskCustomFieldsAssessmentSection]);

        const { standardFields, customFields } = useMemo(() => {
            const standardFieldKeys = ['impact', 'likelihood'];
            const standard: FormSchema = {};
            const custom: FormSchema = {};

            Object.entries(formSchema).forEach(([key, value]) => {
                if (standardFieldKeys.includes(key)) {
                    standard[key] = value;
                } else {
                    custom[key] = value;
                }
            });

            return {
                standardFields: standard,
                customFields: custom,
            };
        }, [formSchema]);

        return (
            <FormWrapper
                ref={formRef}
                schema={formSchema}
                data-id="assessment-edit-form"
                formId="assessment-edit-form"
                onSubmit={handleSubmit}
            >
                <Stack direction="column" gap="4x">
                    <Grid columns="2" gap="lg">
                        <UniversalRenderFields
                            fields={standardFields}
                            formId="assessment-edit-form"
                            data-id="assessment-edit-form-standard-fields"
                        />
                    </Grid>

                    <KeyValuePair
                        type="REACT_NODE"
                        label={t`Inherent score`}
                        value={<InherentScoreDisplay />}
                    />
                    <Box>
                        <Tooltip
                            text={t`This is the calculated risk score before any controls are in place, calculated from your Inherent Impact and Likelihood.`}
                            data-id="assessment-score-tooltip"
                        >
                            <Stack direction="row" align="center" gap="xs">
                                <Text
                                    type="title"
                                    size="100"
                                    colorScheme="neutral"
                                >
                                    {t`What does this score mean?`}
                                </Text>
                                <Icon name="Help" colorScheme="neutral" />
                            </Stack>
                        </Tooltip>
                    </Box>

                    {!isEmpty(Object.keys(customFields)) && (
                        <Stack direction="column" gap="md">
                            <UniversalRenderFields
                                fields={customFields}
                                formId="assessment-edit-form"
                                data-id="assessment-edit-form-custom-fields"
                            />
                        </Stack>
                    )}
                </Stack>
            </FormWrapper>
        );
    },
);
