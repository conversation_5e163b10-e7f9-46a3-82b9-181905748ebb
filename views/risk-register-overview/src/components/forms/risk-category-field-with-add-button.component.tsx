import { useCallback, useEffect, useMemo, useRef } from 'react';
import { sharedRiskCategoriesController } from '@controllers/risk';
import { Button } from '@cosmos/components/button';
import {
    Combobox,
    type ComboboxImperativeHandle,
} from '@cosmos/components/combobox';
import { FormField } from '@cosmos/components/form-field';
import { Grid } from '@cosmos/components/grid';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { CustomFieldRenderProps } from '@ui/forms';
import { openAddRiskCategoryModal } from './helpers/open-add-risk-category-modal.helper';

export const CategoryFieldWithAddButton = observer(
    ({
        formId,
        name,
        'data-id': dataId,
        onChange,
        value,
    }: CustomFieldRenderProps): React.JSX.Element => {
        const { isLoading, hasNextPage, onFetchCategories } =
            sharedRiskCategoriesController;

        // Create ref for imperative control of the Combobox
        const comboboxRef = useRef<ComboboxImperativeHandle | null>(null);

        // Initialize categories on component mount
        useEffect(() => {
            sharedRiskCategoriesController.initialize();
        }, []);

        const options = useMemo(() => {
            // Force reactivity by accessing the categories directly
            const { categories } = sharedRiskCategoriesController;

            return categories.map((category) => ({
                id: String(category.id),
                label: category.name,
                value: String(category.id),
            }));
        }, [sharedRiskCategoriesController.categories]);

        const selectedOptions = useMemo((): ListBoxItemData[] => {
            if (!value || !Array.isArray(value)) {
                return [];
            }

            return value as ListBoxItemData[];
        }, [value]);

        const handleAddCategory = useCallback(() => {
            openAddRiskCategoryModal({
                currentValue: value,
                onCategoryAdded: (newCategory, currentValue) => {
                    // Add the new category to the current selection
                    const currentArray = Array.isArray(currentValue)
                        ? currentValue
                        : [];
                    const updatedValue = [...currentArray, newCategory];

                    // Update both the form value and the combobox internal state
                    onChange(updatedValue);
                    comboboxRef.current?.setSelectedItems(updatedValue);
                },
            });
        }, [value, onChange]);

        return (
            <FormField
                label={t`Categories`}
                layout="stack"
                formId={formId}
                name={name}
                optionalText="Optional"
                helpText="Search for a category or add new ones in Manage categories."
                data-id={dataId}
                renderInput={({
                    describeIds,
                    inputId,
                    inputTestId,
                    labelId,
                    feedbackType,
                }) => (
                    <Grid
                        columns="1fr auto"
                        gap="md"
                        align="start"
                        data-id="risk-category-field-grid"
                    >
                        <Combobox
                            isMultiSelect
                            aria-describedby={describeIds}
                            aria-labelledby={labelId}
                            data-id={inputTestId}
                            id={inputId}
                            name={name}
                            options={options}
                            defaultSelectedOptions={selectedOptions}
                            imperativeHandleRef={comboboxRef}
                            hasMore={hasNextPage}
                            isLoading={isLoading}
                            loaderLabel={t`Loading categories...`}
                            getSearchEmptyState={() => t`No categories found`}
                            removeAllSelectedItemsLabel={t`Clear all`}
                            feedbackType={feedbackType}
                            onFetchOptions={onFetchCategories}
                            onChange={onChange}
                        />
                        <Button
                            label={t`Add categories`}
                            level="secondary"
                            colorScheme="neutral"
                            data-id="add-category-button"
                            onClick={handleAddCategory}
                        />
                    </Grid>
                )}
            />
        );
    },
);
