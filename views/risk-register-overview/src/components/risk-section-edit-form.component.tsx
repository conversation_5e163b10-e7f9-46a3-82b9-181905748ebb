import { Skeleton } from '@cosmos/components/skeleton';
import { observer } from '@globals/mobx';
import type { RiskSection } from '../helpers/risk-configuration.helper';
import type {
    AssessmentFormValues,
    DetailsFormValues,
    OwnersFormValues,
    TreatmentFormValues,
} from '../types/form-data.types';
import { AssessmentEditForm } from './forms/assessment-edit-form.component';
import { DetailsEditForm } from './forms/details-edit-form.component';
import { OwnersEditForm } from './forms/owners-edit-form.component';
import { RiskSourceStatusEditForm } from './forms/risk-source-status-edit-form.component';
import { TreatmentEditForm } from './forms/treatment-edit-form.component';

// Union type for all form values
type AllFormValues =
    | AssessmentFormValues
    | DetailsFormValues
    | OwnersFormValues
    | TreatmentFormValues;

interface RiskSectionEditFormProps {
    section: RiskSection;
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit?: (values: AllFormValues) => void | Promise<void>;
}

/**
 * Main form component that renders the appropriate form based on the section.
 * This component acts as a router/orchestrator for the individual form components.
 */
export const RiskSectionEditForm = observer(
    ({
        section,
        formRef,
        onSubmit,
    }: RiskSectionEditFormProps): React.JSX.Element => {
        // Render the appropriate form component based on section
        switch (section) {
            case 'assessment': {
                return (
                    <AssessmentEditForm formRef={formRef} onSubmit={onSubmit} />
                );
            }
            case 'details': {
                return (
                    <DetailsEditForm formRef={formRef} onSubmit={onSubmit} />
                );
            }
            case 'owners': {
                return <OwnersEditForm formRef={formRef} onSubmit={onSubmit} />;
            }
            case 'treatment': {
                return (
                    <TreatmentEditForm formRef={formRef} onSubmit={onSubmit} />
                );
            }
            case 'sourceStatus': {
                return (
                    <RiskSourceStatusEditForm
                        formRef={formRef}
                        onSubmit={onSubmit}
                    />
                );
            }
            default: {
                // Fallback skeleton for unknown sections
                return <Skeleton data-testid="RiskSectionEditFormSkeleton" />;
            }
        }
    },
);
