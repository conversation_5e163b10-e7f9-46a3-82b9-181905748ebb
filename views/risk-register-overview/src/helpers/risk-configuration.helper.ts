import { t } from '@globals/i18n/macro';

/**
 * Risk section type definition.
 */
export type RiskSection =
    | 'assessment'
    | 'details'
    | 'treatment'
    | 'owners'
    | 'sourceStatus';

/**
 * Skeleton bar counts for different sections.
 */
export const SKELETON_BAR_COUNTS = {
    assessment: 3,
    details: 6,
    treatment: 2,
    owners: 2,
    sourceStatus: 2,
} as const;

/**
 * Get section configuration with proper i18n translations.
 */
export const getSectionConfig = (
    section: RiskSection,
): {
    'data-id': string;
    title: string;
    editButtonLabel: string | null;
    testId: string;
} => {
    switch (section) {
        case 'assessment': {
            return {
                'data-id': 'assessment-card',
                title: t`Assessment`,
                editButtonLabel: t`Edit`,
                testId: 'AssessmentViewEditCardComponent',
            };
        }
        case 'details': {
            return {
                'data-id': 'details-card',
                title: t`Details`,
                editButtonLabel: t`Edit`,
                testId: 'DetailsViewEditCardComponent',
            };
        }
        case 'treatment': {
            return {
                'data-id': 'treatment-card',
                title: t`Current treatment`,
                editButtonLabel: t`Edit`,
                testId: 'TreatmentViewEditCardComponent',
            };
        }
        case 'owners': {
            return {
                'data-id': 'owners-card',
                title: t`Owners`,
                editButtonLabel: t`Edit`,
                testId: 'OwnersViewEditCardComponent',
            };
        }
        case 'sourceStatus': {
            return {
                'data-id': 'source-status-card',
                title: t`Source and Status`,
                editButtonLabel: t`Edit`,
                testId: 'SourceStatusViewEditCardComponent',
            };
        }
        default: {
            throw new Error(`Unknown risk section: ${section as string}`);
        }
    }
};
