import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export function getRiskTypeLabel(status: RiskResponseDto['type']): string {
    switch (status) {
        case 'INTERNAL': {
            return t`Internal`;
        }
        case 'EXTERNAL': {
            return t`External`;
        }
        default: {
            return '—';
        }
    }
}

export function getRiskSourceLabel(riskSource: string): string {
    switch (riskSource) {
        case 'INTERNAL': {
            return t`Internal risk`;
        }
        case 'EXTERNAL': {
            return t`External risk`;
        }
        default: {
            return '—';
        }
    }
}

export function getRiskStatusLabel(status: string): string {
    switch (status) {
        case 'ACTIVE': {
            return t`Active`;
        }
        case 'CLOSED': {
            return t`Closed`;
        }
        case 'ARCHIVED': {
            return t`Archived`;
        }
        default: {
            return '—';
        }
    }
}
