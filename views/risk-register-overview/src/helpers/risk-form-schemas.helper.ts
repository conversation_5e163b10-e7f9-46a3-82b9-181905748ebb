import { constant, isNil } from 'lodash-es';
import { z } from 'zod';
import { createNumericOptionsWithDash } from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type {
    CustomFieldsSubmissionResponseDto,
    RiskResponseDto,
    RiskSettingsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { convertToISO8601String } from '@helpers/date-time';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormSchema } from '@ui/forms';
import { mapUserToOption } from './risk-user-form-schemas.helper';

/**
 * Form limits constants for risk forms.
 */
export const RISK_FORM_LIMITS = {
    TITLE_MAX_LENGTH: 191,
    DESCRIPTION_MAX_LENGTH: 30000,
    TREATMENT_DETAILS_MAX_LENGTH: 30000,
    FILE_MAX_SIZE: 25 * 1024 * 1024, // 25MB
} as const;

/**
 * Generate form schema for assessment section.
 */
export const getAssessmentFormSchema = (
    riskDetails: RiskResponseDto | null,
    riskSettings: RiskSettingsResponseDto | null,
    customFields?: Partial<CustomFieldsSubmissionResponseDto>[],
): FormSchema => {
    const impactOptions = riskSettings?.impact
        ? createNumericOptionsWithDash(riskSettings.impact)
        : [];
    const likelihoodOptions = riskSettings?.likelihood
        ? createNumericOptionsWithDash(riskSettings.likelihood)
        : [];

    const defaultImpact = impactOptions.find((option) => {
        if (isNil(riskDetails?.impact)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.impact);
    });

    const defaultLikelihood = likelihoodOptions.find((option) => {
        if (isNil(riskDetails?.likelihood)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.likelihood);
    });

    const baseSchema: FormSchema = {
        impact: {
            type: 'select',
            label: t`Inherent impact`,
            placeholder: t`Select impact`,
            options: impactOptions,
            initialValue: defaultImpact,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.impact ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid impact value`,
                    },
                ),
        },
        likelihood: {
            type: 'select',
            label: t`Inherent likelihood`,
            placeholder: t`Select likelihood`,
            options: likelihoodOptions,
            initialValue: defaultLikelihood,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.likelihood ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid likelihood value`,
                    },
                ),
        },
    };

    const customFieldsSchema = customFields
        ? sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
              customFields,
          )
        : {};

    return {
        ...baseSchema,
        ...customFieldsSchema,
    };
};

export const getTreatmentOptions = (): {
    id: string;
    label: string;
    value: string;
}[] => [
    { id: 'UNTREATED', label: t`Untreated`, value: 'UNTREATED' },
    { id: 'MITIGATE', label: t`Mitigate`, value: 'MITIGATE' },
    { id: 'ACCEPT', label: t`Accept`, value: 'ACCEPT' },
    { id: 'TRANSFER', label: t`Transfer`, value: 'TRANSFER' },
    { id: 'AVOID', label: t`Avoid`, value: 'AVOID' },
];

export const getTreatmentFormSchema = (
    riskDetails: RiskResponseDto | null,
    riskSettings: RiskSettingsResponseDto | null,
    customFields?: CustomFieldsSubmissionResponseDto[],
): FormSchema => {
    const treatmentDetailsMaxLength =
        RISK_FORM_LIMITS.TREATMENT_DETAILS_MAX_LENGTH;

    const treatmentOptions = getTreatmentOptions();
    const impactOptions = riskSettings?.impact
        ? createNumericOptionsWithDash(riskSettings.impact)
        : [];
    const likelihoodOptions = riskSettings?.likelihood
        ? createNumericOptionsWithDash(riskSettings.likelihood)
        : [];

    const defaultTreatmentPlan = treatmentOptions.find(
        (option) => option.value === riskDetails?.treatmentPlan,
    );
    const defaultResidualImpact = impactOptions.find((option) => {
        if (isNil(riskDetails?.residualImpact)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.residualImpact);
    });
    const defaultResidualLikelihood = likelihoodOptions.find((option) => {
        if (isNil(riskDetails?.residualLikelihood)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.residualLikelihood);
    });

    const baseSchema = {
        treatmentPlan: {
            type: 'select',
            label: t`Treatment option`,
            placeholder: t`Select treatment plan`,
            options: treatmentOptions,
            initialValue: defaultTreatmentPlan,
            isOptional: true,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .optional(),
        },
        treatmentDetails: {
            type: 'textarea',
            label: t`Treatment plan`,
            placeholder: t`Enter treatment details`,
            initialValue: riskDetails?.treatmentDetails ?? '',
            validator: z
                .string()
                .max(
                    treatmentDetailsMaxLength,
                    t`Treatment details must be ${treatmentDetailsMaxLength} characters or less`,
                )
                .optional(),
            shownIf: {
                fieldName: 'treatmentPlan.value',
                operator: 'notEquals',
                value: 'UNTREATED',
            },
        },
        residualImpact: {
            type: 'select',
            label: t`Residual impact`,
            placeholder: t`Select residual impact`,
            options: impactOptions,
            initialValue: defaultResidualImpact,
            isOptional: true,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.impact ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid residual impact value`,
                    },
                ),
            shownIf: {
                operator: 'or',
                conditions: [
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'TRANSFER',
                    },
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'MITIGATE',
                    },
                ],
            },
        },
        residualLikelihood: {
            type: 'select',
            label: t`Residual likelihood`,
            placeholder: t`Select residual likelihood`,
            options: likelihoodOptions,
            initialValue: defaultResidualLikelihood,
            isOptional: true,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.likelihood ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid residual likelihood value`,
                    },
                ),
            shownIf: {
                operator: 'or',
                conditions: [
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'TRANSFER',
                    },
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'MITIGATE',
                    },
                ],
            },
        },
        treatmentDueDate: {
            type: 'date',
            label: t`Anticipated completed date`,
            initialValue: riskDetails?.anticipatedCompletionDate
                ? (convertToISO8601String(
                      new Date(riskDetails.anticipatedCompletionDate),
                  ) as TDateISODate)
                : undefined,
            isOptional: true,
            shownIf: {
                operator: 'or',
                conditions: [
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'TRANSFER',
                    },
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'MITIGATE',
                    },
                ],
            },
        },
        treatmentCompletionDate: {
            type: 'date',
            label: t`Completed date`,
            initialValue: riskDetails?.completionDate
                ? (convertToISO8601String(
                      new Date(riskDetails.completionDate),
                  ) as TDateISODate)
                : undefined,
            isOptional: true,
            shownIf: {
                fieldName: 'treatmentPlan.value',
                operator: 'notEquals',
                value: 'UNTREATED',
            },
        },
        reviewers: {
            type: 'combobox',
            isMultiSelect: true,
            getSearchEmptyState: constant(t`No users found`),
            options: sharedUsersInfiniteController.options,
            label: t`Reviewers`,
            loaderLabel: t`Loading users...`,
            placeholder: t`Select reviewers`,
            isOptional: true,
            hasMore: sharedUsersInfiniteController.hasNextPage,
            isLoading: sharedUsersInfiniteController.isLoading,
            onFetchOptions: sharedUsersInfiniteController.onFetchUsers,
            initialValue: (riskDetails?.reviewers ?? []).map(mapUserToOption),
            shownIf: {
                fieldName: 'treatmentPlan.value',
                operator: 'notEquals',
                value: 'UNTREATED',
            },
        },
    };

    const customFieldsSchema = customFields
        ? sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
              customFields,
          )
        : {};

    return {
        ...baseSchema,
        ...customFieldsSchema,
    } as FormSchema;
};
