import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';
import { RiskAccessDeniedView, RiskSectionViewEditCard } from '../components';

export const RiskRegisterOverviewView = observer((): React.JSX.Element => {
    const {
        hasRiskReadPermission,
        hasRiskManagePermission,
        isRiskManagerWithRestrictedView,
        isRiskDomainReadEnabled,
    } = sharedFeatureAccessModel;

    const { isUserInReadOnlyMode, isAuditorInReadOnlyMode } =
        sharedCurrentUserController;

    const hasBasicRiskAccess = isRiskDomainReadEnabled && hasRiskReadPermission;

    if (isAuditorInReadOnlyMode) {
        return <RiskAccessDeniedView reason="auditor-read-only" />;
    }

    if (!hasBasicRiskAccess) {
        return <RiskAccessDeniedView reason="insufficient-permissions" />;
    }

    const isRestrictedAccess = isRiskManagerWithRestrictedView;

    const isGlobalReadOnly = isUserInReadOnlyMode || !hasRiskManagePermission;

    return (
        <Grid
            columns="4fr 3fr"
            gap="xl"
            p="xl"
            data-testid="RiskRegisterOverviewView"
            data-id="ufMJP7Wf"
            data-restricted-view={isRestrictedAccess}
            data-read-only={isGlobalReadOnly}
        >
            <Stack direction="column" gap="xl">
                <RiskSectionViewEditCard section="assessment" />
                <RiskSectionViewEditCard section="treatment" />
            </Stack>
            <Stack direction="column" gap="xl">
                <RiskSectionViewEditCard section="sourceStatus" />
                <RiskSectionViewEditCard section="details" />
                <RiskSectionViewEditCard section="owners" />
            </Stack>
        </Grid>
    );
});
