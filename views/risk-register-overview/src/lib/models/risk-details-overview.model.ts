import { constant } from 'lodash-es';
import React from 'react';
import { z } from 'zod';
import { sharedRiskCategoriesController } from '@controllers/risk';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    CustomFieldsSubmissionResponseDto,
    RiskResponseDto,
} from '@globals/api-sdk/types';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { CustomFieldRenderProps, FormSchema } from '@ui/forms';
import { CategoryFieldWithAddButton } from '../../components/forms/risk-category-field-with-add-button.component';
import { RISK_FORM_LIMITS } from '../../helpers/risk-form-schemas.helper';

const renderCategoryField = (
    fieldProps: CustomFieldRenderProps,
): React.JSX.Element => {
    return React.createElement(CategoryFieldWithAddButton, fieldProps);
};

export class RiskDetailsOverviewModel {
    constructor() {
        makeAutoObservable(this);
    }

    get categoriesOptions(): ListBoxItemData[] {
        return sharedRiskCategoriesController.categories.map((category) => ({
            id: category.id.toString(),
            label: category.name,
        }));
    }

    getCategoriesFormSchema = (
        riskDetails: RiskResponseDto | null,
    ): FormSchema => {
        if (sharedEntitlementFlagController.isRiskManagementEnabled) {
            return {
                categories: {
                    type: 'custom',
                    render: renderCategoryField,
                    label: t`Category`,
                    isOptional: true,
                    initialValue: () =>
                        riskDetails?.categories
                            ? riskDetails.categories.map((category) => ({
                                  id: String(category.id),
                                  label: category.name,
                                  value: String(category.id),
                              }))
                            : [],
                },
            };
        }

        return {
            categories: {
                type: 'combobox',
                isMultiSelect: true,
                getSearchEmptyState: constant(t`No categories found`),
                options: this.categoriesOptions,
                label: t`Risk categories`,
                loaderLabel: t`Loading risk categories options`,
                isOptional: true,
                hasMore: sharedRiskCategoriesController.hasNextPage,
                isLoading: sharedRiskCategoriesController.isLoading,
                onFetchOptions:
                    sharedRiskCategoriesController.onFetchCategories,
                initialValue: riskDetails?.categories
                    ? riskDetails.categories.map((category) => ({
                          id: String(category.id),
                          label: category.name,
                          value: String(category.id),
                      }))
                    : [],
            },
        };
    };

    getDetailsFormSchema = (
        riskDetails: RiskResponseDto | null,
        customFields?: CustomFieldsSubmissionResponseDto[],
    ): FormSchema => {
        const titleMaxLength = RISK_FORM_LIMITS.TITLE_MAX_LENGTH;
        const descriptionMaxLength = RISK_FORM_LIMITS.DESCRIPTION_MAX_LENGTH;

        const baseSchema = {
            title: {
                type: 'text',
                label: t`Title`,
                initialValue: riskDetails?.title ?? '',
                validator: z
                    .string()
                    .min(1, t`Title is required`)
                    .max(
                        titleMaxLength,
                        t`Title must be ${titleMaxLength} characters or less`,
                    ),
            },
            description: {
                type: 'textarea',
                label: t`Description`,
                placeholder: t`Enter risk description`,
                initialValue: riskDetails?.description ?? '',
                validator: riskDetails?.id
                    ? z
                          .string()
                          .max(
                              descriptionMaxLength,
                              t`Description must be ${descriptionMaxLength} characters or less`,
                          )
                          .optional()
                    : z.string().min(1, t`Description is required`),
            },
            identifiedAt: {
                type: 'date',
                initialValue: riskDetails?.identifiedAt
                    ? new Date(riskDetails.identifiedAt)
                          .toISOString()
                          .split('T')[0]
                    : undefined,
                label: t`Risk identified date`,
                isOptional: true,
            },
            ...this.getCategoriesFormSchema(riskDetails),
        };

        const customFieldsSchema = customFields
            ? sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
                  customFields,
              )
            : {};

        return {
            ...baseSchema,
            ...customFieldsSchema,
        } as FormSchema;
    };
}

export const sharedRiskDetailsOverviewModel = new RiskDetailsOverviewModel();
