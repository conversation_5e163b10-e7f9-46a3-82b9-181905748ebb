import { getPolicyStatusLabel } from '@components/policies';
import {
    sharedPolicyBuilderController,
    sharedPolicyHeaderActionsController,
} from '@controllers/policy-builder';
import type {
    ContentType,
    KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import { Metadata, type MetadataProps } from '@cosmos/components/metadata';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { getUserInitials } from '@helpers/user';
import { sharedPolicyBuilderModel } from '@models/policy-builder';

export class PolicyBuilderHeaderModel {
    readonly pageId = 'policy-builder';

    constructor() {
        makeAutoObservable(this);
    }

    private get policy() {
        return sharedPolicyBuilderController.policy;
    }

    get isLoading(): boolean {
        return sharedPolicyBuilderModel.isPolicyBuilderFirstLoading;
    }

    get title(): string {
        return sharedPolicyBuilderModel.policyName;
    }

    get actionStack(): React.JSX.Element {
        return this.policyHeaderActionsController.actionStack;
    }

    private get policyHeaderActionsController() {
        return sharedPolicyHeaderActionsController;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const pairs = [];
        const { currentVersion } = sharedPolicyBuilderController;

        if (currentVersion?.policyVersionStatus) {
            const { label, colorScheme } = getPolicyStatusLabel(
                currentVersion.policyVersionStatus,
            );

            pairs.push(
                this.buildBadgeKeyValue(
                    t`Status`,
                    'policy-status',
                    label,
                    colorScheme,
                ),
            );
        }

        if (currentVersion?.createdAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Created on`,
                    'policy-creation-date',
                    currentVersion.createdAt,
                ),
            );
        }

        if (sharedPolicyBuilderModel.isBambooHRProvider) {
            pairs.push({
                id: 'policy-bamboo-hr-approved-date',
                label: t`Approved on`,
                value: t`Approved in Bamboo HR`,
                type: 'TEXT' as ContentType,
            });
        }

        if (currentVersion?.approvedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Approved on`,
                    'policy-approved-date',
                    currentVersion.approvedAt,
                ),
            );
        }

        if (currentVersion?.publishedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Published on`,
                    'policy-published-date',
                    currentVersion.publishedAt,
                ),
            );
        }

        if (this.policy?.currentOwner) {
            pairs.push(
                this.buildUserKeyValue(
                    t`Owner`,
                    'policy-owner',
                    this.policy.currentOwner,
                ),
            );
        }

        return pairs;
    }

    get slot(): React.JSX.Element {
        return (
            <Metadata
                key="version"
                label={this.versionLabel}
                type="tag"
                colorScheme={
                    sharedPolicyBuilderModel.isDraft ? 'warning' : 'neutral'
                }
            />
        );
    }

    private get versionLabel() {
        const { currentVersion, isDraft } = sharedPolicyBuilderModel;

        if (isDraft) {
            return t`Draft`;
        }
        if (currentVersion?.composedVersion) {
            const version = currentVersion.composedVersion;

            return t`V${version}`;
        }

        return t`V1.0`;
    }

    private buildKeyValue(
        label: string,
        id: string,
        date: string,
    ): KeyValuePairProps {
        return {
            id,
            label,
            value: new Date(date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            }),
            type: 'TEXT' as ContentType,
        };
    }

    private buildUserKeyValue(
        label: string,
        id: string,
        user: UserResponseDto,
    ): KeyValuePairProps {
        const fullName = getFullName(user.firstName, user.lastName);
        const initials = getUserInitials(user);

        return {
            id,
            label,
            value: {
                username: fullName,
                avatarProps: {
                    fallbackText: initials,
                    imgSrc: user.avatarUrl ?? '',
                    imgAlt: fullName,
                },
            },
            type: 'USER' as ContentType,
        };
    }

    private buildBadgeKeyValue(
        label: string,
        id: string,
        badgeLabel: string,
        colorScheme: MetadataProps['colorScheme'],
    ): KeyValuePairProps {
        return {
            id,
            label,
            value: {
                label: badgeLabel,
                colorScheme,
            },
            type: 'BADGE' as ContentType,
        };
    }
}
