import { getExternalPolicyProviderLabel } from '@components/policies';
import {
    BannerLocation,
    type BannerMessage,
    BannerPersistenceType,
    createBannerMessage,
} from '@controllers/banner-service';
import {
    PolicyActionOutdatedBanner,
    sharedPolicyBuilderController,
} from '@controllers/policy-builder';
import { routeController } from '@controllers/route';
import { Button } from '@cosmos/components/button';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { AppLink } from '@ui/app-link';

const BANNER_IDS = {
    CHANGES_REQUESTED: 'policy-builder-changes-requested-banner',
    DRAFT: 'policy-builder-draft-banner',
    PUBLISHED: 'policy-builder-published-banner',
    MISSING_OWNER: 'policy-builder-missing-owner',
    RENEWAL_DATE: 'policy-builder-renewal-date-banner',
    OUTDATED: 'policy-builder-outdated-banner',
    UNACCEPTABLE: 'policy-builder-unacceptable-banner',
    DRAFT_APPROVED_WARNING: 'policy-builder-draft-approved-warning',
} as const;

export class PoliciesBuilderBannersModel {
    constructor() {
        makeAutoObservable(this);
    }

    get changesRequestedBanner(): BannerMessage | null {
        const { changesRequestedBy, shouldShowChangesRequestedBanner } =
            sharedPolicyBuilderModel;

        if (!shouldShowChangesRequestedBanner) {
            return null;
        }

        const { policyId } = routeController.currentParams;

        const REDIRECT_TO_OVERVIEW = `${routeController.userPartOfUrl}/governance/policies/builder/${policyId}/overview`;

        const HEADER_MESSAGE = changesRequestedBy
            ? t`${changesRequestedBy} requested changes to this version.`
            : t`Changes were requested for this version.`;

        return createBannerMessage({
            id: BANNER_IDS.CHANGES_REQUESTED,
            title: t`Changes requested`,
            severity: 'warning',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
            action: (
                <Text type="body" size="200" colorScheme="warning">
                    <Trans>
                        {HEADER_MESSAGE}{' '}
                        <AppLink
                            colorScheme="warning"
                            data-id="sync-to-newest-version-button"
                            href={REDIRECT_TO_OVERVIEW}
                        >
                            See review and approval section
                        </AppLink>{' '}
                        for details.
                    </Trans>
                </Text>
            ),
        });
    }

    get draftOrPublishedBanner(): BannerMessage | null {
        const {
            isDraft,
            hasPublishedVersion,
            isPublished,
            hasDraftVersion,
            publishedVersionId,
            draftVersionId,
        } = sharedPolicyBuilderModel;
        const shouldShowVersionBanner =
            (isDraft && hasPublishedVersion) ||
            (isPublished && hasDraftVersion);
        const { switchToPolicyVersion } = sharedPolicyBuilderController;

        if (!shouldShowVersionBanner) {
            return null;
        }

        if (!isDraft) {
            const goToDraft = (): void => {
                if (draftVersionId) {
                    switchToPolicyVersion(draftVersionId);
                }
            };

            return createBannerMessage({
                id: BANNER_IDS.PUBLISHED,
                title: t`This is a published version`,
                body: t`You have an unpublished draft version`,
                severity: 'primary',
                location: BannerLocation.PAGE_HEADER,
                persistenceType: BannerPersistenceType.ROUTE_SCOPED,
                action: (
                    <Button
                        label={t`View draft version`}
                        size="sm"
                        level="secondary"
                        data-id="published-banner-draft-button"
                        onClick={goToDraft}
                    />
                ),
            });
        }

        const goToPublished = (): void => {
            if (publishedVersionId) {
                switchToPolicyVersion(publishedVersionId);
            }
        };

        return createBannerMessage({
            id: BANNER_IDS.DRAFT,
            title: t`This is a draft version`,
            severity: 'primary',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
            action: (
                <Button
                    label={t`View latest published version`}
                    size="sm"
                    level="secondary"
                    data-id="draft-banner-published-button"
                    onClick={goToPublished}
                />
            ),
        });
    }

    get missingOwnerBanner(): BannerMessage | null {
        if (sharedPolicyBuilderController.policy?.currentOwner) {
            return null;
        }

        return createBannerMessage({
            id: BANNER_IDS.MISSING_OWNER,
            title: t`Owner is missing`,
            body: t`Drata requires an owner for all policies. Edit policy details to add an owner, then try again.`,
            severity: 'critical',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
        });
    }

    get expiredRenewalDateBanner(): BannerMessage | null {
        const { hasExpiredRenewalDate, currentVersion } =
            sharedPolicyBuilderModel;

        if (!currentVersion) {
            return null;
        }

        if (
            !hasExpiredRenewalDate ||
            !['DRAFT', 'NEEDS_APPROVAL', 'APPROVED'].includes(
                currentVersion.policyVersionStatus,
            )
        ) {
            return null;
        }

        return createBannerMessage({
            id: BANNER_IDS.RENEWAL_DATE,
            title: t`Update renewal date`,
            body: t`Before finalizing, make sure your renewal date is updated.`,
            severity: 'critical',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
        });
    }

    get externalSyncBanner(): BannerMessage | null {
        const { isExternalPolicy, policyStatus, externalPolicyProvider } =
            sharedPolicyBuilderModel;

        if (!isExternalPolicy || !externalPolicyProvider) {
            return null;
        }

        const providerLabel = getExternalPolicyProviderLabel(
            externalPolicyProvider,
        );

        if (policyStatus === 'OUTDATED') {
            return createBannerMessage({
                id: BANNER_IDS.OUTDATED,
                title: t`This policy was updated in ${providerLabel}`,
                body: t`Sync to the newest version of this policy to review the latest changes.`,
                severity: 'warning',
                location: BannerLocation.PAGE_HEADER,
                persistenceType: BannerPersistenceType.ROUTE_SCOPED,
                action: <PolicyActionOutdatedBanner />,
            });
        }

        if (policyStatus === 'UNACCEPTABLE') {
            return createBannerMessage({
                id: BANNER_IDS.UNACCEPTABLE,
                title: t`This policy was deleted in ${providerLabel}`,
                body: t`Import and associate a new file to this policy`,
                severity: 'critical',
                location: BannerLocation.PAGE_HEADER,
                persistenceType: BannerPersistenceType.ROUTE_SCOPED,
            });
        }

        return null;
    }

    get draftApprovedWarningBanner(): BannerMessage | null {
        const { isDraft, currentVersion } = sharedPolicyBuilderModel;

        if (!isDraft || !currentVersion?.approvedAt) {
            return null;
        }

        return createBannerMessage({
            id: BANNER_IDS.DRAFT_APPROVED_WARNING,
            title: t`Changes requested`,
            body: t`Changes have been requested for this policy version.`,
            severity: 'warning',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
        });
    }

    policiesBuilderViewBanners = (): BannerMessage[] => {
        const banners: BannerMessage[] = [];
        const {
            changesRequestedBanner,
            draftOrPublishedBanner,
            missingOwnerBanner,
            expiredRenewalDateBanner,
            externalSyncBanner,
            draftApprovedWarningBanner,
        } = this;

        if (changesRequestedBanner) {
            banners.push(changesRequestedBanner);
        }

        if (draftOrPublishedBanner) {
            banners.push(draftOrPublishedBanner);
        }

        if (missingOwnerBanner) {
            banners.push(missingOwnerBanner);
        }

        if (expiredRenewalDateBanner) {
            banners.push(expiredRenewalDateBanner);
        }

        if (externalSyncBanner) {
            banners.push(externalSyncBanner);
        }

        if (draftApprovedWarningBanner) {
            banners.push(draftApprovedWarningBanner);
        }

        return banners;
    };
}
