import { isString } from 'lodash-es';
import { z } from 'zod';
import {
    type PersonnelData,
    sharedAuditCreationPersonnelController,
    sharedAuditCreationWizardController,
} from '@controllers/audit-creation-wizard';
import {
    CONTROL_EVIDENCE_INPUT_NAMES,
    isSOC2Type1Framework,
    OS_OPTIONS,
} from '@controllers/audit-hub';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    getPersonnelPlaceholderStatus,
    type PersonnelPlaceholderStatus,
    transformPersonnelToOptions,
} from '@helpers/audit-personnel';
import type { FormSchema } from '@ui/forms';

export class AuditCreationWizardSamplesModel {
    constructor() {
        makeAutoObservable(this);
    }

    getMultiPersonnelEmptyPlaceholder = (
        personnelDataType: string,
        personnelData: PersonnelData | null,
    ): PersonnelPlaceholderStatus => {
        const personnel =
            personnelData?.[personnelDataType as keyof typeof personnelData];

        return getPersonnelPlaceholderStatus(personnel);
    };

    get auditSamplesFormSchema(): FormSchema {
        const { auditSamplesData, createdAuditId, auditDetails } =
            sharedAuditCreationWizardController;

        const isSoc2Type1 = isSOC2Type1Framework(auditDetails?.framework?.type);

        const {
            personnelData,
            hasNextHiredPersonnelPage,
            hasNextFormerPersonnelPage,
            hasNextCurrentPersonnelPage,
        } = sharedAuditCreationPersonnelController;

        const hiredPersonnelStatus = this.getMultiPersonnelEmptyPlaceholder(
            CONTROL_EVIDENCE_INPUT_NAMES.HIRED_PERSONNEL,
            personnelData,
        );
        const formerPersonnelStatus = this.getMultiPersonnelEmptyPlaceholder(
            CONTROL_EVIDENCE_INPUT_NAMES.FORMER_PERSONNEL,
            personnelData,
        );
        const currentPersonnelStatus = this.getMultiPersonnelEmptyPlaceholder(
            CONTROL_EVIDENCE_INPUT_NAMES.CURRENT_PERSONNEL,
            personnelData,
        );

        const formSchema = {
            [CONTROL_EVIDENCE_INPUT_NAMES.PLATFORM]: {
                type: 'select',
                label: t`Operating System`,
                options: OS_OPTIONS,
                placeholder: t`Select`,
                required: true,
                loaderLabel: t`Loading`,
                initialValue: auditSamplesData.platform,
                validator: z
                    .object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    })
                    .nullable()
                    .refine((data) => data !== null, {
                        message: t`Please select an operating system`,
                    }),
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.DATE_RANGE]: {
                type: 'date',
                label: isSoc2Type1 ? t`Date` : t`Dates`,
                isMulti: !isSoc2Type1, // Single date for SOC2 Type 1, multi for others
                required: true,
                loaderLabel: t`Loading`,
                initialValue: (() => {
                    if (isSoc2Type1) {
                        return isString(auditSamplesData.dateRange)
                            ? auditSamplesData.dateRange
                            : auditSamplesData.dateRange[0] || '';
                    }

                    return Array.isArray(auditSamplesData.dateRange)
                        ? auditSamplesData.dateRange
                        : [];
                })(),
                getIsDateUnavailable: (date: string) => {
                    const startDate = auditDetails?.date?.startingDate;
                    const endDate = auditDetails?.date?.endingDate;

                    if (!startDate) {
                        return true; // If no audit period is set, all dates are unavailable
                    }

                    const selectedDate = new Date(date);
                    const startFormattedDate = new Date(startDate);
                    const endFormattedDate = endDate
                        ? new Date(endDate)
                        : startFormattedDate;

                    return (
                        selectedDate < startFormattedDate ||
                        selectedDate > endFormattedDate
                    );
                },
                validator: isSoc2Type1
                    ? z.string().date(t`Please select a valid date`)
                    : z.array(z.string().date()).min(2, {
                          message: t`At least two dates are required for this audit type`,
                      }),
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.HIRED_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel hired during audit period`,
                initialValue: auditSamplesData.hiredPersonnel,
                loaderLabel: t`Loading`,
                isMultiSelect: true,
                isOptional: true,
                removeAllSelectedItemsLabel: t`Clear all`,
                disabled: !hiredPersonnelStatus.enabled,
                placeholder: hiredPersonnelStatus.message,
                options: transformPersonnelToOptions(
                    personnelData?.hiredPersonnel ?? [],
                ),
                hasMore: hasNextHiredPersonnelPage,
                onFetchOptions: ({
                    search,
                    increasePage,
                }: {
                    search?: string;
                    increasePage?: boolean;
                }) => {
                    if (createdAuditId) {
                        sharedAuditCreationPersonnelController.onFetchPersonnel(
                            createdAuditId,
                            'hired',
                            {
                                search,
                                increasePage,
                            },
                        );
                    }
                },
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.FORMER_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel terminated during audit period`,
                initialValue: auditSamplesData.formerPersonnel,
                loaderLabel: t`Loading`,
                isMultiSelect: true,
                isOptional: true,
                removeAllSelectedItemsLabel: t`Clear all`,
                disabled: !formerPersonnelStatus.enabled,
                placeholder: formerPersonnelStatus.message,
                options: transformPersonnelToOptions(
                    personnelData?.formerPersonnel ?? [],
                ),
                hasMore: hasNextFormerPersonnelPage,
                onFetchOptions: ({
                    search,
                    increasePage,
                }: {
                    search?: string;
                    increasePage?: boolean;
                }) => {
                    if (createdAuditId) {
                        sharedAuditCreationPersonnelController.onFetchPersonnel(
                            createdAuditId,
                            'former',
                            {
                                search,
                                increasePage,
                            },
                        );
                    }
                },
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.CURRENT_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel employed during audit period`,
                initialValue: auditSamplesData.currentPersonnel,
                loaderLabel: t`Loading`,
                isMultiSelect: true,
                isOptional: true,
                removeAllSelectedItemsLabel: t`Clear all`,
                disabled: !currentPersonnelStatus.enabled,
                placeholder: currentPersonnelStatus.message,
                options: transformPersonnelToOptions(
                    personnelData?.currentPersonnel ?? [],
                ),
                hasMore: hasNextCurrentPersonnelPage,
                onFetchOptions: ({
                    search,
                    increasePage,
                }: {
                    search?: string;
                    increasePage?: boolean;
                }) => {
                    if (createdAuditId) {
                        sharedAuditCreationPersonnelController.onFetchPersonnel(
                            createdAuditId,
                            'current',
                            {
                                search,
                                increasePage,
                            },
                        );
                    }
                },
            },
        };

        return formSchema as FormSchema;
    }
}

export const sharedAuditCreationWizardSamplesModel =
    new AuditCreationWizardSamplesModel();
