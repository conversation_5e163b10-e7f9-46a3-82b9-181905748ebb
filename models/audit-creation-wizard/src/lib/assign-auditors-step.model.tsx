import {
    type AuditorOption,
    sharedAuditCreationWizardController,
} from '@controllers/audit-creation-wizard';
import { Avatar } from '@cosmos/components/avatar';
import type { AuditorWithAuditsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';

export class AssignAuditorsStepModel {
    constructor() {
        makeAutoObservable(this);
    }

    get auditorOptions(): AuditorOption[] {
        const auditors = sharedAuditCreationWizardController.auditorsData;

        return auditors.map((auditor: AuditorWithAuditsResponseDto) => ({
            id: auditor.id,
            label: `${auditor.firstName} ${auditor.lastName}`,
            value: auditor.id,
            description: auditor.email,
            startSlot: (
                <Avatar
                    fallbackText={auditor.firstName[0] + auditor.lastName[0]}
                    imgSrc={auditor.avatarUrl ?? undefined}
                    imgAlt={`${auditor.firstName} ${auditor.lastName}`}
                    size="xs"
                />
            ),
        }));
    }

    get formSchema(): FormSchema {
        return {
            assignedAuditors: {
                type: 'combobox',
                label: t`Auditors`,
                placeholder: t`Choose auditor`,
                removeAllSelectedItemsLabel: t`Clear all`,
                isMultiSelect: true,
                isOptional: true,
                initialValue:
                    sharedAuditCreationWizardController.assignedAuditors,
                options: this.auditorOptions,
                onFetchOptions:
                    sharedAuditCreationWizardController.handleAuditorSearch,
                loaderLabel: t`Loading auditors...`,
                getSearchEmptyState: () => t`Can't find them? Add new auditor`,
            },
        };
    }
}

export const sharedAssignAuditorsStepModel = new AssignAuditorsStepModel();
