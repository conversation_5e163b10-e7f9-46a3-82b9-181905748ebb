import type { ComponentProps } from 'react';
import {
    VENDORS_CURRENT_FILTER_BUSINESS_UNIT_ID,
    VENDORS_CURRENT_FILTER_IMPACT_LEVELS_ID,
    VENDORS_CURRENT_FILTER_NEXT_REVIEW_DEADLINE_ID,
    VENDORS_CURRENT_FILTER_RISK_ID,
    VENDORS_CURRENT_FILTER_SCHEDULED_QUESTIONNAIRES_ID,
    VENDORS_CURRENT_FILTER_SECURITY_REVIEW_STATUS_ID,
    VENDORS_CURRENT_FILTER_STATUS_ID,
    VENDORS_CURRENT_FILTER_SUBPROCESSORS_ID,
    VENDORS_CURRENT_FILTER_TYPE_ID,
} from '@controllers/vendors';
import type { Datatable } from '@cosmos/components/datatable';
import type { VendorsControllerListVendorsData } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import {
    getBusinessUnitOptions,
    getDeadlineStatusOptions,
    getImpactLevelOptions,
    getRiskOptions,
    getScheduledQuestionnairesOptions,
    getSecurityReviewStatusOptions,
    getStatusOptions,
    getSubprocessorOptions,
    getVendorTypeOptions,
} from './constants/vendors-current.constants';
import { getCurrentVendorFilterLabel } from './helpers/get-current-vendor-filter-label.helper';

export type VendorsCurrentFilterProps = ComponentProps<
    typeof Datatable
>['filterProps'];

export interface VendorsCurrentFilterValues {
    [VENDORS_CURRENT_FILTER_STATUS_ID]?: NonNullable<
        VendorsControllerListVendorsData['query']
    >['status'];
    [VENDORS_CURRENT_FILTER_NEXT_REVIEW_DEADLINE_ID]?: NonNullable<
        VendorsControllerListVendorsData['query']
    >['nextReviewDeadlineStatus'];
}
export class VendorsCurrentModel {
    initialFilterValues: VendorsCurrentFilterValues = {};

    constructor() {
        makeAutoObservable(this);
    }

    setInitialFilterValues(values: VendorsCurrentFilterValues): void {
        this.clearInitialFilterValues();
        this.initialFilterValues = values;
    }

    clearInitialFilterValues(): void {
        this.initialFilterValues = {};
    }

    get filters(): VendorsCurrentFilterProps {
        return {
            filters: [
                {
                    filterType: 'select',
                    id: VENDORS_CURRENT_FILTER_TYPE_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_TYPE_ID,
                    ),
                    options: getVendorTypeOptions(),
                },
                {
                    filterType: 'select',
                    id: VENDORS_CURRENT_FILTER_STATUS_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_STATUS_ID,
                    ),
                    options: getStatusOptions(),
                },
                {
                    filterType: 'select',
                    id: VENDORS_CURRENT_FILTER_IMPACT_LEVELS_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_IMPACT_LEVELS_ID,
                    ),
                    options: getImpactLevelOptions(),
                },
                {
                    filterType: 'select',
                    id: VENDORS_CURRENT_FILTER_RISK_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_RISK_ID,
                    ),
                    options: getRiskOptions(),
                },
                {
                    filterType: 'select',
                    id: VENDORS_CURRENT_FILTER_SECURITY_REVIEW_STATUS_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_SECURITY_REVIEW_STATUS_ID,
                    ),
                    options: getSecurityReviewStatusOptions(),
                },
                {
                    filterType: 'select',
                    id: VENDORS_CURRENT_FILTER_NEXT_REVIEW_DEADLINE_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_NEXT_REVIEW_DEADLINE_ID,
                    ),
                    options: getDeadlineStatusOptions(),
                },
                {
                    filterType: 'select',
                    id: VENDORS_CURRENT_FILTER_BUSINESS_UNIT_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_BUSINESS_UNIT_ID,
                    ),
                    options: getBusinessUnitOptions(),
                },
                {
                    filterType: 'radio',
                    id: VENDORS_CURRENT_FILTER_SUBPROCESSORS_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_SUBPROCESSORS_ID,
                    ),
                    options: getSubprocessorOptions(),
                },
                {
                    filterType: 'radio',
                    id: VENDORS_CURRENT_FILTER_SCHEDULED_QUESTIONNAIRES_ID,
                    label: getCurrentVendorFilterLabel(
                        VENDORS_CURRENT_FILTER_SCHEDULED_QUESTIONNAIRES_ID,
                    ),
                    options: getScheduledQuestionnairesOptions(),
                },
            ],
        };
    }
}

export const sharedVendorsCurrentModel = new VendorsCurrentModel();
