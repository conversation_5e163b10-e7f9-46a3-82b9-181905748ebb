import type { ComponentProps } from 'react';
import {
    VENDORS_PROSPECTIVE_FILTER_IMPACT_LEVELS_ID,
    VENDORS_PROSPECTIVE_FILTER_SECURITY_REVIEW_STATUS_ID,
} from '@controllers/vendors';
import type { Datatable } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    getImpactLevelOptions,
    getReviewStatusOptions,
} from './constants/vendors-prospective.constants';
import { getProspectiveVendorFilterLabel } from './helpers/get-prospective-vendor-filter-label.helper';

export type VendorsProspectiveFilterProps = ComponentProps<
    typeof Datatable
>['filterProps'];

export class VendorsProspectiveModel {
    constructor() {
        makeAutoObservable(this);
    }

    get filters(): VendorsProspectiveFilterProps {
        return {
            filters: [
                {
                    filterType: 'select',
                    id: VENDORS_PROSPECTIVE_FILTER_IMPACT_LEVELS_ID,
                    label: getProspectiveVendorFilterLabel(
                        VENDORS_PROSPECTIVE_FILTER_IMPACT_LEVELS_ID,
                    ),
                    placeholder: t`All impact levels`,
                    options: getImpactLevelOptions(),
                },
                {
                    filterType: 'select',
                    id: VENDORS_PROSPECTIVE_FILTER_SECURITY_REVIEW_STATUS_ID,
                    label: getProspectiveVendorFilterLabel(
                        VENDORS_PROSPECTIVE_FILTER_SECURITY_REVIEW_STATUS_ID,
                    ),
                    placeholder: t`All statuses`,
                    options: getReviewStatusOptions(),
                },
            ],
        };
    }
}

export const sharedVendorsProspectiveModel = new VendorsProspectiveModel();
