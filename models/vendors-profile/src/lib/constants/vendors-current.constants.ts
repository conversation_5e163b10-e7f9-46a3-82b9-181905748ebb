import type {
    VendorsControllerListVendorsData,
    VendorSecurityReviewResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export function getVendorTypeLabel(
    type: NonNullable<VendorsControllerListVendorsData['query']>['type'],
): string {
    switch (type) {
        case 'NONE': {
            return t`None`;
        }
        case 'CONTRACTOR': {
            return t`Contractor`;
        }
        case 'PARTNER': {
            return t`Partner`;
        }
        case 'SUPPLIER': {
            return t`Supplier`;
        }
        case 'VENDOR': {
            return t`Vendor`;
        }
        case 'OTHER': {
            return t`Other`;
        }
        default: {
            return t`None`;
        }
    }
}

export function getSecurityReviewStatusLabel(
    status: NonNullable<
        VendorsControllerListVendorsData['query']
    >['securityReviewStatus'],
): string {
    switch (status) {
        case 'COMPLETED': {
            return t`Completed`;
        }
        case 'IN_PROGRESS': {
            return t`In progress`;
        }
        case 'NEEDS_REVIEW': {
            return t`Needs review`;
        }
        case 'NO_PAST_REVIEW': {
            return t`No past review`;
        }
        case 'UP_TO_DATE': {
            return t`Up to date`;
        }
        default: {
            return t`No past review`;
        }
    }
}

export function mapSecurityReviewStatus(
    apiStatus: VendorSecurityReviewResponseDto['status'] | undefined | null,
): NonNullable<
    VendorsControllerListVendorsData['query']
>['securityReviewStatus'] {
    switch (apiStatus) {
        case 'COMPLETED': {
            return 'COMPLETED';
        }
        case 'IN_PROGRESS': {
            return 'IN_PROGRESS';
        }
        case 'NOT_YET_STARTED': {
            return 'NO_PAST_REVIEW';
        }
        case 'NOT_REQUIRED': {
            return 'NO_SECURITY';
        }
        case undefined:
        case null: {
            return 'NO_SECURITY';
        }
        default: {
            return 'NO_SECURITY';
        }
    }
}

function getBusinessUnitLabel(
    category: NonNullable<
        VendorsControllerListVendorsData['query']
    >['category'],
): string {
    switch (category) {
        case 'NONE': {
            return t`None`;
        }
        case 'ADMINISTRATIVE': {
            return t`Administrative`;
        }
        case 'CS': {
            return t`Customer success`;
        }
        case 'ENGINEERING': {
            return t`Engineering`;
        }
        case 'FINANCE': {
            return t`Finance`;
        }
        case 'HR': {
            return t`Human resources`;
        }
        case 'INFORMATION_TECHNOLOGY': {
            return t`Information technology`;
        }
        case 'LEGAL': {
            return t`Legal`;
        }
        case 'MARKETING': {
            return t`Marketing`;
        }
        case 'PRODUCT': {
            return t`Product`;
        }
        case 'SALES': {
            return t`Sales`;
        }
        case 'SECURITY': {
            return t`Security`;
        }
        default: {
            return t`None`;
        }
    }
}

export function getRiskLabel(
    risk: NonNullable<VendorsControllerListVendorsData['query']>['risk'],
): string {
    switch (risk) {
        case 'NONE': {
            return t`None`;
        }
        case 'LOW': {
            return t`Low`;
        }
        case 'MODERATE': {
            return t`Moderate`;
        }
        case 'HIGH': {
            return t`High`;
        }
        default: {
            return t`None`;
        }
    }
}

export function getDeadlineStatusLabel(
    status: NonNullable<
        VendorsControllerListVendorsData['query']
    >['nextReviewDeadlineStatus'],
): string {
    switch (status) {
        case 'OVERDUE': {
            return t`Overdue`;
        }
        case 'DUE_SOON': {
            return t`Due soon`;
        }
        case 'NO_RENEWAL': {
            return t`Not due`;
        }
        default: {
            return t`Not due`;
        }
    }
}

function getSubprocessorLabel(
    value: NonNullable<
        VendorsControllerListVendorsData['query']
    >['isSubProcessor'],
): string {
    switch (value) {
        case 'YES': {
            return t`Yes`;
        }
        case 'NO': {
            return t`No`;
        }
        default: {
            return t`No`;
        }
    }
}

function getScheduledQuestionnairesLabel(
    status: NonNullable<
        VendorsControllerListVendorsData['query']
    >['scheduledQuestionnaireStatus'],
): string {
    switch (status) {
        case 'ENABLED': {
            return t`Scheduled`;
        }
        case 'DISABLED': {
            return t`Not scheduled`;
        }
        default: {
            return t`Not scheduled`;
        }
    }
}

export function getImpactLevelLabel(
    level: NonNullable<
        VendorsControllerListVendorsData['query']
    >['impactLevel'],
): string {
    switch (level) {
        case 'INSIGNIFICANT': {
            return t`Insignificant`;
        }
        case 'MINOR': {
            return t`Minor`;
        }
        case 'MODERATE': {
            return t`Moderate`;
        }
        case 'MAJOR': {
            return t`Major`;
        }
        case 'CRITICAL': {
            return t`Critical`;
        }
        case 'UNSCORED': {
            return t`Unscored`;
        }
        default: {
            return t`Unscored`;
        }
    }
}

export function getVendorStatusLabel(
    status: NonNullable<VendorsControllerListVendorsData['query']>['status'],
): string {
    switch (status) {
        case 'NONE': {
            return t`None`;
        }
        case 'ACTIVE': {
            return t`Active`;
        }
        case 'APPROVED': {
            return t`Approved`;
        }
        case 'ARCHIVED': {
            return t`Archived`;
        }
        case 'FLAGGED': {
            return t`Flagged`;
        }
        case 'OFFBOARDED': {
            return t`Offboarded`;
        }
        case 'ON_HOLD': {
            return t`On hold`;
        }
        case 'REJECTED': {
            return t`Rejected`;
        }
        case 'UNDER_REVIEW': {
            return t`Under review`;
        }
        default: {
            return t`None`;
        }
    }
}

const VENDOR_TYPE_VALUES = [
    'NONE',
    'CONTRACTOR',
    'PARTNER',
    'SUPPLIER',
    'VENDOR',
    'OTHER',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['type'][];

export const IMPACT_LEVEL_VALUES = [
    'INSIGNIFICANT',
    'MINOR',
    'MODERATE',
    'MAJOR',
    'CRITICAL',
    'UNSCORED',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['impactLevel'][];

const SECURITY_REVIEW_STATUS_VALUES = [
    'COMPLETED',
    'IN_PROGRESS',
    'NEEDS_REVIEW',
    'NO_PAST_REVIEW',
    'UP_TO_DATE',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['securityReviewStatus'][];

const BUSINESS_UNIT_VALUES = [
    'NONE',
    'ADMINISTRATIVE',
    'CS',
    'ENGINEERING',
    'FINANCE',
    'HR',
    'INFORMATION_TECHNOLOGY',
    'LEGAL',
    'MARKETING',
    'PRODUCT',
    'SALES',
    'SECURITY',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['category'][];

const RISK_VALUES = [
    'NONE',
    'LOW',
    'MODERATE',
    'HIGH',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['risk'][];

const SCHEDULED_QUESTIONNAIRES_VALUES = [
    'ENABLED',
    'DISABLED',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['scheduledQuestionnaireStatus'][];

const VENDOR_STATUS_VALUES = [
    'NONE',
    'ACTIVE',
    'APPROVED',
    'ARCHIVED',
    'FLAGGED',
    'OFFBOARDED',
    'ON_HOLD',
    'REJECTED',
    'UNDER_REVIEW',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['status'][];

const DEADLINE_STATUS_VALUES = [
    'OVERDUE',
    'DUE_SOON',
    'NO_RENEWAL',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['nextReviewDeadlineStatus'][];

const SUBPROCESSOR_VALUES = [
    'YES',
    'NO',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['isSubProcessor'][];

export const getImpactLevelOptions = (): {
    id: (typeof IMPACT_LEVEL_VALUES)[number];
    label: string;
    value: (typeof IMPACT_LEVEL_VALUES)[number];
}[] =>
    IMPACT_LEVEL_VALUES.map((value) => ({
        id: value,
        label: getImpactLevelLabel(value),
        value,
    }));

export const getVendorTypeOptions = (): {
    id: (typeof VENDOR_TYPE_VALUES)[number];
    label: string;
    value: (typeof VENDOR_TYPE_VALUES)[number];
}[] =>
    VENDOR_TYPE_VALUES.map((value) => ({
        id: value,
        label: getVendorTypeLabel(value),
        value,
    }));

export const getSecurityReviewStatusOptions = (): {
    id: (typeof SECURITY_REVIEW_STATUS_VALUES)[number];
    label: string;
    value: (typeof SECURITY_REVIEW_STATUS_VALUES)[number];
}[] =>
    SECURITY_REVIEW_STATUS_VALUES.map((value) => ({
        id: value,
        label: getSecurityReviewStatusLabel(value),
        value,
    }));

export const getBusinessUnitOptions = (): {
    id: (typeof BUSINESS_UNIT_VALUES)[number];
    label: string;
    value: (typeof BUSINESS_UNIT_VALUES)[number];
}[] =>
    BUSINESS_UNIT_VALUES.map((value) => ({
        id: value,
        label: getBusinessUnitLabel(value),
        value,
    }));

export const getRiskOptions = (): {
    id: (typeof RISK_VALUES)[number];
    label: string;
    value: (typeof RISK_VALUES)[number];
}[] =>
    RISK_VALUES.map((value) => ({
        id: value,
        label: getRiskLabel(value),
        value,
    }));

export const getDeadlineStatusOptions = (): {
    id: (typeof DEADLINE_STATUS_VALUES)[number];
    label: string;
    value: (typeof DEADLINE_STATUS_VALUES)[number];
}[] =>
    DEADLINE_STATUS_VALUES.map((value) => ({
        id: value,
        label: getDeadlineStatusLabel(value),
        value,
    }));

export const getSubprocessorOptions = (): {
    label: string;
    value: (typeof SUBPROCESSOR_VALUES)[number];
}[] =>
    SUBPROCESSOR_VALUES.map((value) => ({
        label: getSubprocessorLabel(value),
        value,
    }));

export const getScheduledQuestionnairesOptions = (): {
    label: string;
    value: (typeof SCHEDULED_QUESTIONNAIRES_VALUES)[number];
}[] =>
    SCHEDULED_QUESTIONNAIRES_VALUES.map((value) => ({
        label: getScheduledQuestionnairesLabel(value),
        value,
    }));

export const getStatusOptions = (): {
    id: (typeof VENDOR_STATUS_VALUES)[number];
    label: string;
    value: (typeof VENDOR_STATUS_VALUES)[number];
}[] =>
    VENDOR_STATUS_VALUES.map((value) => ({
        id: value,
        label: getVendorStatusLabel(value),
        value,
    }));
