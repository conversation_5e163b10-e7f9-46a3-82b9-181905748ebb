import type { VendorsControllerListVendorsData } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    getImpactLevelLabel,
    IMPACT_LEVEL_VALUES,
} from './vendors-current.constants';

export const REVIEW_STATUS_VALUES = [
    'NOT_YET_STARTED',
    'IN_PROGRESS',
    'COMPLETED',
] as const satisfies readonly NonNullable<
    VendorsControllerListVendorsData['query']
>['reviewStatus'][];

function getReviewStatusLabel(
    level: NonNullable<
        VendorsControllerListVendorsData['query']
    >['reviewStatus'],
): string {
    switch (level) {
        case 'NOT_YET_STARTED': {
            return t`Not started yet`;
        }
        case 'IN_PROGRESS': {
            return t`In progress`;
        }
        case 'COMPLETED': {
            return t`Completed`;
        }
        default: {
            return t`Completed`;
        }
    }
}

export const getReviewStatusOptions = (): {
    id: (typeof REVIEW_STATUS_VALUES)[number];
    label: string;
    value: (typeof REVIEW_STATUS_VALUES)[number];
}[] =>
    REVIEW_STATUS_VALUES.map((value) => ({
        id: value,
        label: getReviewStatusLabel(value),
        value,
    }));

export const getImpactLevelOptions = (): {
    id: (typeof IMPACT_LEVEL_VALUES)[number];
    label: string;
    value: (typeof IMPACT_LEVEL_VALUES)[number];
}[] =>
    IMPACT_LEVEL_VALUES.map((value) => ({
        id: value,
        label: getImpactLevelLabel(value),
        value,
    }));
