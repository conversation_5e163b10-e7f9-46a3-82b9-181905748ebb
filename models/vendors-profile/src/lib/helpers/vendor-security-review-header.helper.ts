import { isEmpty, isNil } from 'lodash-es';
import type { ComponentProps } from 'react';
import { openFinalizeReviewModal } from '@components/vendors-security-reviews';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
    sharedVendorsSecurityReviewMutationController,
    sharedVendorsVrmAgentController,
    sharedVendorVrmAgentActionsController,
} from '@controllers/vendors';
import type {
    ActionStack,
    ActionStackProps,
} from '@cosmos/components/action-stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

export const handleDeleteReview = action(
    (securityReviewId: number, vendorId: number): void => {
        openConfirmationModal({
            title: t`Delete Security Review`,
            body: t`Confirm that you'd like to delete this Security Review. Any uploaded documents to this review will also be removed.`,
            confirmText: t`Yes, delete review`,
            cancelText: t`No, take me back`,
            type: 'danger',
            size: 'md',
            onConfirm: action(() => {
                const { isProspectiveVendor } = sharedVendorsDetailsController;
                const vendorType = isProspectiveVendor
                    ? 'prospective'
                    : 'current';

                const redirectPath = `${routeController.userPartOfUrl}/vendors/${vendorType}/${vendorId}/overview`;

                sharedVendorsSecurityReviewMutationController.deleteSecurityReviewWithCallback(
                    securityReviewId,
                    () => {
                        sharedProgrammaticNavigationController.navigateTo(
                            redirectPath,
                        );
                    },
                );
            }),
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    },
);

export const handleFinalizeReview = action((): void => {
    const { isVendorEditable } = sharedFeatureAccessModel;

    if (!isVendorEditable) {
        return;
    }

    openFinalizeReviewModal();
});

export const handleReRunAssessment = action((): void => {
    const { isVendorEditable } = sharedFeatureAccessModel;
    const { securityReviewDetails } =
        sharedVendorsSecurityReviewDetailsController;
    const vendorId = securityReviewDetails?.vendor?.id;

    if (!isVendorEditable || !vendorId) {
        return;
    }

    sharedVendorVrmAgentActionsController.handleReRun(vendorId);
});

export const getHeaderStackActions = (
    securityReviewId?: number,
    vendorId?: number,
): ComponentProps<typeof ActionStack>['stacks'] => {
    const { isVendorEditable } = sharedFeatureAccessModel;
    const { allSecurityReviewDocuments } =
        sharedVendorsSecurityReviewDocumentsController;

    const dropdownItems = [];

    if (isVendorEditable && securityReviewId && vendorId) {
        dropdownItems.push({
            id: 'vendor-security-review-header-delete-review',
            label: t`Delete review`,
            type: 'item' as const,
            value: 'DELETE_VENDOR',
            colorScheme: 'critical' as const,
            onSelect: () => {
                handleDeleteReview(securityReviewId, vendorId);
            },
        });
    }

    // Check if re-run assessment button should be shown
    const shouldShowReRunButton =
        isVendorEditable &&
        sharedVendorsVrmAgentController.currentAssessment?.createdAt &&
        !isEmpty(allSecurityReviewDocuments) &&
        sharedVendorsVrmAgentController.hasNewerSecurityReviewDocuments();

    const actions: NonNullable<ActionStackProps['stacks']>[number]['actions'] =
        [
            {
                actionType: 'dropdown',
                id: 'vendor-security-review-header-dropdown',
                typeProps: {
                    isIconOnly: true,
                    startIconName: 'HorizontalMenu',
                    level: 'tertiary',
                    label: t`Options`,
                    align: 'end',
                    items: dropdownItems,
                },
            },
        ];

    // Add re-run assessment button if conditions are met
    if (shouldShowReRunButton) {
        actions.push({
            actionType: 'button',
            id: 'vendor-security-review-header-rerun-assessment',
            typeProps: {
                label: t`Re-run assessment`,
                level: 'secondary',
                colorScheme: 'primary',
                onClick: handleReRunAssessment,
            },
        });
    }
    const { currentAssessment } = sharedVendorsVrmAgentController;

    if (isNil(currentAssessment) || currentAssessment.canFinalize) {
        actions.push({
            actionType: 'button',
            id: 'vendor-security-review-header-finalize-review',
            typeProps: {
                label: t`Finalize review`,
                colorScheme: 'primary',
                onClick: handleFinalizeReview,
            },
        });
    }

    return [
        {
            actions,
            id: 'vendor-security-review-header-actions-stack',
        },
    ] as const;
};
