import { isEmpty } from 'lodash-es';
import {
    BannerLocation,
    type BannerMessage,
    BannerPersistenceType,
    createBannerMessage,
} from '@controllers/banner-service';
import { sharedVendorsSecurityReviewDocumentsController } from '@controllers/vendors';

/**
 * Creates a banner message for empty security review documents state.
 * This function is reactive and will be re-evaluated when allSecurityReviewDocuments changes.
 */
export function createSecurityReviewEmptyStateBanner(): BannerMessage[] {
    // Access the observable inside the function to make it reactive
    const { allSecurityReviewDocuments } =
        sharedVendorsSecurityReviewDocumentsController;

    if (isEmpty(allSecurityReviewDocuments)) {
        return [
            createBannerMessage({
                id: 'vendors-profile-security-review-banner',
                title: 'VRM Agent',
                severity: 'ai',
                body: 'Send a questionnaire to the vendor or upload documents like a SOC 2 report to get started on the assessment.',
                location: BannerLocation.DOMAIN_CONTENT,
                persistenceType: BannerPersistenceType.ROUTE_SCOPED,
            }),
        ];
    }

    return [];
}
