import {
    BannerLocation,
    type BannerMessage,
    BannerPersistenceType,
    createBannerMessage,
} from '@controllers/banner-service';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import {
    sharedVendorsDetailsController,
    sharedVendorsVrmAgentController,
    sharedVendorVrmAgentActionsController,
} from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { AppButton } from '@ui/app-button';

function handleOpenAssessment(): void {
    const { currentParams, userPartOfUrl } = routeController;
    const { vendorId } = currentParams;
    const { currentAssessmentSecurityReviewId } =
        sharedVendorsVrmAgentController;

    if (!vendorId || !currentAssessmentSecurityReviewId) {
        return;
    }
    const navigationUrl = `${userPartOfUrl}/vendors/current/${vendorId}/security-reviews/${currentAssessmentSecurityReviewId}`;

    sharedProgrammaticNavigationController.navigateTo(navigationUrl);
}

function createVrmAgentBanner(): BannerMessage | null {
    const { vendorDetails } = sharedVendorsDetailsController;
    const { hasVendorAssessmentAgentWorkflow, isLoading } =
        sharedVendorsVrmAgentController;

    if (isLoading || hasVendorAssessmentAgentWorkflow || !vendorDetails) {
        return null;
    }

    const vendorName = vendorDetails.name;

    return createBannerMessage({
        id: 'vendors-profile-vrm-agent-banner',
        title: t`VRM Agent`,
        body: t`I can help you conduct a detailed assessment on ${vendorName} and provide a report of their security posture.`,
        severity: 'ai',
        location: BannerLocation.PAGE_HEADER,
        persistenceType: BannerPersistenceType.ROUTE_SCOPED,
        action: (
            <AppButton
                label={t`Start review`}
                size="sm"
                level="tertiary"
                startIconName="AI"
                data-id="vendors-profile-vrm-agent-banner-start-assessment-button"
                onClick={() => {
                    sharedVendorVrmAgentActionsController.handleAssessmentStart(
                        vendorDetails.id,
                    );
                }}
            />
        ),
    });
}

function createVrmAgentWorkflowBanner(): BannerMessage | null {
    const { hasVendorAssessmentAgentWorkflow } =
        sharedVendorsVrmAgentController;

    if (!hasVendorAssessmentAgentWorkflow) {
        return null;
    }

    return createBannerMessage({
        id: 'vendors-profile-vrm-agent-workflow-banner',
        title: t`VRM Agent`,
        body: t`You are currently conducting a security review of this vendor.`,
        severity: 'ai',
        location: BannerLocation.PAGE_HEADER,
        persistenceType: BannerPersistenceType.ROUTE_SCOPED,

        action: (
            <AppButton
                label={t`Open review`}
                size="sm"
                level="tertiary"
                startIconName="AI"
                data-id="vendors-profile-vrm-agent-workflow-banner-open-assessment-button"
                onClick={handleOpenAssessment}
            />
        ),
    });
}

function createSecurityReviewBanner(): BannerMessage | null {
    const { profileHeaderData } = sharedVendorsDetailsController;

    if (!profileHeaderData?.showSecurityReviewsInfoBanner) {
        return null;
    }

    return createBannerMessage({
        id: 'vendors-profile-overview-banner',
        title: t`Vendor review in progress`,
        body: t`You are currently conducting a security review of this vendor.`,
        severity: 'primary',
        location: BannerLocation.PAGE_HEADER,
        persistenceType: BannerPersistenceType.ROUTE_SCOPED,
    });
}

export const getVendorsProfileBanners = (): BannerMessage[] => {
    const banners: BannerMessage[] = [];
    const vrmAgentBanner = createVrmAgentBanner();

    if (vrmAgentBanner) {
        banners.push(vrmAgentBanner);
    }

    const vrmAgentWorkflowBanner = createVrmAgentWorkflowBanner();

    if (vrmAgentWorkflowBanner) {
        banners.push(vrmAgentWorkflowBanner);
    }

    // Add security review banner if applicable
    const securityReviewBanner = createSecurityReviewBanner();

    if (securityReviewBanner) {
        banners.push(securityReviewBanner);
    }

    return banners;
};
