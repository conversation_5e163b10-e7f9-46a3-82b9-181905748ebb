import type { Tab } from '@controllers/route';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { VendorCategory } from './types/vendor-types';

export class VendorsSecurityReviewContentNavModel {
    #vendorType: VendorCategory = 'current';
    #vendorId: number | null = null;
    #securityReviewId: number | null = null;

    constructor(
        vendorId: number,
        securityReviewId: number,
        vendorType: VendorCategory,
    ) {
        makeAutoObservable(this);

        this.#vendorId = vendorId;
        this.#securityReviewId = securityReviewId;
        this.#vendorType = vendorType;
    }

    get tabs(): Tab[] {
        const { hasVendorAssessmentAgentWorkflow } =
            sharedVendorsVrmAgentController;
        const vendorTypePrefix = this.#vendorType;

        // If hasVendorAssessmentAgentWorkflow is false, return empty array (no tabs)
        if (!hasVendorAssessmentAgentWorkflow) {
            return [];
        }

        // If hasVendorAssessmentAgentWorkflow is true, show tabs
        return [
            {
                topicPath: `vendors/${vendorTypePrefix}/${this.#vendorId}/security-reviews/${this.#securityReviewId}/assessment`,
                label: t`Assessment`,
            },
            {
                topicPath: `vendors/${vendorTypePrefix}/${this.#vendorId}/security-reviews/${this.#securityReviewId}/documents`,
                label: t`Documents`,
            },
        ];
    }
}
