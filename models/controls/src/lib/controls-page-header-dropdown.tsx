import { isError, noop } from 'lodash-es';
import {
    FlatfileEntityEnum,
    sharedFlatfileController,
} from '@controllers/flatfile';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { Button } from '@cosmos/components/button';
import { Dropdown } from '@cosmos/components/dropdown';
import { ListBoxItem } from '@cosmos/components/list-box';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, observer, runInAction } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { AppButton } from '@ui/app-button';

const handleImportClick = action((): void => {
    try {
        sharedFlatfileController.createSpace({
            entityType: FlatfileEntityEnum.CONTROL,
        });
    } catch (error) {
        logger.error({
            message: 'Failed to trigger control import',
            errorObject: {
                message: isError(error) ? error.message : String(error),
                statusCode: '500',
            },
        });

        snackbarController.addSnackbar({
            id: 'controls-import-error',
            props: {
                title: 'Import failed',
                description:
                    'Failed to start control import. Please try again.',
                severity: 'critical',
                closeButtonAriaLabel: 'Close',
            },
        });
    }
});

export const ControlsPageHeaderDropdown = observer(
    (): React.JSX.Element | undefined => {
        const navigate = useNavigate();
        const {
            hasWriteControlPermission,
            isReleaseBulkImportControlsEnabled,
        } = sharedFeatureAccessModel;

        const { isCustomControlsEnabled } = sharedEntitlementFlagController;

        const handleCreateSingleControlClick = () => {
            runInAction(() => {
                navigate(
                    `${routeController.userPartOfUrl}/compliance/controls/create`,
                );
            });
        };

        if (!hasWriteControlPermission) {
            return undefined;
        }

        if (!isCustomControlsEnabled) {
            return (
                <AppButton
                    data-id="create-control-button"
                    className="custom-controls-not-entitled"
                    label={t`Create control`}
                    // Chameleon will take over here
                    onClick={noop}
                />
            );
        }

        if (!isReleaseBulkImportControlsEnabled) {
            return (
                <AppButton
                    data-id="create-control-button"
                    label={t`Create control`}
                    href={`${routeController.userPartOfUrl}/compliance/controls/create`}
                />
            );
        }

        return (
            <Dropdown data-id="create-control-dropdown">
                <Dropdown.Trigger data-id="create-control-trigger">
                    <Button
                        level="secondary"
                        data-id="create-control-button"
                        endIconName="ChevronDown"
                        label={t`Create control`}
                    />
                </Dropdown.Trigger>
                <Dropdown.Content data-id="create-single-control-content">
                    <Dropdown.Item data-id="create-single-control-item">
                        <ListBoxItem
                            data-id="create-single-control-list-item"
                            label={t`Create a single control`}
                            onClick={handleCreateSingleControlClick}
                        />
                    </Dropdown.Item>
                    <Dropdown.Item data-id="create-control-bulk-item">
                        <ListBoxItem
                            data-id="create-control-bulk-list-item"
                            label={t`Create/Update in bulk`}
                            onClick={handleImportClick}
                        />
                    </Dropdown.Item>
                </Dropdown.Content>
            </Dropdown>
        );
    },
);
