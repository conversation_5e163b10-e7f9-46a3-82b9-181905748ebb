import { sharedAuditHubEvidenceViewerController } from '@controllers/audit-hub-evidence-viewer';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { routeController } from '@controllers/route';
import { ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

/**
 * Page header model for the Evidence Viewer page.
 * Contains all the logic for page header, breadcrumbs, key-value pairs, and action stack.
 */
export class EvidenceViewerPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'evidence-viewer';

    get evidenceTabUrl(): string {
        const {
            auditId,
            clientId,
            getRequestId: requestId,
        } = sharedCustomerRequestDetailsController;
        const { userPartOfUrl } = routeController;

        return `${userPartOfUrl}/audit-hub/clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/evidence`;
    }

    get title(): string {
        const { evidenceDocument } = sharedAuditHubEvidenceViewerController;

        if (!evidenceDocument?.name) {
            return t`Evidence Viewer`;
        }

        return evidenceDocument.name;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        return sharedAuditHubEvidenceViewerController.keyValuePairs;
    }

    get breadcrumbs(): Breadcrumb[] {
        const {
            customerRequestDetails,
            auditId,
            clientId,
            getRequestId: requestId,
        } = sharedCustomerRequestDetailsController;
        const { company } = sharedCurrentCompanyController;
        const breadcrumbs: Breadcrumb[] = [];

        if (clientId) {
            breadcrumbs.push(
                {
                    label: t`Client List`,
                    pathname: '/audit-hub/clients',
                },
                {
                    label: company?.name || t`Client`,
                    pathname: `/audit-hub/clients/${clientId}/audits`,
                },
            );

            if (customerRequestDetails && auditId && requestId) {
                breadcrumbs.push(
                    {
                        label: customerRequestDetails.framework.label,
                        pathname: `/audit-hub/clients/${clientId}/audits/${auditId}/details`,
                    },
                    {
                        label: customerRequestDetails.code,
                        pathname: `/audit-hub/clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/evidence`,
                    },
                );
            }
        }

        return breadcrumbs;
    }

    get actionStack(): React.JSX.Element {
        const {
            navigateToPrevious,
            navigateToNext,
            canNavigatePrevious,
            canNavigateNext,
            currentPosition,
            totalCount,
        } = sharedAuditHubEvidenceViewerController;

        return (
            <ActionStack
                data-id="evidence-viewer-action-stack"
                stacks={[
                    {
                        id: 'evidence-viewer-pagination',
                        actions: [
                            {
                                actionType: 'button',
                                id: 'download-evidence-button',
                                typeProps: {
                                    startIconName: 'Download',
                                    label: t`Download`,
                                    level: 'tertiary',
                                    colorScheme: 'primary',
                                    onClick: () => {
                                        sharedAuditHubEvidenceViewerController.downloadSelectedEvidence();
                                    },
                                },
                            },
                            {
                                actionType: 'text',
                                id: 'evidence-pagination-label',
                                typeProps: {
                                    children: t`${currentPosition} of ${totalCount}`,
                                    colorScheme: 'neutral',
                                    size: '200',
                                },
                            },
                            {
                                actionType: 'button',
                                id: 'previous-evidence-button',
                                typeProps: {
                                    endIconName: 'ArrowUp',
                                    label: t`Previous`,
                                    level: 'secondary',
                                    colorScheme: 'neutral',
                                    cosmosUseWithCaution_isDisabled:
                                        !canNavigatePrevious,
                                    onClick: () => {
                                        navigateToPrevious();
                                    },
                                },
                            },
                            {
                                actionType: 'button',
                                id: 'next-evidence-button',
                                typeProps: {
                                    endIconName: 'ArrowDown',
                                    label: t`Next`,
                                    level: 'secondary',
                                    colorScheme: 'neutral',
                                    cosmosUseWithCaution_isDisabled:
                                        !canNavigateNext,
                                    onClick: () => {
                                        navigateToNext();
                                    },
                                },
                            },
                        ],
                    },
                ]}
            />
        );
    }
}

export const sharedEvidenceViewerPageHeaderModel =
    new EvidenceViewerPageHeaderModel();
