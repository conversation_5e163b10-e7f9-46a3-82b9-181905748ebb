import { z } from 'zod';
import {
    type PersonnelData,
    sharedAuditCreationPersonnelController,
} from '@controllers/audit-creation-wizard';
import {
    OS_OPTIONS,
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { AuditorFrameworkTypeNames } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    getPersonnelPlaceholderStatus,
    type PersonnelPlaceholderStatus,
    transformPersonnelToOptions,
} from '@helpers/audit-personnel';
import type { FormSchema } from '@ui/forms';
import { CONTROL_EVIDENCE_INPUT_NAMES } from './constants/control-evidence.constants';

export class AuditHubWizardSampleModel {
    constructor() {
        makeAutoObservable(this);
    }

    getMultiPersonnelEmptyPlaceholder = (
        personnelDataType: string,
        personnelData: PersonnelData | null,
    ): PersonnelPlaceholderStatus => {
        const personnel =
            personnelData?.[personnelDataType as keyof typeof personnelData];

        return getPersonnelPlaceholderStatus(personnel);
    };

    get sampleFormSchema(): FormSchema {
        const { auditByIdData } = sharedAuditHubController;
        const { sampleWizardData } = sharedAuditHubAuditController;
        const {
            personnelData,
            hasNextHiredPersonnelPage,
            hasNextFormerPersonnelPage,
            hasNextCurrentPersonnelPage,
        } = sharedAuditCreationPersonnelController;

        const hiredPersonnelStatus = this.getMultiPersonnelEmptyPlaceholder(
            CONTROL_EVIDENCE_INPUT_NAMES.HIRED_PERSONNEL,
            personnelData,
        );
        const formerPersonnelStatus = this.getMultiPersonnelEmptyPlaceholder(
            CONTROL_EVIDENCE_INPUT_NAMES.FORMER_PERSONNEL,
            personnelData,
        );
        const currentPersonnelStatus = this.getMultiPersonnelEmptyPlaceholder(
            CONTROL_EVIDENCE_INPUT_NAMES.CURRENT_PERSONNEL,
            personnelData,
        );

        const isSOC2Type1 =
            auditByIdData?.framework.type ===
            AuditorFrameworkTypeNames.SOC_2_TYPE_1;

        const auditId = auditByIdData?.framework.id;

        const formSchema = {
            platform: {
                type: 'select',
                label: t`Operating System`,
                options: OS_OPTIONS,
                placeholder: t`Select`,
                required: true,
                loaderLabel: t`Loading`,
                initialValue: sampleWizardData.platform,
                validator: z
                    .object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    })
                    .required(),
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.DATE_RANGE]: {
                type: 'date',
                label: t`Dates`,
                isMulti: !isSOC2Type1,
                required: true,
                locale: 'en-US',
                loaderLabel: t`Loading`,
                initialValue: sampleWizardData.date,
                getIsDateUnavailable: (date: string) => {
                    const { startDate, endDate } =
                        auditByIdData?.framework ?? {};

                    if (!startDate || !endDate) {
                        return false;
                    }
                    const selectedDate = new Date(date);
                    const startFormattedDate = new Date(startDate);
                    const endFormattedDate = new Date(endDate);

                    if (isSOC2Type1) {
                        return (
                            selectedDate < startFormattedDate ||
                            selectedDate >= endFormattedDate
                        );
                    }

                    return (
                        selectedDate < startFormattedDate ||
                        selectedDate > endFormattedDate
                    );
                },
                validator: isSOC2Type1
                    ? z.string().date()
                    : z.array(z.string().date()).min(2, {
                          message: t`At least two dates are required for this audit type`,
                      }),
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.HIRED_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel hired during audit period`,
                initialValue: sampleWizardData.hiredPersonnel,
                loaderLabel: t`Loading`,
                isMultiSelect: true,
                isOptional: true,
                removeAllSelectedItemsLabel: t`Clear all`,
                disabled: !(
                    hiredPersonnelStatus.enabled || hasNextHiredPersonnelPage
                ),
                placeholder:
                    hiredPersonnelStatus.enabled || hasNextHiredPersonnelPage
                        ? t`Select`
                        : hiredPersonnelStatus.message,
                options: transformPersonnelToOptions(
                    personnelData?.hiredPersonnel ?? [],
                ),
                hasMore: hasNextHiredPersonnelPage,
                onFetchOptions: ({
                    search,
                    increasePage,
                }: {
                    search?: string;
                    increasePage?: boolean;
                }) => {
                    if (auditId) {
                        sharedAuditCreationPersonnelController.onFetchPersonnel(
                            auditId,
                            'hired',
                            {
                                search,
                                increasePage,
                            },
                        );
                    }
                },
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.FORMER_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel terminated during audit period`,
                loaderLabel: t`Loading`,
                initialValue: sampleWizardData.formerPersonnel,
                isMultiSelect: true,
                isOptional: true,
                removeAllSelectedItemsLabel: t`Clear all`,
                disabled: !(
                    formerPersonnelStatus.enabled || hasNextFormerPersonnelPage
                ),
                placeholder:
                    formerPersonnelStatus.enabled || hasNextFormerPersonnelPage
                        ? t`Select`
                        : formerPersonnelStatus.message,
                options: transformPersonnelToOptions(
                    personnelData?.formerPersonnel ?? [],
                ),
                hasMore: hasNextFormerPersonnelPage,
                onFetchOptions: ({
                    search,
                    increasePage,
                }: {
                    search?: string;
                    increasePage?: boolean;
                }) => {
                    if (auditId) {
                        sharedAuditCreationPersonnelController.onFetchPersonnel(
                            auditId,
                            'former',
                            {
                                search,
                                increasePage,
                            },
                        );
                    }
                },
            },
            [CONTROL_EVIDENCE_INPUT_NAMES.CURRENT_PERSONNEL]: {
                type: 'combobox',
                label: t`Personnel employed during audit period`,
                loaderLabel: t`Loading`,
                isMultiSelect: true,
                initialValue: sampleWizardData.currentPersonnel,
                removeAllSelectedItemsLabel: t`Clear all`,
                isOptional: true,
                disabled: !(
                    currentPersonnelStatus.enabled ||
                    hasNextCurrentPersonnelPage
                ),
                placeholder:
                    currentPersonnelStatus.enabled ||
                    hasNextCurrentPersonnelPage
                        ? t`Select`
                        : currentPersonnelStatus.message,
                options: transformPersonnelToOptions(
                    personnelData?.currentPersonnel ?? [],
                ),
                hasMore: hasNextCurrentPersonnelPage,
                onFetchOptions: ({
                    search,
                    increasePage,
                }: {
                    search?: string;
                    increasePage?: boolean;
                }) => {
                    if (auditId) {
                        sharedAuditCreationPersonnelController.onFetchPersonnel(
                            auditId,
                            'current',
                            {
                                search,
                                increasePage,
                            },
                        );
                    }
                },
            },
        };

        return formSchema as FormSchema;
    }
}
