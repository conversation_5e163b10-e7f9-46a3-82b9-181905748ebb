import { sharedMonitoringFiltersController } from '@controllers/monitoring';
import type { FilterProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    getCheckStatusOptions,
    getMonitorCheckTypeOptions,
    getTestTypeOptions,
} from '@views/monitoring';

export class MonitoringFiltersModel {
    constructor() {
        makeAutoObservable(this);
    }

    get filters(): FilterProps {
        const { connectionOptions, controlOptions, frameworkOptions } =
            sharedMonitoringFiltersController;

        return {
            triggerLabel: t`Filters`,
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'allowedCheckResultStatuses',
                    label: t`Result`,
                    options: [
                        {
                            label: t`Passed`,
                            value: 'PASSED',
                        },
                        {
                            label: t`Failed`,
                            value: 'FAILED',
                        },
                        {
                            label: t`Error`,
                            value: 'ERROR',
                        },
                    ],
                },
                {
                    filterType: 'checkbox',
                    id: 'allowedStatuses',
                    label: t`Status`,
                    options: getCheckStatusOptions(),
                },
                {
                    filterType: 'checkbox',
                    id: 'allowedCategories',
                    label: t`Category`,
                    options: getMonitorCheckTypeOptions(),
                },
                {
                    filterType: 'checkbox',
                    id: 'allowedTestSources',
                    label: t`Type`,
                    options: getTestTypeOptions(),
                },
                {
                    filterType: 'checkbox',
                    id: 'hasExclusions',
                    label: t`Exclusions`,
                    options: [
                        {
                            label: t`Has Exclusions`,
                            value: 'true',
                        },
                    ],
                },
                {
                    filterType: 'combobox',
                    id: 'allowedConnections',
                    isMultiSelect: true,
                    placeholder: t`Select all that apply`,
                    label: t`Connection`,
                    options: connectionOptions,
                },
                {
                    filterType: 'combobox',
                    id: 'allowedControls',
                    isMultiSelect: true,
                    placeholder: t`Select all that apply`,
                    label: t`Control`,
                    options: controlOptions,
                },
                {
                    filterType: 'combobox',
                    id: 'allowedFrameworks',
                    isMultiSelect: true,
                    placeholder: t`Select all that apply`,
                    label: t`Framework`,
                    options: frameworkOptions,
                },
                {
                    filterType: 'checkbox',
                    id: 'allowedTicketStatuses',
                    label: t`Tickets`,
                    options: [
                        {
                            label: t`In Progress`,
                            value: 'IN_PROGRESS',
                        },
                        {
                            label: t`Done`,
                            value: 'ARCHIVED',
                        },
                    ],
                },
            ],
        };
    }
}

export const sharedMonitoringFiltersModel = new MonitoringFiltersModel();
