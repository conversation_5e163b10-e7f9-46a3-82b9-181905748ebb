import { openClosedTicketsModal } from '@components/utilities';
import { sharedConnectionsController } from '@controllers/connections';
import {
    type CreateTicketFn,
    type CreateTicketPayload,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import {
    activeMonitoringDetailsController,
    sharedMonitoringDetailsTicketsController,
    sharedMonitoringDetailsTicketsMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import { DEFAULT_PARAMS } from '@cosmos/components/datatable';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { CreateTicketModalView } from '@views/create-ticket-modal';

const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';

export class MonitoringDetailsTicketingModel {
    constructor() {
        makeAutoObservable(this);
    }

    createMonitorTicket = (
        payload: CreateTicketPayload,
        monitorId: number,
    ): Promise<void> => {
        return sharedMonitoringDetailsTicketsMutationController.createTicket(
            payload,
            monitorId,
        );
    };

    handleCreateTicket = (monitorId: number): void => {
        const workspaceName = sharedWorkspacesController.currentWorkspace?.name;

        const monitorName =
            sharedMonitoringTestDetailsController.testName ||
            activeMonitoringDetailsController.monitorDetailsData?.testName ||
            `Monitor ${monitorId}`;

        const defaultDescription = `Created in Drata for ${workspaceName}'s test: ${monitorName}`;

        this.openCreateTicketModal((payload: CreateTicketPayload) => {
            return this.createMonitorTicket(payload, monitorId);
        }, defaultDescription);
    };

    handleViewClosedTickets = (monitorId: number): void => {
        sharedMonitoringDetailsTicketsController.loadClosedTicketsInfinite(
            monitorId,
        );

        openClosedTicketsModal({
            objectName: sharedMonitoringDetailsTicketsController.objectName,
            objectId: monitorId,
            ticketsController: sharedMonitoringDetailsTicketsController,
        });
    };

    openCreateTicketModal = (
        onCreateTicket: CreateTicketFn,
        defaultDescription?: string,
    ): void => {
        sharedTicketAutomationController.loadTicketAutomation(DEFAULT_PARAMS);
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedCreateTicketController.initialize(onCreateTicket, {
            description: defaultDescription,
        });
        modalController.openModal({
            id: CREATE_TICKET_MODAL_ID,
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: true,
            content: () => (
                <CreateTicketModalView data-id="MonitorTicketModal" />
            ),
        });
    };
}

export const sharedMonitoringDetailsTicketingModel =
    new MonitoringDetailsTicketingModel();
