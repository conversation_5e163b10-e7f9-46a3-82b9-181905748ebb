import { capitalize, isEmpty } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import {
    sharedRiskManagementController,
    sharedRiskManagementMutationController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { dimension3x } from '@cosmos/constants/tokens';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, observer, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getFullName, getInitials } from '@helpers/formatters';
import { getRiskTreatmentLabel } from '@helpers/risk-treatment';
import {
    DeleteCustomRiskModal,
    DeleteRiskModal,
} from '@views/risk-register-management';
import { calculateRiskMetrics } from '@views/risk-register-overview';

const openDeleteRiskModalAction = action((): void => {
    const { riskDetails } = sharedRiskDetailsController;
    const { riskManagementListQuery } = sharedRiskManagementController;
    const { navigateTo } = sharedProgrammaticNavigationController;
    const { isScalingRiskFoundationEnabled } = sharedFeatureAccessModel;

    if (!riskDetails?.riskId) {
        return;
    }

    const modalId = 'delete-risk-modal';

    if (isScalingRiskFoundationEnabled) {
        modalController.openModal({
            id: modalId,
            content: () => (
                <DeleteRiskModal
                    data-id="delete-risk-modal-header"
                    onClose={() => {
                        modalController.closeModal(modalId);
                    }}
                    onConfirm={() => {
                        sharedRiskManagementMutationController.deleteRisk(
                            riskDetails.riskId,
                        );

                        when(
                            () =>
                                !sharedRiskManagementMutationController.isDeleteRiskPending,
                            () => {
                                if (
                                    sharedRiskManagementMutationController.hasError
                                ) {
                                    return;
                                }

                                snackbarController.addSnackbar({
                                    id: 'risk-delete-success',
                                    props: {
                                        title: t`Risk deleted`,
                                        severity: 'success',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });

                                riskManagementListQuery.invalidate();

                                navigateTo(
                                    `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks`,
                                );

                                modalController.closeModal(modalId);
                            },
                        );
                    }}
                />
            ),
            size: 'md',
            centered: true,
            disableClickOutsideToClose: false,
        });

        return;
    }

    modalController.openModal({
        id: modalId,
        content: () => (
            <DeleteCustomRiskModal
                data-id="delete-custom-risk-modal-header"
                onClose={() => {
                    modalController.closeModal(modalId);
                }}
                onConfirm={() => {
                    sharedRiskManagementMutationController.deleteRisk(
                        riskDetails.riskId,
                    );

                    when(
                        () =>
                            !sharedRiskManagementMutationController.isDeleteRiskPending,
                        () => {
                            if (
                                sharedRiskManagementMutationController.hasError
                            ) {
                                return;
                            }

                            snackbarController.addSnackbar({
                                id: 'risk-delete-success',
                                props: {
                                    title: t`Risk deleted`,
                                    severity: 'success',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            riskManagementListQuery.invalidate();

                            navigateTo(
                                `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks`,
                            );

                            modalController.closeModal(modalId);
                        },
                    );
                }}
            />
        ),
        size: 'md',
        centered: true,
        disableClickOutsideToClose: false,
    });
});

const DeleteRiskComponent = observer(() => {
    const { riskDetails } = sharedRiskDetailsController;
    const { isScalingRiskFoundationEnabled, hasRiskManagePermission } =
        sharedFeatureAccessModel;

    const shouldShowDeleteButton =
        hasRiskManagePermission &&
        (isScalingRiskFoundationEnabled || riskDetails?.source === 'CUSTOM');

    return (
        <ActionStack
            data-id="risk-details-content-header-action-stack"
            gap={dimension3x}
            stacks={[
                {
                    id: 'create-risk-button-stack',
                    actions: shouldShowDeleteButton
                        ? [
                              {
                                  actionType: 'dropdown',
                                  id: 'create-risk-button',
                                  typeProps: {
                                      label: t`Risk header options`,
                                      isIconOnly: true,
                                      level: 'secondary',
                                      startIconName: 'HorizontalMenu',
                                      align: 'end',
                                      colorScheme: 'primary',
                                      items: [
                                          {
                                              id: `delete-risk`,
                                              label: t`Delete risk`,
                                              colorScheme: 'critical',
                                              onSelect: action(() => {
                                                  openDeleteRiskModalAction();
                                              }),
                                          },
                                      ],
                                  },
                              },
                          ]
                        : [],
                },
            ]}
        />
    );
});

export class RiskDetailsContentHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return (
            sharedRiskDetailsController.isLoading &&
            sharedRiskSettingsController.isLoading
        );
    }

    get title(): string {
        return `${sharedRiskDetailsController.riskDetails?.title}`;
    }

    get slot(): React.JSX.Element {
        return (
            <Metadata
                colorScheme="neutral"
                label={`${sharedRiskDetailsController.riskDetails?.riskId}`}
                type="tag"
            />
        );
    }

    get breadcrumbs(): Breadcrumb[] {
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!currentWorkspaceId) {
            return [];
        }

        return [
            {
                label: t`Register`,
                pathname: `${routeController.userPartOfUrl}/risk/management/registers/1/register-risks`,
            },
        ];
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { riskDetails } = sharedRiskDetailsController;
        const { riskSettings } = sharedRiskSettingsController;

        const inheritMetrics = calculateRiskMetrics(
            riskDetails?.score ?? 0,
            riskSettings,
        );

        const residualMetrics = calculateRiskMetrics(
            riskDetails?.residualScore ?? 0,
            riskSettings,
        );

        return [
            {
                id: 'treatment',
                'data-id': 'treatment',
                label: t`Treatment`,
                value: riskDetails?.treatmentPlan
                    ? getRiskTreatmentLabel(riskDetails.treatmentPlan)
                    : '—',
                type: 'TEXT',
            },
            {
                id: 'score',
                'data-id': 'score',
                label: t`Inherent score`,
                value: (
                    <RiskScore
                        intensity="strong"
                        severity={inheritMetrics.severity}
                        scoreNumber={riskDetails?.score}
                        label={capitalize(inheritMetrics.threshold?.name)}
                    />
                ),
                type: 'REACT_NODE',
            },
            {
                id: 'residual-score',
                'data-id': 'residual-score',
                label: t`Residual score`,
                value: (
                    <RiskScore
                        intensity="strong"
                        severity={residualMetrics.severity}
                        scoreNumber={riskDetails?.residualScore}
                        label={capitalize(residualMetrics.threshold?.name)}
                    />
                ),
                type: 'REACT_NODE',
            },
            {
                id: 'owner',
                'data-id': 'owner',
                label: t`Owner`,
                value: (() => {
                    if (!riskDetails?.owners || isEmpty(riskDetails.owners)) {
                        return null;
                    }

                    if (riskDetails.owners.length === 1) {
                        const owner = riskDetails.owners[0];
                        const { firstName, lastName, id, avatarUrl } = owner;
                        const fullName = getFullName(firstName, lastName);
                        const fallbackText = getInitials(fullName);

                        return (
                            <AvatarIdentity
                                key={id}
                                primaryLabel={fullName}
                                fallbackText={fallbackText}
                                imgSrc={avatarUrl}
                                data-id="xTMIBGty"
                            />
                        );
                    }

                    return (
                        <AvatarStack
                            data-id="xTMIBGty"
                            avatarData={riskDetails.owners.map((owner) => {
                                const { firstName, lastName, avatarUrl } =
                                    owner;
                                const fullName = getFullName(
                                    firstName,
                                    lastName,
                                );
                                const fallbackText = getInitials(fullName);

                                return {
                                    primaryLabel: fullName,
                                    fallbackText,
                                    imgSrc: avatarUrl,
                                    visibleItemsLimit: 3,
                                };
                            })}
                        />
                    );
                })(),
                type: 'REACT_NODE',
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        return <DeleteRiskComponent />;
    }
}
