import { VULNERABILITY_DATE_RANGE_VALUES } from '@controllers/risk-vulnerabilities';
import type { VulnerabilityFindingResponseDto } from '@globals/api-sdk/types';
import { getVulnerabilitiesFilterDateRangeLabel } from '../helpers/get-vulnerabilities-filter-date-range-label.helper';
import { getVulnerabilitiesFilterSeverityLabel } from '../helpers/get-vulnerabilities-filter-severity-label.helper';
import { getVulnerabilitiesFilterStatusLabel } from '../helpers/get-vulnerabilities-filter-status-label.helper';

export const VULNERABILITY_STATUS_VALUES = [
    'OPEN',
    'IGNORED',
    'CLOSED',
] as const satisfies readonly VulnerabilityFindingResponseDto['status'][];

export const getVulnerabilityStatusOptions = (): {
    label: string;
    value: (typeof VULNERABILITY_STATUS_VALUES)[number];
}[] =>
    VULNERABILITY_STATUS_VALUES.map((value) => ({
        label: getVulnerabilitiesFilterStatusLabel(value),
        value,
    }));

export const VULNERABILITY_SEVERITY_VALUES = [
    'CRITICAL',
    'HIGH',
    'MEDIUM',
    'LOW',
] as const satisfies readonly VulnerabilityFindingResponseDto['severity'][];

export const getVulnerabilitySeverityOptions = (): {
    id: (typeof VULNERABILITY_SEVERITY_VALUES)[number];
    label: string;
    value: (typeof VULNERABILITY_SEVERITY_VALUES)[number];
}[] =>
    VULNERABILITY_SEVERITY_VALUES.map((value) => ({
        id: value,
        label: getVulnerabilitiesFilterSeverityLabel(value),
        value,
    }));

export const VULNERABILITY_AVAILABILITY_VALUES = [
    true,
    false,
] as const satisfies readonly VulnerabilityFindingResponseDto['fixAvailability'][];

export const VULNERABILITY_AVAILABILITY_OPTIONS =
    VULNERABILITY_AVAILABILITY_VALUES.map((fixAvailability) => ({
        id: fixAvailability ? 'PUBLISHED' : 'NOT_PUBLISHED',
        label: fixAvailability ? 'Available' : 'Not available',
        value: fixAvailability ? 'PUBLISHED' : 'NOT_PUBLISHED',
    }));

export const getVulnerabilitySlaDateRangeOptions = (): {
    id: (typeof VULNERABILITY_DATE_RANGE_VALUES)[number];
    label: string;
    value: (typeof VULNERABILITY_DATE_RANGE_VALUES)[number];
}[] =>
    VULNERABILITY_DATE_RANGE_VALUES.map((value) => ({
        id: value,
        label: getVulnerabilitiesFilterDateRangeLabel(value),
        value,
    }));
