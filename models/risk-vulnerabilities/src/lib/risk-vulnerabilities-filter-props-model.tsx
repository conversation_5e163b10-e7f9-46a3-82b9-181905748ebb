import type { ComponentProps } from 'react';
import {
    RISK_VULNERABILITIES_FILTER_DUE_DATE_ID,
    RISK_VULNERABILITIES_FILTER_FIX_AVAILABILITY_ID,
    RISK_VULNERABILITIES_FILTER_RESOURCE_ID,
    RISK_VULNERABILITIES_FILTER_SEVERITY_ID,
    RISK_VULNERABILITIES_FILTER_STATUS_ID,
    sharedRiskVulnerabilitiesController,
} from '@controllers/risk-vulnerabilities';
import type { Datatable } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import { makeAutoObservable } from '@globals/mobx';
import {
    getVulnerabilitySeverityOptions,
    getVulnerabilitySlaDateRangeOptions,
    getVulnerabilityStatusOptions,
    VULNERABILITY_AVAILABILITY_OPTIONS,
} from './constants/risk-vulnerabilities-filter-props.constants';
import { getVulnerabilityFilterLabel } from './helpers/get-vulnerability-filter-label.helper';

export type VulnerabilitiesFilterProps = ComponentProps<
    typeof Datatable
>['filterProps'];

export class VulnerabilitiesFiltersModel {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * TODO: implement the correct combobox filter when infinite queries are ready at https://drata.atlassian.net/browse/ENG-66498.
     */
    get resourceSelectFilter(): Filter {
        const { resources } = sharedRiskVulnerabilitiesController;

        return {
            filterType: 'combobox',
            id: RISK_VULNERABILITIES_FILTER_RESOURCE_ID,
            label: getVulnerabilityFilterLabel(
                RISK_VULNERABILITIES_FILTER_RESOURCE_ID,
            ),
            options: resources,
        };
    }

    get filters(): VulnerabilitiesFilterProps {
        return {
            filters: [
                /**
                 * TODO: implement the correct combobox filter when infinite queries are ready at https://drata.atlassian.net/browse/ENG-66498.
                 */
                this.resourceSelectFilter,
                {
                    filterType: 'select',
                    id: RISK_VULNERABILITIES_FILTER_DUE_DATE_ID,
                    label: getVulnerabilityFilterLabel(
                        RISK_VULNERABILITIES_FILTER_DUE_DATE_ID,
                    ),
                    options: getVulnerabilitySlaDateRangeOptions(),
                },
                {
                    filterType: 'checkbox',
                    id: RISK_VULNERABILITIES_FILTER_STATUS_ID,
                    label: getVulnerabilityFilterLabel(
                        RISK_VULNERABILITIES_FILTER_STATUS_ID,
                    ),
                    options: getVulnerabilityStatusOptions(),
                },
                {
                    filterType: 'checkbox',
                    id: RISK_VULNERABILITIES_FILTER_SEVERITY_ID,
                    label: getVulnerabilityFilterLabel(
                        RISK_VULNERABILITIES_FILTER_SEVERITY_ID,
                    ),
                    options: getVulnerabilitySeverityOptions(),
                },
                {
                    filterType: 'checkbox',
                    id: RISK_VULNERABILITIES_FILTER_FIX_AVAILABILITY_ID,
                    label: getVulnerabilityFilterLabel(
                        RISK_VULNERABILITIES_FILTER_FIX_AVAILABILITY_ID,
                    ),
                    options: VULNERABILITY_AVAILABILITY_OPTIONS,
                },
            ],
        };
    }
}
