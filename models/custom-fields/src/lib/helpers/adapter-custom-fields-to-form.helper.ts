import { isEmpty, isNil, isNumber, isObject, isString } from 'lodash-es';
import { z } from 'zod';
import type { CustomFieldsSubmissionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { validateUrlWithTLDCheck } from '@globals/zod';
import type { FormSchema, FormValues } from '@ui/forms';

const DEFAULT_EMPTY_OPTION = {
    id: '',
    label: '-',
    value: undefined,
};

export function adapterCustomFieldsToFormSchema(
    customFields: Partial<CustomFieldsSubmissionResponseDto>[],
): FormSchema {
    const schema: FormSchema = {};

    customFields.forEach((field) => {
        if (!field.customFieldId || !field.name || !field.fieldType) {
            return;
        }

        const fieldKey = `customField_${field.customFieldId}`;
        const initialValue = field.submission?.value ?? '';
        const safeInitialValue =
            isString(initialValue) || isNumber(initialValue)
                ? String(initialValue)
                : '';

        const fieldName = field.name;
        const fieldDescription = field.description;
        const isRequired = field.isRequired ?? false;

        switch (field.fieldType) {
            case 'TEXT': {
                schema[fieldKey] = {
                    type: 'text',
                    label: fieldName,
                    helpText: fieldDescription || undefined,
                    isOptional: !isRequired,
                    initialValue: safeInitialValue,
                    validator: isRequired
                        ? z.string().refine((val) => !isEmpty(val.trim()), {
                              message: t`${fieldName} is required`,
                          })
                        : z.string().optional(),
                };
                break;
            }

            case 'LONG_TEXT': {
                schema[fieldKey] = {
                    type: 'textarea',
                    label: fieldName,
                    helpText: fieldDescription || undefined,
                    isOptional: !isRequired,
                    initialValue: safeInitialValue,
                    validator: isRequired
                        ? z.string().refine((val) => !isEmpty(val.trim()), {
                              message: t`${fieldName} is required`,
                          })
                        : z.string().optional(),
                };
                break;
            }

            case 'OPTIONS': {
                if (!field.options) {
                    break;
                }

                const options = field.options
                    .filter((opt) => !opt.isHidden)
                    .map((opt) => ({
                        id: opt.id.toString(),
                        label: opt.value,
                        value: opt.value,
                    }));

                const allOptions = isRequired
                    ? options
                    : [DEFAULT_EMPTY_OPTION, ...options];

                const selectedOption = allOptions.find(
                    (opt) => opt.id.toString() === safeInitialValue,
                );

                schema[fieldKey] = {
                    type: 'select',
                    label: fieldName,
                    helpText: fieldDescription || undefined,
                    isOptional: !isRequired,
                    initialValue: selectedOption,
                    options: allOptions,
                    loaderLabel: t`Loading options...`,
                };
                break;
            }

            case 'OPTIONS_NUMERIC': {
                if (!field.numericOptions) {
                    break;
                }

                const options = field.numericOptions
                    .filter((opt) => !opt.isHidden)
                    .map((opt) => ({
                        id: opt.id.toString(),
                        label: opt.value.toString(),
                        value: opt.value.toString(),
                    }));

                const allNumericOptions = isRequired
                    ? options
                    : [DEFAULT_EMPTY_OPTION, ...options];

                const selectedNumericOption = allNumericOptions.find(
                    (opt) => opt.id.toString() === safeInitialValue,
                );

                schema[fieldKey] = {
                    type: 'select',
                    label: fieldName,
                    helpText: fieldDescription || undefined,
                    isOptional: !isRequired,
                    initialValue: selectedNumericOption,
                    options: allNumericOptions,
                    loaderLabel: t`Loading options...`,
                };
                break;
            }

            // TODO: Pending to add currency form type: https://drata.atlassian.net/browse/ENG-71520
            case 'CURRENCY': {
                if (!field.currency) {
                    break;
                }
                const safeInitial =
                    isString(field.submission?.value) ||
                    isNumber(field.submission?.value)
                        ? String(field.submission.value)
                        : undefined;

                const currencyCode = field.currency.code;

                const currencyValidator = isRequired
                    ? z
                          .string({ message: t`${fieldName} is required` })
                          .refine((val) => !isEmpty(val.trim()), {
                              message: t`${fieldName} is required`,
                          })
                          .refine((val) => !Number.isNaN(Number(val)), {
                              message: t`${fieldName} must be a number`,
                          })
                    : z
                          .string()
                          .refine((val) => !Number.isNaN(Number(val)), {
                              message: t`${fieldName} must be a number`,
                          })
                          .optional();

                schema[fieldKey] = {
                    type: 'text',
                    label: fieldName,
                    helpText: fieldDescription
                        ? t`${fieldDescription} (Currency: ${currencyCode})`
                        : t`Currency: ${currencyCode}`,
                    isOptional: !isRequired,
                    validator: currencyValidator,
                    initialValue: safeInitial,
                };
                break;
            }

            case 'NUMBER': {
                const safeInitial =
                    isString(field.submission?.value) ||
                    isNumber(field.submission?.value)
                        ? String(field.submission.value)
                        : undefined;
                const numberValidator = isRequired
                    ? z
                          .string()
                          .refine((val) => !isEmpty(val.trim()), {
                              message: t`${fieldName} is required`,
                          })
                          .refine((val) => !Number.isNaN(Number(val)), {
                              message: t`${fieldName} must be a number`,
                          })
                    : z
                          .string()
                          .refine((val) => !Number.isNaN(Number(val)), {
                              message: t`${fieldName} must be a number`,
                          })
                          .optional();

                schema[fieldKey] = {
                    type: 'text',
                    label: fieldName,
                    helpText: fieldDescription
                        ? t`${fieldDescription} (Numbers only)`
                        : t`Numbers only`,
                    isOptional: !isRequired,
                    validator: numberValidator,
                    initialValue: safeInitial,
                };
                break;
            }
            case 'FORMULA': {
                schema[fieldKey] = {
                    type: 'text',
                    label: fieldName,
                    helpText: fieldDescription
                        ? t`${fieldDescription} (Formula field - calculated automatically)`
                        : t`Formula field - calculated automatically`,
                    isOptional: true,
                    validator: z.string().optional(),
                    initialValue: safeInitialValue,
                };
                break;
            }

            case 'URL': {
                const urlValidator = isRequired
                    ? validateUrlWithTLDCheck(t`${fieldName} is required`)
                    : validateUrlWithTLDCheck().optional();

                schema[fieldKey] = {
                    type: 'text',
                    label: fieldName,
                    helpText: fieldDescription
                        ? t`${fieldDescription} (URL)`
                        : t`URL`,
                    isOptional: !isRequired,
                    validator: urlValidator,
                    initialValue: safeInitialValue,
                };
                break;
            }

            default: {
                const fieldType = field.fieldType as string;

                schema[fieldKey] = {
                    type: 'text',
                    label: fieldName,
                    helpText: fieldDescription
                        ? t`${fieldDescription} (Unknown field type: ${fieldType})`
                        : t`Unknown field type: ${fieldType}`,
                    isOptional: !isRequired,
                    validator: isRequired
                        ? z.string().refine((val) => !isEmpty(val.trim()), {
                              message: t`${fieldName} is required`,
                          })
                        : z.string().optional(),
                    initialValue: safeInitialValue,
                };
                break;
            }
        }
    });

    return schema;
}

export function getValueFromFormValue(
    formValue: unknown,
    fieldType: CustomFieldsSubmissionResponseDto['fieldType'],
): string | number | undefined {
    if (fieldType === 'CURRENCY' || fieldType === 'NUMBER') {
        return isNil(formValue) ||
            (isString(formValue) && formValue.trim() === '')
            ? undefined
            : Number(formValue);
    }

    if (
        isObject(formValue) &&
        'id' in formValue &&
        formValue.id &&
        isString(formValue.id)
    ) {
        return Number(formValue.id);
    }

    if (isString(formValue) || isNumber(formValue)) {
        return formValue;
    }

    return undefined;
}

export function extractCustomFieldsFromFormValues(
    formValues: FormValues,
    customFields: CustomFieldsSubmissionResponseDto[],
): Record<string, unknown> | null {
    const customFieldEntries = Object.entries(formValues).filter(([key]) =>
        key.startsWith('customField_'),
    );

    if (isEmpty(customFieldEntries)) {
        return null;
    }

    const customFieldSubmissions = customFields.map((field) => {
        const { customFieldId, customFieldLocationId } = field;
        const customFieldName = field.name;
        const { fieldType } = field;
        const value = formValues[`customField_${customFieldId}`];

        return {
            customFieldId,
            customFieldName,
            value: getValueFromFormValue(value, fieldType),
            customFieldLocationId,
        };
    });

    return {
        customFieldSubmissions,
    };
}
