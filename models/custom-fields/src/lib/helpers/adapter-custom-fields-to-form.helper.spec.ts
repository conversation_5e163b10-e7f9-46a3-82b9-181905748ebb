import { beforeAll, describe, expect, test, vi } from 'vitest';
import type { CustomFieldsSubmissionResponseDto } from '@globals/api-sdk/types';
import { i18n } from '@globals/i18n';
import {
    adapterCustomFieldsToFormSchema,
    extractCustomFieldsFromFormValues,
    getValueFromFormValue,
} from './adapter-custom-fields-to-form.helper';

vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));

describe('adapterCustomFieldsToFormSchema', () => {
    beforeAll(() => {
        i18n.load('en', {});
        i18n.activate('en');
    });

    describe('basic functionality', () => {
        test('should return empty schema for empty array', () => {
            const result = adapterCustomFieldsToFormSchema([]);

            expect(result).toStrictEqual({});
        });

        test('should skip fields without required properties', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {}, // Missing all required properties
                { customFieldId: 1 }, // Missing name and fieldType
                { name: 'Test' }, // Missing customFieldId and fieldType
                { fieldType: 'TEXT' }, // Missing customFieldId and name
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result).toStrictEqual({});
        });
    });

    describe('tEXT field type', () => {
        test('should create text field schema for required TEXT field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 1,
                    name: 'Required Field',
                    fieldType: 'TEXT',
                    isRequired: true,
                    description: 'This is a required field',
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result).toStrictEqual({
                customField_1: {
                    type: 'text',
                    label: 'Required Field',
                    helpText: 'This is a required field',
                    isOptional: false,
                    initialValue: '',
                    validator: expect.any(Object),
                },
            });
        });

        test('should create text field schema for optional TEXT field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 2,
                    name: 'Optional Field',
                    fieldType: 'TEXT',
                    isRequired: false,
                    submission: { submissionId: 1, value: 'existing value' },
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_2).toStrictEqual({
                type: 'text',
                label: 'Optional Field',
                helpText: undefined,
                isOptional: true,
                initialValue: 'existing value',
                validator: expect.any(Object),
            });
        });

        test('should handle TEXT field with undefined isRequired', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 3,
                    name: 'Field with undefined isRequired',
                    fieldType: 'TEXT',
                    // isRequired is undefined
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_3.isOptional).toBeTruthy(); // Should default to optional
        });
    });

    describe('lONG_TEXT field type', () => {
        test('should create textarea field schema for LONG_TEXT field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 4,
                    name: 'Long Text Field',
                    fieldType: 'LONG_TEXT',
                    isRequired: true,
                    description: 'Enter detailed information',
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_4).toStrictEqual({
                type: 'textarea',
                label: 'Long Text Field',
                helpText: 'Enter detailed information',
                isOptional: false,
                initialValue: '',
                validator: expect.any(Object),
            });
        });
    });

    describe('oPTIONS field type', () => {
        test('should create select field schema for OPTIONS field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 5,
                    name: 'Options Field',
                    fieldType: 'OPTIONS',
                    isRequired: true,
                    options: [
                        {
                            id: 1,
                            value: 'Option 1',
                            isHidden: false,
                            position: 1,
                        },
                        {
                            id: 2,
                            value: 'Option 2',
                            isHidden: false,
                            position: 2,
                        },
                        {
                            id: 3,
                            value: 'Hidden Option',
                            isHidden: true,
                            position: 3,
                        },
                    ],
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_5).toStrictEqual({
                type: 'select',
                label: 'Options Field',
                helpText: undefined,
                isOptional: false,
                initialValue: undefined,
                options: [
                    { id: '1', label: 'Option 1', value: 'Option 1' },
                    { id: '2', label: 'Option 2', value: 'Option 2' },
                ],
                loaderLabel: 'Loading options...',
            });
        });

        test('should skip OPTIONS field when options array is undefined', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 6,
                    name: 'Options Field Without Options',
                    fieldType: 'OPTIONS',
                    isRequired: true,
                    // options is undefined
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_6).toBeUndefined();
        });

        test('should handle OPTIONS field with selected value', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 7,
                    name: 'Options Field with Selection',
                    fieldType: 'OPTIONS',
                    isRequired: false,
                    options: [
                        {
                            id: 1,
                            value: 'Option 1',
                            isHidden: false,
                            position: 1,
                        },
                        {
                            id: 2,
                            value: 'Option 2',
                            isHidden: false,
                            position: 2,
                        },
                    ],
                    submission: { submissionId: 1, value: 2 },
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_7.initialValue).toStrictEqual({
                id: '2',
                label: 'Option 2',
                value: 'Option 2',
            });
        });
    });

    describe('cURRENCY field type', () => {
        test('should create text field schema for CURRENCY field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 10,
                    name: 'Currency Field',
                    fieldType: 'CURRENCY',
                    isRequired: true,
                    description: 'Enter amount',
                    currency: { id: 1, code: 'USD' },
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_10).toStrictEqual({
                type: 'text',
                label: 'Currency Field',
                helpText: 'Enter amount (Currency: USD)',
                isOptional: false,
                validator: expect.any(Object),
                initialValue: undefined,
            });
        });

        test('should skip CURRENCY field when currency is undefined', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 11,
                    name: 'Currency Field Without Currency',
                    fieldType: 'CURRENCY',
                    isRequired: true,
                    // currency is undefined
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_11).toBeUndefined();
        });
    });

    describe('nUMBER field type', () => {
        test('should create text field schema for NUMBER field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 12,
                    name: 'Number Field',
                    fieldType: 'NUMBER',
                    isRequired: true,
                    description: 'Enter a number',
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_12).toStrictEqual({
                type: 'text',
                label: 'Number Field',
                helpText: 'Enter a number (Numbers only)',
                isOptional: false,
                validator: expect.any(Object),
                initialValue: undefined,
            });
        });
    });

    describe('fORMULA field type', () => {
        test('should create text field schema for FORMULA field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 13,
                    name: 'Formula Field',
                    fieldType: 'FORMULA',
                    description: 'Calculated field',
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_13).toStrictEqual({
                type: 'text',
                label: 'Formula Field',
                helpText:
                    'Calculated field (Formula field - calculated automatically)',
                isOptional: true,
                validator: expect.any(Object),
                initialValue: '',
            });
        });
    });

    describe('uRL field type', () => {
        test('should create text field schema for required URL field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 14,
                    name: 'Website URL',
                    fieldType: 'URL',
                    isRequired: true,
                    description: 'Company website',
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_14).toStrictEqual({
                type: 'text',
                label: 'Website URL',
                helpText: 'Company website (URL)',
                isOptional: false,
                validator: expect.any(Object),
                initialValue: '',
            });
        });

        test('should create text field schema for optional URL field', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 15,
                    name: 'Optional URL',
                    fieldType: 'URL',
                    isRequired: false,
                    submission: {
                        submissionId: 1,
                        value: 'https://example.com',
                    },
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_15).toStrictEqual({
                type: 'text',
                label: 'Optional URL',
                helpText: 'URL',
                isOptional: true,
                validator: expect.any(Object),
                initialValue: 'https://example.com',
            });
        });
    });

    describe('edge cases', () => {
        test('should handle null submission value', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 16,
                    name: 'Test Field',
                    fieldType: 'TEXT',
                    isRequired: false,
                    submission: undefined,
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_16.initialValue).toBe('');
        });

        test('should handle numeric submission value', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 17,
                    name: 'Test Field',
                    fieldType: 'TEXT',
                    isRequired: false,
                    submission: { submissionId: 1, value: 123 },
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_17.initialValue).toBe('123');
        });

        test('should handle non-string, non-number submission value', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 18,
                    name: 'Test Field',
                    fieldType: 'TEXT',
                    isRequired: false,
                    submission: {
                        submissionId: 1,
                        value: { complex: 'object' },
                    },
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_18.initialValue).toBe('');
        });

        test('should handle undefined submission', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 19,
                    name: 'Test Field',
                    fieldType: 'TEXT',
                    isRequired: false,
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_19.initialValue).toBe('');
        });

        test('should handle submission with undefined value', () => {
            const customFields: Partial<CustomFieldsSubmissionResponseDto>[] = [
                {
                    customFieldId: 20,
                    name: 'Test Field',
                    fieldType: 'TEXT',
                    isRequired: false,
                    submission: { submissionId: 1, value: undefined },
                },
            ];

            const result = adapterCustomFieldsToFormSchema(customFields);

            expect(result.customField_20.initialValue).toBe('');
        });
    });
});

describe('getValueFromFormValue', () => {
    describe('cURRENCY and NUMBER field types', () => {
        test('should convert string to number for CURRENCY field type', () => {
            const result = getValueFromFormValue('123.45', 'CURRENCY');

            expect(result).toBe(123.45);
        });

        test('should convert string to number for NUMBER field type', () => {
            const result = getValueFromFormValue('456', 'NUMBER');

            expect(result).toBe(456);
        });

        test('should handle invalid number strings for CURRENCY', () => {
            const result = getValueFromFormValue('invalid', 'CURRENCY');

            expect(result).toBeNaN();
        });

        test('should handle invalid number strings for NUMBER', () => {
            const result = getValueFromFormValue('invalid', 'NUMBER');

            expect(result).toBeNaN();
        });

        test('should handle empty string for CURRENCY', () => {
            const result = getValueFromFormValue('', 'CURRENCY');

            expect(result).toBeUndefined();
        });

        test('should handle empty string for NUMBER', () => {
            const result = getValueFromFormValue('', 'NUMBER');

            expect(result).toBeUndefined();
        });
    });

    describe('number values', () => {
        test('should return number as-is when formValue is already a number', () => {
            const result = getValueFromFormValue(42, 'TEXT');

            expect(result).toBe(42);
        });

        test('should return number for CURRENCY field type when already a number', () => {
            const result = getValueFromFormValue(99.99, 'CURRENCY');

            expect(result).toBe(99.99);
        });
    });

    describe('string values', () => {
        test('should return string as-is for TEXT field type', () => {
            const result = getValueFromFormValue('hello world', 'TEXT');

            expect(result).toBe('hello world');
        });

        test('should return string as-is for LONG_TEXT field type', () => {
            const result = getValueFromFormValue(
                'long text content',
                'LONG_TEXT',
            );

            expect(result).toBe('long text content');
        });

        test('should return string as-is for FORMULA field type', () => {
            const result = getValueFromFormValue('formula result', 'FORMULA');

            expect(result).toBe('formula result');
        });

        test('should return string as-is for URL field type', () => {
            const result = getValueFromFormValue('https://example.com', 'URL');

            expect(result).toBe('https://example.com');
        });
    });

    describe('object values with id property', () => {
        test('should extract and convert id to number from object for OPTIONS field', () => {
            const formValue = {
                id: '123',
                label: 'Option 1',
                value: 'option1',
            };
            const result = getValueFromFormValue(formValue, 'OPTIONS');

            expect(result).toBe(123);
        });

        test('should extract and convert id to number from object for OPTIONS_NUMERIC field', () => {
            const formValue = { id: '456', label: '456', value: '456' };
            const result = getValueFromFormValue(formValue, 'OPTIONS_NUMERIC');

            expect(result).toBe(456);
        });

        test('should handle object with non-string id', () => {
            const formValue = { id: 789, label: 'Option', value: 'option' };
            const result = getValueFromFormValue(formValue, 'OPTIONS');

            expect(result).toBeUndefined();
        });

        test('should handle object without id property', () => {
            const formValue = { label: 'Option', value: 'option' };
            const result = getValueFromFormValue(formValue, 'OPTIONS');

            expect(result).toBeUndefined();
        });
    });

    describe('edge cases', () => {
        test('should return undefined for null value', () => {
            const result = getValueFromFormValue(null, 'TEXT');

            expect(result).toBeUndefined();
        });

        test('should return undefined for undefined value', () => {
            const result = getValueFromFormValue(undefined, 'TEXT');

            expect(result).toBeUndefined();
        });

        test('should return undefined for boolean value', () => {
            const result = getValueFromFormValue(true, 'TEXT');

            expect(result).toBeUndefined();
        });

        test('should return undefined for array value', () => {
            const result = getValueFromFormValue(['item1', 'item2'], 'TEXT');

            expect(result).toBeUndefined();
        });

        test('should return undefined for object without id', () => {
            const result = getValueFromFormValue({ name: 'test' }, 'TEXT');

            expect(result).toBeUndefined();
        });
    });
});

describe('extractCustomFieldsFromFormValues', () => {
    const mockCustomFields = [
        {
            customFieldId: 1,
            customFieldLocationId: 101,
            name: 'Text Field',
            fieldType: 'TEXT',
            isRequired: true,
            options: [],
            numericOptions: [],
            currency: undefined,
            description: 'A text field',
            submission: undefined,
        },
        {
            customFieldId: 2,
            customFieldLocationId: 102,
            name: 'Number Field',
            fieldType: 'NUMBER',
            isRequired: false,
            options: [],
            numericOptions: [],
            currency: undefined,
            description: 'A number field',
            submission: undefined,
        },
        {
            customFieldId: 3,
            customFieldLocationId: 103,
            name: 'Currency Field',
            fieldType: 'CURRENCY',
            isRequired: true,
            options: [],
            numericOptions: [],
            currency: { id: 1, code: 'USD' },
            description: 'A currency field',
            submission: undefined,
        },
        {
            customFieldId: 4,
            customFieldLocationId: 104,
            name: 'Options Field',
            fieldType: 'OPTIONS',
            isRequired: false,
            options: [
                { id: 1, value: 'Option 1', isHidden: false, position: 1 },
                { id: 2, value: 'Option 2', isHidden: false, position: 2 },
            ],
            numericOptions: [],
            currency: undefined,
            description: 'An options field',
            submission: undefined,
        },
    ] as unknown as CustomFieldsSubmissionResponseDto[];

    describe('basic functionality', () => {
        test('should return null when no custom field entries exist', () => {
            const formValues = {
                regularField: 'value',
                anotherField: 123,
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            expect(result).toBeNull();
        });

        test('should return null when custom field entries are empty', () => {
            const formValues = {
                customField_1: '',
                customField_2: null,
                customField_3: undefined,
                regularField: 'value',
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            expect(result).toStrictEqual({
                customFieldSubmissions: [
                    {
                        customFieldId: 1,
                        customFieldName: 'Text Field',
                        value: '',
                        customFieldLocationId: 101,
                    },
                    {
                        customFieldId: 2,
                        customFieldName: 'Number Field',
                        value: undefined,
                        customFieldLocationId: 102,
                    },
                    {
                        customFieldId: 3,
                        customFieldName: 'Currency Field',
                        value: undefined,
                        customFieldLocationId: 103,
                    },
                    {
                        customFieldId: 4,
                        customFieldName: 'Options Field',
                        value: undefined,
                        customFieldLocationId: 104,
                    },
                ],
            });
        });

        test('should extract custom field submissions with correct structure', () => {
            const formValues = {
                customField_1: 'Hello World',
                customField_2: '123',
                regularField: 'ignored',
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            expect(result).toStrictEqual({
                customFieldSubmissions: [
                    {
                        customFieldId: 1,
                        customFieldName: 'Text Field',
                        value: 'Hello World',
                        customFieldLocationId: 101,
                    },
                    {
                        customFieldId: 2,
                        customFieldName: 'Number Field',
                        value: 123,
                        customFieldLocationId: 102,
                    },
                    {
                        customFieldId: 3,
                        customFieldName: 'Currency Field',
                        value: undefined, // undefined for CURRENCY field type
                        customFieldLocationId: 103,
                    },
                    {
                        customFieldId: 4,
                        customFieldName: 'Options Field',
                        value: undefined, // undefined for OPTIONS field type
                        customFieldLocationId: 104,
                    },
                ],
            });
        });
    });

    describe('field type handling', () => {
        test('should handle TEXT field values correctly', () => {
            const formValues = {
                customField_1: 'Sample text value',
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            const submission = result?.customFieldSubmissions as object[];

            expect(submission[0]).toStrictEqual({
                customFieldId: 1,
                customFieldName: 'Text Field',
                value: 'Sample text value',
                customFieldLocationId: 101,
            });
        });

        test('should handle NUMBER field values correctly', () => {
            const formValues = {
                customField_2: '456.78',
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            const submission = result?.customFieldSubmissions as object[];

            expect(submission[1]).toStrictEqual({
                customFieldId: 2,
                customFieldName: 'Number Field',
                value: 456.78,
                customFieldLocationId: 102,
            });
        });

        test('should handle CURRENCY field values correctly', () => {
            const formValues = {
                customField_3: '999.99',
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            const submission = result?.customFieldSubmissions as object[];

            expect(submission[2]).toStrictEqual({
                customFieldId: 3,
                customFieldName: 'Currency Field',
                value: 999.99,
                customFieldLocationId: 103,
            });
        });

        test('should handle OPTIONS field values correctly', () => {
            const formValues = {
                customField_4: {
                    id: '2',
                    label: 'Option 2',
                    value: 'Option 2',
                },
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            const submission = result?.customFieldSubmissions as object[];

            expect(submission[3]).toStrictEqual({
                customFieldId: 4,
                customFieldName: 'Options Field',
                value: 2,
                customFieldLocationId: 104,
            });
        });
    });

    describe('edge cases', () => {
        test('should handle mixed valid and invalid custom field values', () => {
            const formValues = {
                customField_1: 'Valid text',
                customField_2: '', // Empty string - should be filtered out
                customField_3: '100.50',
                customField_4: null, // Null - should be filtered out
                customField_999: 'Non-existent field', // Field not in customFields array
                regularField: 'ignored',
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            expect(result).toStrictEqual({
                customFieldSubmissions: [
                    {
                        customFieldId: 1,
                        customFieldName: 'Text Field',
                        value: 'Valid text',
                        customFieldLocationId: 101,
                    },
                    {
                        customFieldId: 2,
                        customFieldName: 'Number Field',
                        value: undefined, // undefined for NUMBER field type (empty string)
                        customFieldLocationId: 102,
                    },
                    {
                        customFieldId: 3,
                        customFieldName: 'Currency Field',
                        value: 100.5,
                        customFieldLocationId: 103,
                    },
                    {
                        customFieldId: 4,
                        customFieldName: 'Options Field',
                        value: undefined, // No value in formValues
                        customFieldLocationId: 104,
                    },
                ],
            });
        });

        test('should handle empty customFields array', () => {
            const formValues = {
                customField_1: 'Some value',
            };

            const result = extractCustomFieldsFromFormValues(formValues, []);

            expect(result).toStrictEqual({
                customFieldSubmissions: [],
            });
        });

        test('should handle all custom fields with empty/null/undefined values', () => {
            const formValues = {
                customField_1: '',
                customField_2: null,
                customField_3: undefined,
                customField_4: '',
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            expect(result).toStrictEqual({
                customFieldSubmissions: [
                    {
                        customFieldId: 1,
                        customFieldName: 'Text Field',
                        value: '',
                        customFieldLocationId: 101,
                    },
                    {
                        customFieldId: 2,
                        customFieldName: 'Number Field',
                        value: undefined,
                        customFieldLocationId: 102,
                    },
                    {
                        customFieldId: 3,
                        customFieldName: 'Currency Field',
                        value: undefined,
                        customFieldLocationId: 103,
                    },
                    {
                        customFieldId: 4,
                        customFieldName: 'Options Field',
                        value: '',
                        customFieldLocationId: 104,
                    },
                ],
            });
        });

        test('should handle numeric values as form values', () => {
            const formValues = {
                customField_1: 123, // Number for text field
                customField_2: 456, // Number for number field
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            const submission = result?.customFieldSubmissions as object[];

            expect(submission[0]).toStrictEqual({
                customFieldId: 1,
                customFieldName: 'Text Field',
                value: 123,
                customFieldLocationId: 101,
            });

            expect(submission[1]).toStrictEqual({
                customFieldId: 2,
                customFieldName: 'Number Field',
                value: 456,
                customFieldLocationId: 102,
            });
        });

        test('should handle complex object values for OPTIONS fields', () => {
            const formValues = {
                customField_4: {
                    id: '1',
                    label: 'Option 1',
                    value: 'Option 1',
                    extraProperty: 'ignored',
                },
            };

            const result = extractCustomFieldsFromFormValues(
                formValues,
                mockCustomFields,
            );

            const submission = result?.customFieldSubmissions as object[];

            expect(submission[3]).toStrictEqual({
                customFieldId: 4,
                customFieldName: 'Options Field',
                value: 1,
                customFieldLocationId: 104,
            });
        });
    });
});
