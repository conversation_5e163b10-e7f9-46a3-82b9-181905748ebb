import React from 'react';
import type { CreateTicketFn } from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';

export const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';

export const openCreateTicketModal = (
    onCreateTicket: CreateTicketFn,
    defaultDescription?: string,
): void => {
    modalController.openModal({
        id: CREATE_TICKET_MODAL_ID,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
        content: () => {
            // We'll import this dynamically to avoid circular dependencies
            const {
                CreateTicketModalView,
            } = require('@views/create-ticket-modal');

            return React.createElement(CreateTicketModalView, {
                onCreateTicket,
                defaultDescription,
            });
        },
    });
};
