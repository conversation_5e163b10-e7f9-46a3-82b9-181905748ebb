import type React from 'react';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { AppLink } from '@ui/app-link';

export const AISettingsDescription = (): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="xl"
            data-id="_-vYUhjg"
            data-testid="AISettingsDescription"
        >
            <Stack direction="column" gap="lg" data-id="_-vYUhjg">
                <Text type="body" size="200" colorScheme="neutral">
                    <Trans>
                        When you opt in to our AI, you get access to a number of
                        different thoughtful ways to enhance and streamline your
                        work across the application.
                    </Trans>
                </Text>

                <Text type="body" size="200" colorScheme="neutral">
                    <Trans>
                        Drata is committed to an approach to AI that safeguards
                        your data.
                    </Trans>
                </Text>

                <Text allowBold type="title" size="200" colorScheme="neutral">
                    <Trans>
                        <strong>Strict data separation.</strong> We enforce
                        strict data separation across all of our customers to
                        ensure your information is kept safe.
                    </Trans>
                </Text>

                <Text allowBold type="title" size="200" colorScheme="neutral">
                    <Trans>
                        <strong>Responsible governance.</strong> We designed our
                        AI approach with fairness, inclusivity, safety,
                        reliability, and privacy in mind.
                    </Trans>
                </Text>

                <Text allowBold type="title" size="200" colorScheme="neutral">
                    <Trans>
                        <strong>Content moderation.</strong> Our team conducts
                        internal reviews and uses automated tools to ensure data
                        quality.
                    </Trans>
                </Text>

                <Text allowBold type="title" size="200" colorScheme="neutral">
                    <Trans>
                        <strong>You&apos;re in control.</strong> You can always
                        change your preferences for AI features in settings.
                    </Trans>
                </Text>
            </Stack>
            <AppLink
                isExternal
                href="https://drata.com/blog/our-ai-philosophy"
                data-id="ai-documentation-link"
                data-testid="ai-documentation-link"
            >
                {t`Read our AI documentation`}
            </AppLink>
        </Stack>
    );
};
