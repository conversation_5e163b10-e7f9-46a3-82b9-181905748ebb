import {
    type AriaAttributes,
    type ChangeEvent,
    type Change<PERSON><PERSON><PERSON>and<PERSON>,
    type FocusEvent,
    type FocusEventHandler,
    type ForwardedRef,
    forwardRef,
    type InputHTMLAttributes,
    type KeyboardEvent,
    type KeyboardEventHandler,
    type <PERSON><PERSON>vent,
    type Mouse<PERSON>ventHandler,
    type Ref,
} from 'react';
import { Button } from '@cosmos/components/button';
import {
    FEEDBACK_BORDER_COLORS,
    type FeedbackType,
} from '@cosmos/components/field-feedback';
import { Icon } from '@cosmos/components/icon';
import {
    StructuredListItem,
    type StructuredListItemData,
} from '@cosmos/components/list-box';
import { Loader } from '@cosmos/components/loader';
import { StyledFakeInputDiv, StyledFlexDiv, StyledInput } from './styles';

interface ComboboxTriggerProps {
    /**
     * Provides a hint to the user agent specifying how to, or indeed whether to, prefill a form control.
     */
    autoComplete: InputHTMLAttributes<HTMLInputElement>['autoComplete'];
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Whether the form control is disabled.
     */
    disabled?: boolean;
    /**
     * The type of feedback given from an external source. Maps to the FieldFeedback component.
     */
    feedbackType?: FeedbackType;
    /**
     * A string used by CSS Grid for grid-template layouts.
     */
    gridArea?: string;
    /**
     * Current value of the input.
     */
    value: string;
    /**
     * Name of the form control. Submitted with the form as part of a name/value pair.
     */
    name: string;
    /**
     * Function called when input is blurred.
     */
    onBlur?: FocusEventHandler<HTMLElement>;
    /**
     * Function called when value changes.
     */
    onChange?: ChangeEventHandler<HTMLInputElement>;
    /**
     * Function called when the input is clicked.
     */
    onClick?: MouseEventHandler<HTMLElement>;
    /**
     * Function called when input is focused.
     */
    onFocus?: FocusEventHandler<HTMLElement>;
    /**
     * Function called when key down.
     */
    onKeyDown?: KeyboardEventHandler<HTMLElement>;
    /**
     * Text shown when no options are selected.
     */
    placeholderText?: string;
    /**
     * A value is required for the form to be submittable.
     */
    required?: boolean;
    /**
     * Item shown when an option is selected.
     */
    selectedItem?: StructuredListItemData;
    /**
     * Indicates if an async search is being performed.
     */
    isLoading?: boolean;
    /**
     * From downshift, explicitly redeclared for sake of dependency inversion.
     */
    'aria-autocomplete': AriaAttributes['aria-autocomplete'];
    'aria-activedescendant': AriaAttributes['aria-activedescendant'];
    'aria-controls': AriaAttributes['aria-controls'];
    'aria-expanded': AriaAttributes['aria-expanded'];
    'aria-labelledby': AriaAttributes['aria-labelledby'];
    'aria-describedby': AriaAttributes['aria-describedby'];
    id: string;
    role: 'combobox';
    /**
     * Required to attach the popup to the fake input.
     */
    fakeInputRef: Ref<HTMLDivElement>;
    /**
     * Read-only.
     */
    readOnly?: boolean;
    clearSelectedItemButtonLabel?: string;
    onClear: MouseEventHandler<HTMLButtonElement>;
}

const BaseComboboxTrigger = (
    {
        'aria-autocomplete': ariaAutocomplete,
        'aria-activedescendant': ariaActiveDescendant,
        'aria-controls': ariaControls,
        'aria-describedby': ariaDescribedBy,
        'aria-expanded': ariaExpanded,
        'aria-labelledby': ariaLabelledBy,
        'data-id': dataId = 'select-trigger',
        disabled = false,
        feedbackType = undefined,
        autoComplete = 'off',
        gridArea = undefined,
        id,
        name,
        onBlur = (e: FocusEvent<HTMLElement>) => e,
        onChange = (e: ChangeEvent<HTMLElement>) => e,
        onClick = (e: MouseEvent<HTMLElement>) => e,
        onFocus = (e: FocusEvent<HTMLElement>) => e,
        onKeyDown = (e: KeyboardEvent<HTMLElement>) => e,
        placeholderText = undefined,
        required = false,
        role = 'combobox',
        selectedItem = undefined,
        isLoading = false,
        value,
        fakeInputRef,
        readOnly = false,
        clearSelectedItemButtonLabel = undefined,
        onClear,
    }: ComboboxTriggerProps,
    ref: ForwardedRef<HTMLInputElement>,
) => {
    const shouldShowSelectedItem = Boolean(selectedItem) && value === '';

    const feedbackBorderColor =
        feedbackType && FEEDBACK_BORDER_COLORS[feedbackType];

    return (
        <StyledFakeInputDiv
            data-id={`${dataId}-fake-input`}
            $gridArea={gridArea}
            $feedbackBorderColor={feedbackBorderColor}
            ref={fakeInputRef}
            $isReadOnly={readOnly || disabled}
            data-testid="BaseComboboxTrigger"
            onClick={readOnly ? undefined : onClick}
        >
            <Icon name="Search" size="200" />
            <StyledFlexDiv>
                <StyledInput
                    data-1p-ignore
                    $shouldMinimizeWidth={shouldShowSelectedItem}
                    aria-activedescendant={ariaActiveDescendant}
                    aria-autocomplete={ariaAutocomplete}
                    aria-controls={ariaControls}
                    aria-describedby={ariaDescribedBy}
                    aria-expanded={ariaExpanded}
                    aria-labelledby={ariaLabelledBy}
                    autoComplete={autoComplete}
                    aria-disabled={readOnly}
                    data-id={`${dataId}-input`}
                    disabled={disabled}
                    id={id}
                    name={name}
                    placeholder={placeholderText}
                    ref={ref}
                    required={required}
                    role={role}
                    type="text"
                    value={value}
                    readOnly={readOnly}
                    onBlur={onBlur}
                    onChange={readOnly ? undefined : onChange}
                    onFocus={onFocus}
                    onKeyDown={readOnly ? undefined : onKeyDown}
                />
                {shouldShowSelectedItem && selectedItem && (
                    <>
                        <StructuredListItem
                            hideDescription
                            shouldWrap={false}
                            startSlot={selectedItem.startSlot}
                            endSlot={selectedItem.endSlot}
                            label={selectedItem.label}
                            description={selectedItem.description}
                        />
                        {clearSelectedItemButtonLabel && (
                            <Button
                                isIconOnly
                                data-id={`${dataId}-clear-selected-item-button`}
                                colorScheme="neutral"
                                label={clearSelectedItemButtonLabel}
                                level="tertiary"
                                size="sm"
                                startIconName="Cancel"
                                onClick={(e) => {
                                    e.stopPropagation(); // necessary to avoid opening/closing the dropdown
                                    onClear(e);
                                }}
                            />
                        )}
                    </>
                )}
            </StyledFlexDiv>
            {isLoading ? (
                <Loader
                    isSpinnerOnly
                    colorScheme="primary"
                    label="Loading"
                    size="200"
                    spinnerType="spinner"
                />
            ) : (
                <Icon name="ChevronDown" size="200" />
            )}
        </StyledFakeInputDiv>
    );
};

export const ComboboxTrigger = forwardRef(BaseComboboxTrigger);
