import { FormField, type FormFieldProps } from '@cosmos/components/form-field';
import { Input, type InputProps } from '@cosmos/components/input';
import { DEFAULT_DATA_ID } from './constants';

export interface TextFieldProps {
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Label text informs users of the expected information in a form field.
     */
    label: FormFieldProps['label'];
    /** Optional overrides for label styles for non-standard uses.*/
    labelStyleOverrides?: FormFieldProps['labelStyleOverrides'];
    /**
     * Optional text providing additional guidance or context to the user.
     */
    helpText?: FormFieldProps['helpText'];
    /**
     * If true, hides the label element.
     */
    shouldHideLabel?: boolean;
    /**
     * A string indicating that this field is optional to complete.
     */
    optionalText?: FormFieldProps['optionalText'];
    /**
     * A value is required or must be checked for the form to be submittable.
     */
    required?: InputProps['required'];
    /**
     * Whether the form control is disabled.
     */
    disabled?: InputProps['disabled'];
    /**
     * Provide optional feedback to the user based on their interaction with the field.
     */
    feedback?: FormFieldProps['feedback'];
    /**
     * A unique identifier for the form this field is a part of.
     */
    formId: FormFieldProps['formId'];
    /**
     * Name of the form control. Submitted with the form as part of a name/value pair.
     */
    name: InputProps['name'];
    /**
     * Function called when value changes.
     */
    onChange: InputProps['onChange'];
    /**
     * Function called when input is focused.
     */
    onFocus?: InputProps['onFocus'];
    /**
     * Function called when input is blurred.
     */
    onBlur?: InputProps['onBlur'];
    /**
     * Function called when key down.
     */
    onKeyDown?: InputProps['onKeyDown'];
    /**
     * The value of the control.
     */
    value: InputProps['value'];
    /**
     * Indicates whether the text field is read-only.
     */
    readOnly?: boolean;

    /**
     * The type of input to render.
     */
    inputType?: InputProps['type'];
}

/**
 * The TextField component is a basic single-line text field that allows users to enter and edit text input with validation support.
 *
 * [TextField in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=44258-537406&t=QHg4TovE4tsvKYCl-4).
 */
export const TextField = ({
    'data-id': dataId = DEFAULT_DATA_ID,
    label,
    labelStyleOverrides = undefined,
    helpText = undefined,
    shouldHideLabel = false,
    optionalText = undefined,
    required = false,
    disabled = undefined,
    feedback = undefined,
    formId,
    name,
    value,
    inputType = 'text',
    onChange,
    readOnly = false,
    onFocus = undefined,
    onBlur = undefined,
    onKeyDown = undefined,
}: TextFieldProps): React.JSX.Element => {
    return (
        <FormField
            data-id={dataId}
            label={label}
            labelStyleOverrides={labelStyleOverrides}
            helpText={helpText}
            shouldHideLabel={shouldHideLabel}
            optionalText={optionalText}
            required={required}
            feedback={feedback}
            formId={formId}
            name={name as string}
            data-testid="TextField"
            renderInput={({
                describeIds,
                gridArea,
                inputId,
                inputTestId,
                labelId,
                feedbackType,
            }) => {
                return (
                    <Input
                        aria-describedby={describeIds}
                        aria-labelledby={labelId}
                        data-id={inputTestId}
                        gridArea={gridArea}
                        id={inputId}
                        name={name}
                        type={inputType}
                        value={value}
                        required={required}
                        disabled={disabled}
                        feedbackType={feedbackType}
                        readOnly={readOnly}
                        onChange={onChange}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        onKeyDown={onKeyDown}
                    />
                );
            }}
        />
    );
};
