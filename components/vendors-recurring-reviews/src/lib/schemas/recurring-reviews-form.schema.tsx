import { isEmpty, isNil } from 'lodash-es';
import { z } from 'zod';
import { getVendorRenewalScheduleOptions } from '@components/vendor-questionnaires';
import {
    sharedVendorsSchedulesQuestionnairesController,
    sharedVendorsTypeformQuestionnairesController,
} from '@controllers/vendors';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { ReviewDatesInfoField } from '../components/review-dates-info-field.component';

export const buildRecurringReviewsFormSchema = action(
    (vendor?: VendorResponseDto | null): FormSchema => {
        const { initialQuestionnaires: scheduledQuestionnaires } =
            sharedVendorsSchedulesQuestionnairesController;
        const hasScheduledQuestionnaires = !isEmpty(scheduledQuestionnaires);
        const {
            formattedQuestionnaireOptions: questionnaireOptions,
            isLoading: isLoadingQuestionnaires,
            hasNextPage: hasMoreQuestionnaires,
        } = sharedVendorsTypeformQuestionnairesController;
        const vendorName = vendor?.name || 'this vendor';

        return {
            reminderToggle: {
                type: 'checkbox',
                label: t`Schedule recurring reviews`,
                helpText: t`Get reminders to conduct a security review of this vendor on a recurring basis.`,
                initialValue: vendor?.renewalScheduleType !== 'NONE',
            },
            renewalScheduleType: {
                type: 'select',
                label: t`Review frequency`,
                options: getVendorRenewalScheduleOptions().filter(
                    (option) => option.value !== 'NONE',
                ),
                initialValue: vendor?.renewalScheduleType
                    ? getVendorRenewalScheduleOptions().find(
                          (option) =>
                              option.value === vendor.renewalScheduleType,
                      )
                    : getVendorRenewalScheduleOptions()[4],
                shownIf: {
                    fieldName: 'reminderToggle',
                    operator: 'equals',
                    value: true,
                },
            },
            renewalDate: {
                type: 'date',
                label: t`Review start date`,
                initialValue: vendor?.renewalDate
                    ? (vendor.renewalDate as TDateISODate)
                    : undefined,
                shownIf: {
                    operator: 'and',
                    conditions: [
                        {
                            fieldName: 'renewalScheduleType',
                            operator: 'equals',
                            value: 'CUSTOM',
                        },
                        {
                            fieldName: 'reminderToggle',
                            operator: 'equals',
                            value: true,
                        },
                    ],
                },
            },
            reminderScheduleQuestionnaire: {
                type: 'checkbox',
                label: t`Scheduled questionnaires`,
                helpText: t`Automatically send questionnaires to ${vendorName} on the review start date.`,
                initialValue: hasScheduledQuestionnaires,
                shownIf: {
                    fieldName: 'reminderToggle',
                    operator: 'equals',
                    value: true,
                },
            },
            questionnaireIds: {
                type: 'combobox',
                label: t`Questionnaire to send`,
                isMultiSelect: true,
                validator: z.array(z.object({ value: z.string() })).min(1, {
                    message: t`Choose questionnaire to send`,
                }),
                isOptional: false,
                options: questionnaireOptions,
                initialValue: scheduledQuestionnaires,
                loaderLabel: t`Loading options...`,
                removeAllSelectedItemsLabel: t`Remove all`,
                getRemoveIndividualSelectedItemClickLabel: undefined,
                getSearchEmptyState: () => t`No options found`,
                isLoading: isLoadingQuestionnaires,
                hasMore: hasMoreQuestionnaires,
                onFetchOptions: action((params) => {
                    sharedVendorsTypeformQuestionnairesController.handleFetchOptions(
                        params,
                    );
                }),
                shownIf: {
                    operator: 'and',
                    conditions: [
                        {
                            fieldName: 'reminderToggle',
                            operator: 'equals',
                            value: true,
                        },
                        {
                            fieldName: 'reminderScheduleQuestionnaire',
                            operator: 'equals',
                            value: true,
                        },
                    ],
                },
            },
            contactsEmail: {
                type: 'text',
                label: t`Vendor contact email address`,
                helpText: t`You can add up to 5 recipients separated by comma e.g. <EMAIL>, <EMAIL>`,
                initialValue: vendor?.contactsEmail ?? '',
                shownIf: {
                    operator: 'and',
                    conditions: [
                        {
                            fieldName: 'reminderToggle',
                            operator: 'equals',
                            value: true,
                        },
                        {
                            fieldName: 'reminderScheduleQuestionnaire',
                            operator: 'equals',
                            value: true,
                        },
                    ],
                },
                validator: z
                    .string({ message: t`At least one email is required` })
                    .min(1, { message: t`At least one email is required` })
                    .refine(
                        (val) => {
                            const emails = val
                                .split(',')
                                .map((email) => email.trim());

                            if (emails.length > 5) {
                                return false;
                            }

                            return emails.every(
                                (email) =>
                                    z.string().email().safeParse(email).success,
                            );
                        },
                        {
                            message: t`Please enter up to 5 valid email addresses separated by commas`,
                        },
                    ),
            },
            reviewDatesInfo: {
                type: 'custom',
                label: t`Review dates`,
                render: (props) =>
                    isNil(vendor) ? null : (
                        <ReviewDatesInfoField
                            {...props}
                            vendor={vendor}
                            data-id="vendor-review-dates-info"
                        />
                    ),
                shownIf: {
                    fieldName: 'reminderToggle',
                    operator: 'equals',
                    value: true,
                },
            },
        };
    },
);
