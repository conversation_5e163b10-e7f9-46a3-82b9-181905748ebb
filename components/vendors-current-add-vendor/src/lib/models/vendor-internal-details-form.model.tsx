import { isNil } from 'lodash-es';
import { z } from 'zod';
import {
    sharedUsersInfiniteController,
    sharedVendorContactsInfiniteController,
} from '@controllers/users';
import {
    sharedVendorCustomFieldsController,
    sharedVendorsCreateCurrentVendorController,
    sharedVendorsDetailsController,
    sharedVendorsIntegrationsInfiniteController,
} from '@controllers/vendors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { formatCurrencyValue, getFullName } from '@helpers/formatters';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { getRiskLabel } from '@models/vendors-profile';
import type { FormSchema } from '@ui/forms';
import { RiskSelectField } from '../../components/risk-select-field.component';
import {
    BUSINESS_UNIT_VALUES,
    getBusinessUnitLabel,
} from '../../constants/business-unit.constants';
import { RISK_VALUES } from '../../constants/risk.constants';
import { SECURITY_OWNER_ROLES } from '../../constants/security-owner-roles.constants';
import { VENDOR_CONTACT_ROLES } from '../../constants/vendor-contact-roles.constants';
import { getVendorStatusOptions } from '../../constants/vendor-status.constants';
import {
    getDropdownActionLabelVendorType,
    VENDOR_TYPE_VALUES,
} from '../../constants/vendor-type.constants';
import {
    findInOptions,
    toFieldOptionsWithLabelFunction,
} from '../../helpers/field-option.helpers';
import { getRecommendedImpactLevelOptions } from '../../helpers/recommended-impact-level.helper';
import { integrationDtoToFormAdapter } from '../../helpers/vendor-add-vendor.helper';
import type { VendorInternalDetailsFormValuesType } from '../../types/vendor-internal-details-schema.type';
import { overlayCustomFieldsInitialValues } from '../helpers/custom-fields-schema.helper';

class VendorInternalDetailsFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    getCustomFieldsSchema(isAddingVendor: boolean): FormSchema {
        const {
            vendorInternalDetailsCustomFieldsByVendorId,
            vendorInternalDetailsCustomFieldsList,
        } = sharedVendorCustomFieldsController;

        if (sharedFeatureAccessModel.isCustomFieldsEnabled) {
            const customFieldsSchema =
                sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
                    isAddingVendor
                        ? vendorInternalDetailsCustomFieldsList
                        : vendorInternalDetailsCustomFieldsByVendorId,
                );

            const storedValues = isNil(
                sharedVendorsDetailsController.vendorDetailsTemporalValues,
            )
                ? sharedVendorsCreateCurrentVendorController.vendorInternalDetails
                : sharedVendorsDetailsController.vendorDetailsTemporalValues;

            overlayCustomFieldsInitialValues(customFieldsSchema, storedValues);

            return customFieldsSchema;
        }

        return {};
    }

    get integrationsController() {
        return sharedVendorsIntegrationsInfiniteController;
    }

    get usersController() {
        return sharedUsersInfiniteController;
    }

    get contactsController() {
        return sharedVendorContactsInfiniteController;
    }

    get storedValues() {
        return isNil(sharedVendorsDetailsController.vendorDetails)
            ? sharedVendorsCreateCurrentVendorController.vendorInternalDetails
            : sharedVendorsDetailsController.vendorDetails;
    }

    get selectedUser() {
        const { options: userOptions } = this.usersController;
        const storedUserId = this.storedValues?.user?.id;

        if (!storedUserId) {
            return undefined;
        }

        const userFound = userOptions.find(
            (user) => user.id === String(storedUserId),
        );

        if (userFound) {
            return userFound;
        }

        // If user is not in options yet, create the item from stored data
        const storedUser = this.storedValues.user;

        if (storedUser) {
            return {
                id: String(storedUser.id),
                label: getFullName(storedUser.firstName, storedUser.lastName),
                value: String(storedUser.id),
                description: storedUser.email,
                userData: storedUser,
            };
        }

        return undefined;
    }

    get selectedContact() {
        const { options: contactOptions } = this.contactsController;
        const storedContactId = this.storedValues?.contact?.id;

        if (!storedContactId) {
            return undefined;
        }

        const contactFound = contactOptions.find(
            (contact) => contact.id === String(storedContactId),
        );

        if (contactFound) {
            return contactFound;
        }

        // If contact is not in options yet, create the item from stored data
        const storedContact = this.storedValues.contact;

        if (storedContact) {
            return {
                id: String(storedContact.id),
                label: getFullName(
                    storedContact.firstName,
                    storedContact.lastName,
                ),
                value: String(storedContact.id),
                description: storedContact.email,
            };
        }

        return undefined;
    }

    getSchema(isAddingVendor = false): FormSchema {
        const {
            options: integrationsOptions,
            hasNextPage: hasMoreIntegrations,
            isFetching: isFetchingIntegrations,
            isLoading: isLoadingIntegrations,
            onFetchIntegrations,
        } = this.integrationsController;

        const {
            options: userOptions,
            hasNextPage: hasMoreUsers,
            isFetching: isFetchingUsers,
            isLoading: isLoadingUsers,
            onFetchUsers: onFetchUsers,
        } = this.usersController;

        const {
            options: contactOptions,
            hasNextPage: hasMoreContacts,
            isFetching: isFetchingContacts,
            isLoading: isLoadingContacts,
            onFetchUsers: onFetchContacts,
        } = this.contactsController;

        const shouldShowImpactLevel =
            !sharedFeatureAccessModel.isVendorRiskManagementProEnabled;
        const storedValues = isNil(
            sharedVendorsDetailsController.vendorDetailsTemporalValues,
        )
            ? sharedVendorsCreateCurrentVendorController.vendorInternalDetails
            : sharedVendorsDetailsController.vendorDetailsTemporalValues;

        const status = storedValues?.status as ListBoxItemData | undefined;
        const type = storedValues?.type as ListBoxItemData | undefined;
        const category = storedValues?.category as ListBoxItemData | undefined;
        const risk = storedValues?.risk as ListBoxItemData | undefined;
        const impactLevel = storedValues?.impactLevel as
            | ListBoxItemData
            | undefined;

        const vendorDetails: VendorInternalDetailsFormValuesType = {
            ...storedValues,
            status: status?.value ?? status,
            type: type?.value ?? type,
            category: category?.value ?? category,
            risk: risk?.value ?? risk,
            impactLevel: impactLevel?.value ?? impactLevel,
        };
        const vendorStatusOptions = getVendorStatusOptions();

        const baseSchema: FormSchema = {
            status: {
                type: 'select',
                options: vendorStatusOptions,
                initialValue: vendorDetails.status
                    ? findInOptions(
                          vendorDetails.status as string,
                          vendorStatusOptions,
                      )
                    : undefined,
                label: t`Status`,
                isOptional: true,
                loaderLabel: t`Loading vendor statuses`,
            },
            type: {
                type: 'select',
                options: toFieldOptionsWithLabelFunction(
                    VENDOR_TYPE_VALUES,
                    getDropdownActionLabelVendorType,
                ),
                initialValue: vendorDetails.type
                    ? findInOptions(
                          vendorDetails.type as string,
                          toFieldOptionsWithLabelFunction(
                              VENDOR_TYPE_VALUES,
                              getDropdownActionLabelVendorType,
                          ),
                      )
                    : undefined,
                label: t`Type`,
                isOptional: true,
                loaderLabel: t`Loading vendor types`,
            },
            category: {
                type: 'select',
                options: toFieldOptionsWithLabelFunction(
                    BUSINESS_UNIT_VALUES,
                    getBusinessUnitLabel,
                ),
                initialValue: vendorDetails.category
                    ? findInOptions(
                          vendorDetails.category as string,
                          toFieldOptionsWithLabelFunction(
                              BUSINESS_UNIT_VALUES,
                              getBusinessUnitLabel,
                          ),
                      )
                    : findInOptions(
                          'NONE',
                          toFieldOptionsWithLabelFunction(
                              BUSINESS_UNIT_VALUES,
                              getBusinessUnitLabel,
                          ),
                      ),
                label: t`Business unit`,
                isOptional: true,
                loaderLabel: t`Loading business units`,
            },
            risk: {
                type: 'custom',
                render: RiskSelectField,
                validateWithDefault: 'select',
                options: toFieldOptionsWithLabelFunction(
                    RISK_VALUES,
                    getRiskLabel,
                ),
                initialValue: vendorDetails.risk
                    ? (findInOptions(
                          vendorDetails.risk as string,
                          toFieldOptionsWithLabelFunction(
                              RISK_VALUES,
                              getRiskLabel,
                          ),
                      ) ?? undefined)
                    : undefined,
                label: t`Risk`,
                isOptional: true,
                loaderLabel: t`Loading risk levels`,
            },
            ...(shouldShowImpactLevel && {
                impactLevel: {
                    type: 'select',
                    options: getRecommendedImpactLevelOptions(),
                    initialValue: vendorDetails.impactLevel
                        ? (getRecommendedImpactLevelOptions().find(
                              (option) =>
                                  option.value === vendorDetails.impactLevel,
                          ) ?? undefined)
                        : undefined,
                    label: t`Impact level`,
                    isOptional: true,
                    loaderLabel: t`Loading impact levels`,
                },
            }),
            dataStored: {
                type: 'textarea',
                initialValue: vendorDetails.dataStored ?? undefined,
                label: t`Stored data`,
                isOptional: true,
            },
            hasPii: {
                type: 'checkbox',
                initialValue: vendorDetails.hasPii ?? false,
                label: t`This vendor stores personally identifiable information (PII)`,
                isOptional: true,
            },
            isSubProcessor: {
                type: 'checkbox',
                initialValue: vendorDetails.isSubProcessor ?? false,
                label: t`This vendor is our sub-processor`,
                isOptional: true,
            },
            location: {
                type: 'text',
                initialValue: vendorDetails.location ?? undefined,
                label: t`Data location`,
                shownIf: {
                    fieldName: 'isSubProcessor',
                    operator: 'equals',
                    value: true,
                },
            },
            integrations: {
                type: 'combobox',
                isMultiSelect: true,
                options: integrationsOptions,
                initialValue: integrationDtoToFormAdapter(
                    vendorDetails.integrations,
                ),
                label: t`Integrations`,
                placeholder: t`Search integrations`,
                isOptional: true,
                getSearchEmptyState: () => {
                    return t`No integrations found`;
                },
                removeAllSelectedItemsLabel: t`Remove all integrations`,
                loaderLabel: t`Loading integrations`,
                getRemoveIndividualSelectedItemClickLabel: ({ itemLabel }) =>
                    `Remove ${itemLabel}`,
                isLoading: isFetchingIntegrations && isLoadingIntegrations,
                hasMore: hasMoreIntegrations,
                onFetchOptions: onFetchIntegrations,
            },
            user: {
                type: 'combobox',
                readOnly: isFetchingUsers && isLoadingUsers,
                options: userOptions,
                initialValue: this.selectedUser,
                label: t`Security owner`,
                isOptional: true,
                loaderLabel: t`Loading security owners`,
                placeholder: t`Search by name`,
                getSearchEmptyState: () => t`No security owners found`,
                clearSelectedItemButtonLabel: t`Clear security owner`,
                isLoading: isFetchingUsers && isLoadingUsers,
                hasMore: hasMoreUsers,
                onFetchOptions: ({ search, increasePage }) => {
                    onFetchUsers({
                        search,
                        increasePage,
                        excludeReadOnlyUsers: true,
                        roles: SECURITY_OWNER_ROLES,
                    });
                },
                helpText: t`A security owner is responsible for reviewing this vendor’s security posture.`,
            },
            contact: {
                type: 'combobox',
                options: contactOptions,
                initialValue: this.selectedContact,
                label: t`Vendor relationship contact`,
                isOptional: true,
                loaderLabel: t`Loading vendor contacts`,
                placeholder: t`Search by name`,
                getSearchEmptyState: () => t`No vendor contacts found`,
                clearSelectedItemButtonLabel: t`Clear contact`,
                isLoading: isFetchingContacts && isLoadingContacts,
                hasMore: hasMoreContacts,
                onFetchOptions: ({ search, increasePage }) => {
                    onFetchContacts({
                        search,
                        increasePage,
                        roles: VENDOR_CONTACT_ROLES,
                    });
                },
                helpText: t`This is the contact to reach out to if you have questions about this vendor.`,
            },
            cost: {
                type: 'text',
                initialValue: vendorDetails.cost
                    ? formatCurrencyValue(vendorDetails.cost)
                    : undefined,
                label: t`Annual contract value`,
                isOptional: true,
                validator: z
                    .string()
                    .optional()
                    .refine(
                        (value) => {
                            // Allow empty/undefined values for optional fields
                            if (!value || value.trim() === '') {
                                return true;
                            }

                            // Validate format: numbers with up to 2 decimal places
                            return /^\d+(?:\.\d{1,2})?$/.test(value);
                        },
                        {
                            message:
                                'Only numbers with up to 2 decimal places are allowed',
                        },
                    )
                    .refine(
                        (value) => {
                            // Allow empty/undefined values for optional fields
                            if (!value || value.trim() === '') {
                                return true;
                            }

                            // Validate value is zero or positive
                            return Number(value) >= 0;
                        },
                        {
                            message: 'Value must be zero or positive',
                        },
                    ),
            },
            notes: {
                type: 'textarea',
                initialValue: vendorDetails.notes ?? undefined,
                label: t`Additional notes`,
                isOptional: true,
            },
        };

        const customFieldsSchema = this.getCustomFieldsSchema(isAddingVendor);

        return {
            ...baseSchema,
            ...customFieldsSchema,
        };
    }
}

export const sharedVendorInternalDetailsFormModel =
    new VendorInternalDetailsFormModel();
