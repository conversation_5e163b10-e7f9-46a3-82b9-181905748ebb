import { z } from 'zod';
import { getVendorQuestionnaireCategoryOptions } from '@components/vendor-questionnaires';
import { sharedUsersInfiniteController } from '@controllers/users';
import {
    MIN_SEARCH_CHARACTERS,
    sharedVendorsDiscoveryController,
} from '@controllers/vendors';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { validateUrlWithTLDCheck } from '@globals/zod';
import type { FormSchema, FormValues } from '@ui/forms';
import { VendorNameAutocompleteWithFormAccess } from '../components/vendor-name-autocomplete-with-form-access.component';

export class VendorsProspectiveAddFormDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    getSchema(state?: FormValues | null): FormSchema {
        const {
            vendorsDiscovery,
            isLoading: isDiscoveryLoading,
            hasError: hasDiscoveryError,
            error: discoveryError,
            loadVendorsDiscovery,
            itemToString,
            transformSelectedItem,
        } = sharedVendorsDiscoveryController;

        const {
            options: usersOptions,
            hasNextPage: usersHasNextPage,
            isFetching: usersIsFetching,
            isLoading: usersIsLoading,
            onFetchUsers,
        } = sharedUsersInfiniteController;

        const vendorsDiscoveryOptions = Array.isArray(vendorsDiscovery)
            ? vendorsDiscovery
            : [];

        return {
            name: {
                type: 'custom',
                label: t`Vendor name`,
                render: (props) => {
                    return VendorNameAutocompleteWithFormAccess({
                        ...props,
                        loaderLabel: t`Loading vendors...`,
                        placeholderText: t`Search by vendor name`,
                        isLoading: isDiscoveryLoading,
                        getSearchEmptyState: ({
                            inputValue,
                        }: {
                            inputValue: string;
                        }) => {
                            if (hasDiscoveryError) {
                                return (
                                    discoveryError?.message ||
                                    t`Error loading vendors`
                                );
                            }

                            return inputValue.length < MIN_SEARCH_CHARACTERS
                                ? t`Start typing a vendor name...`
                                : t`No vendors found`;
                        },
                        options: vendorsDiscoveryOptions,
                        hasMore: false,
                        onFetchOptions: loadVendorsDiscovery,
                        clearSelectedItemButtonLabel: t`Clear`,
                        itemToString,
                        transformSelectedItem,
                    });
                },
                isOptional: false,
                initialValue: undefined,
                validator: z.object(
                    {
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                        description: z.string().optional(),
                    },
                    {
                        message: t`Choose a vendor`,
                    },
                ),
            },
            url: {
                type: 'text',
                initialValue: (state?.url ?? '') as string,
                label: t`Vendor website URL`,
                isOptional: true,
                validator: validateUrlWithTLDCheck(
                    t`Website URL must be a valid URL`,
                ),
            },
            category: {
                type: 'select',
                initialValue:
                    getVendorQuestionnaireCategoryOptions().find(
                        (option) => option.value === (state?.category ?? ''),
                    ) ?? undefined,
                label: t`Business unit`,
                options: getVendorQuestionnaireCategoryOptions(),
            },
            servicesProvided: {
                type: 'textarea',
                initialValue: (state?.servicesProvided ?? '') as string,
                label: t`Provided services`,
                isOptional: true,
            },
            contactAtVendor: {
                type: 'text',
                initialValue: (state?.contactAtVendor ?? '') as string,
                label: t`Contact name`,
                isOptional: true,
            },
            contactsEmail: {
                type: 'text',
                initialValue: (state?.contactsEmail ?? '') as string,
                label: t`Contact email`,
                isOptional: true,
                validator: z.string().email().or(z.literal('')),
            },
            requester: {
                type: 'combobox',
                initialValue: (state?.requester ?? '') as ListBoxItemData,
                label: t`Requester`,
                loaderLabel: t`Loading employees...`,
                placeholder: t`Search by name`,
                isLoading: usersIsFetching && usersIsLoading,
                getSearchEmptyState: () => t`No employees found`,
                options: usersOptions,
                hasMore: usersHasNextPage,
                onFetchOptions: onFetchUsers,
                isOptional: false,
                validator: z.object(
                    {
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    },
                    {
                        message: t`Choose a requester`,
                    },
                ),
            },
            internalSecurityOwner: {
                type: 'combobox',
                initialValue: (state?.internalSecurityOwner ??
                    '') as ListBoxItemData,
                loaderLabel: t`Loading employees...`,
                placeholder: t`Search by name`,
                label: t`Internal Security Owner`,
                isOptional: true,
                isLoading: usersIsFetching && usersIsLoading,
                getSearchEmptyState: () => t`No employees found`,
                options: usersOptions,
                hasMore: usersHasNextPage,
                onFetchOptions: onFetchUsers,
            },
            requestDate: {
                type: 'date',
                initialValue: (state?.requestDate ??
                    new Date().toISOString().split('T')[0]) as TDateISODate,
                label: t`Request date`,
                isOptional: false,
                getIsDateUnavailable: (date) => {
                    return (
                        new Date(date).toISOString().split('T')[0] <
                        new Date().toISOString().split('T')[0]
                    );
                },
                validator: z
                    .string({
                        message: t`Choose a request date`,
                    })
                    .date(),
            },
            renewalDate: {
                type: 'date',
                initialValue: (state?.renewalDate ?? '') as TDateISODate,
                label: t`Review deadline`,
                isOptional: false,
                getIsDateUnavailable: (date) => {
                    return (
                        new Date(date).toISOString().split('T')[0] <
                        new Date().toISOString().split('T')[0]
                    );
                },
                validator: z
                    .string({
                        message: t`Choose a review deadline`,
                    })
                    .date(),
            },
        };
    }
}

export const sharedVendorsProspectiveAddFormDetailsModel =
    new VendorsProspectiveAddFormDetailsModel();
