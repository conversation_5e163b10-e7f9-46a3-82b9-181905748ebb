import { getVendorCategoryLabelsRecord } from '@components/vendor-questionnaires';
import { Metadata } from '@cosmos/components/metadata';
import type {
    BusinessUnitTypeOption,
    QuestionnaireUploadOption,
    QuestionnaireUploadStatusType,
    QuestionnaireUploadType,
} from '../types/vendors-prospective-add.type';

export const VENDORS_PROSPECTIVE_ADD_FORM_ID = 'vendor-prospective-add-form';

export const MAX_CHARACTERS = 30000;

export const getVendorBusinessUnitOptions = (): BusinessUnitTypeOption[] =>
    Object.entries(getVendorCategoryLabelsRecord()).map(
        ([value, label], index) => {
            return {
                id: `${value}-${index}`,
                label,
                value,
            };
        },
    );

export const QUESTIONNAIRE_UPLOAD_LABELS: Record<
    QuestionnaireUploadType,
    string
> = {
    SEND_VIA_DRATA: 'Send view Drata',
    UPLOAD_RESPONSE_FILE: 'Upload response file',
};

export const QUESTIONNAIRE_UPLOAD_OPTIONS: QuestionnaireUploadOption[] =
    Object.entries(QUESTIONNAIRE_UPLOAD_LABELS).map(([value, label], index) => {
        return {
            id: `${value}-${index}`,
            label,
            value,
        };
    });

export const QUESTIONNAIRE_UPLOAD_STATUS_METADATA: Record<
    QuestionnaireUploadStatusType,
    React.JSX.Element
> = {
    WAITING_FOR_RESPONSE: (
        <Metadata
            label={'Waiting for response'}
            type="status"
            colorScheme="warning"
        />
    ),
    COMPLETED: (
        <Metadata label={'Completed'} type="status" colorScheme="success" />
    ),
    UPLOADED: (
        <Metadata label={'Uploaded'} type="status" colorScheme="neutral" />
    ),
};
