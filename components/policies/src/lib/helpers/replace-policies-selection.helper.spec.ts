import { beforeEach, describe, expect, test, vi } from 'vitest';
import type { ObjectItem } from '@components/object-selector';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import type { ActivePoliciesDataWithSlaResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    applyGracePeriodSLAs,
    applyP3MatrixSLAs,
    applyWeekTimeFrameSLAs,
    collectGracePeriodSLAs,
    collectP3MatrixSLAs,
    collectWeekTimeFrameSLAs,
    getReplacePoliciesButtonLabel,
    getReplacePoliciesSelectedText,
    handleReplacePoliciesSelection,
    mapToPoliciesReplacementData,
    type PoliciesReplacementItem,
} from './replace-policies-selection.helper';

vi.mock('@controllers/policy-builder', () => ({
    sharedPolicyBuilderController: {
        resetAddingVersionSLAs: vi.fn(),
        setIsAddingVersionGracePeriodSLAs: vi.fn(),
        setIsAddingVersionP3MatrixSLAs: vi.fn(),
        setIsAddingVersionWeekTimeFrameSLAs: vi.fn(),
    },
}));

vi.mock('@models/policies', () => ({
    gracePeriodSLAOptions: () => [
        { id: '1', label: '1 Day', value: 'ONE_DAY' },
        { id: '7', label: '7 Days', value: 'SEVEN_DAYS' },
    ],
    weekTimeFrameSLAOptions: () => [
        { id: '1', label: '1 Day', value: 'ONE_DAY' },
        { id: '3', label: '3 Days', value: 'THREE_DAYS' },
    ],
    p3MatrixSLAOptions: () => [
        { id: 'p1', label: '1 Day', value: 'ONE_DAY' },
        { id: 'p3', label: '3 Days', value: 'THREE_DAYS' },
        { id: 'p7', label: '7 Days', value: 'SEVEN_DAYS' },
        { id: 'p30', label: '30 Days', value: 'THIRTY_DAYS' },
    ],
}));

describe('replace-policies-selection.helper', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    test('getReplacePoliciesButtonLabel returns correct labels', () => {
        expect(getReplacePoliciesButtonLabel(true)).toBe(
            t`Edit selected policies`,
        );
        expect(getReplacePoliciesButtonLabel(false)).toBe(
            t`Select policies to replace`,
        );
    });

    test('getReplacePoliciesSelectedText returns singular/plural correctly', () => {
        expect(getReplacePoliciesSelectedText(1)).toBe(t`1 policy selected`);
        const selectedText = t`policies selected`;

        expect(getReplacePoliciesSelectedText(3)).toBe(`3 ${selectedText}`);
    });

    test('mapToPoliciesReplacementData maps selected items', () => {
        const items: ObjectItem<ActivePoliciesDataWithSlaResponseDto>[] = [
            {
                id: '1',
                value: '1',
                label: 'P1',
                description: '',
                objectType: 'POLICY',
                objectData: {
                    id: 1,
                } as unknown as ActivePoliciesDataWithSlaResponseDto,
                avatar: { fallbackText: 'P', imgAlt: 'Policy' },
            },
            {
                id: '2',
                value: '2',
                label: 'P2',
                description: '',
                objectType: 'POLICY',
                objectData: {
                    id: 2,
                    policyGracePeriodSLAs: [{ id: 1, label: 'L1' }],
                } as unknown as ActivePoliciesDataWithSlaResponseDto,
                avatar: { fallbackText: 'P', imgAlt: 'Policy' },
            },
        ];

        const mapped = mapToPoliciesReplacementData(items);

        expect(mapped).toHaveLength(2);
        expect(mapped[0]).toMatchObject({ id: 1, name: 'P1', hasSLA: false });
        expect(mapped[1].hasSLA).toBeTruthy();
    });

    test('collect SLA helpers gather correct arrays', () => {
        const mapped = [
            {
                id: 1,
                name: 'A',
                hasSLA: true,
                SLAs: { policyGracePeriodSLAs: [{ id: 1, label: 'L1' }] },
            },
            {
                id: 2,
                name: 'B',
                hasSLA: true,
                SLAs: { policyWeekTimeFrameSLAs: [{ id: 2, label: 'L2' }] },
            },
            {
                id: 3,
                name: 'C',
                hasSLA: true,
                SLAs: { policyP3MatrixSLAs: [{}] },
            },
        ] as PoliciesReplacementItem[];

        expect(collectGracePeriodSLAs(mapped)).toStrictEqual([
            { id: 1, label: 'L1' },
        ]);
        expect(collectWeekTimeFrameSLAs(mapped)).toStrictEqual([
            { id: 2, label: 'L2' },
        ]);
        expect(collectP3MatrixSLAs(mapped)).toStrictEqual([{}]);
    });

    test('applyGracePeriodSLAs applies values and toggles controller flag', () => {
        const formSetValue = vi.fn();

        applyGracePeriodSLAs(formSetValue, [{ id: 1, label: 'L1' }]);
        expect(formSetValue).toHaveBeenCalledWith(
            'slaConfiguration.gracePeriodSLAs',
            expect.any(Array),
            expect.objectContaining({
                shouldDirty: true,
                shouldTouch: true,
                shouldValidate: true,
            }),
        );
        expect(
            sharedPolicyBuilderController.setIsAddingVersionGracePeriodSLAs,
        ).toHaveBeenCalledWith(true);
    });

    test('applyWeekTimeFrameSLAs applies values and toggles controller flag', () => {
        const formSetValue = vi.fn();

        applyWeekTimeFrameSLAs(formSetValue, [{ id: 2, label: 'L2' }]);
        expect(formSetValue).toHaveBeenCalledWith(
            'slaConfiguration.weekTimeFrameSLAs',
            expect.any(Array),
            expect.objectContaining({
                shouldDirty: true,
                shouldTouch: true,
                shouldValidate: true,
            }),
        );
        expect(
            sharedPolicyBuilderController.setIsAddingVersionWeekTimeFrameSLAs,
        ).toHaveBeenCalledWith(true);
    });

    test('applyP3MatrixSLAs applies values and toggles controller flag', () => {
        const formSetValue = vi.fn();

        applyP3MatrixSLAs(formSetValue, [{}]);
        expect(formSetValue).toHaveBeenCalledWith(
            'slaConfiguration.p3MatrixSLAs',
            expect.any(Array),
            expect.objectContaining({
                shouldDirty: true,
                shouldTouch: true,
                shouldValidate: true,
            }),
        );
        expect(
            sharedPolicyBuilderController.setIsAddingVersionP3MatrixSLAs,
        ).toHaveBeenCalledWith(true);
    });

    test('handleReplacePoliciesSelection sets values without SLAs', () => {
        const selectedItems: ObjectItem<ActivePoliciesDataWithSlaResponseDto>[] =
            [
                {
                    id: '10',
                    value: '10',
                    label: 'Policy A',
                    description: '',
                    objectType: 'POLICY',
                    objectData: {
                        id: 10,
                    } as unknown as ActivePoliciesDataWithSlaResponseDto,
                    avatar: { fallbackText: 'P', imgAlt: 'Policy' },
                },
            ];

        const formSetValue = vi.fn();
        const setValue = vi.fn();

        handleReplacePoliciesSelection(selectedItems, formSetValue, setValue);

        expect(setValue).toHaveBeenCalledWith([{ id: 10, name: 'Policy A' }]);
        expect(formSetValue).not.toHaveBeenCalled();
    });

    test('handleReplacePoliciesSelection sets SLA fields when present', () => {
        const selectedItems: ObjectItem<ActivePoliciesDataWithSlaResponseDto>[] =
            [
                {
                    id: '11',
                    value: '11',
                    label: 'Policy B',
                    description: '',
                    objectType: 'POLICY',
                    objectData: {
                        id: 11,
                        policyGracePeriodSLAs: [{ id: 1, label: 'Label A' }],
                        policyWeekTimeFrameSLAs: [{ id: 2, label: 'Label B' }],
                        policyP3MatrixSLAs: [{}],
                    } as unknown as ActivePoliciesDataWithSlaResponseDto,
                    avatar: { fallbackText: 'P', imgAlt: 'Policy' },
                },
            ];

        const formSetValue = vi.fn();
        const setValue = vi.fn();

        handleReplacePoliciesSelection(selectedItems, formSetValue, setValue);

        const calls = (
            formSetValue.mock.calls as [string, unknown?, unknown?][]
        ).map((c) => c[0]);

        expect(calls).toContain('slaConfiguration.gracePeriodSLAs');
        expect(calls).toContain('slaConfiguration.weekTimeFrameSLAs');
        expect(calls).toContain('slaConfiguration.p3MatrixSLAs');
        expect(setValue).toHaveBeenCalledWith([{ id: 11, name: 'Policy B' }]);
    });
});
