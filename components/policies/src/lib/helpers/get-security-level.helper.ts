import type { P3MatrixSlaResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getSecurityLevel = (
    severity: P3MatrixSlaResponseDto['severity'],
): string => {
    switch (severity) {
        case 'LOW': {
            return t`Low severity level`;
        }
        case 'MEDIUM': {
            return t`Medium severity level`;
        }
        case 'HIGH': {
            return t`High severity`;
        }
        case 'CRITICAL': {
            return t`Critical severity level`;
        }
        default: {
            return t`Unknown`;
        }
    }
};
