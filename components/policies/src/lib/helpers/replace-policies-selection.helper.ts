import { isEmpty } from 'lodash-es';
import type { ObjectItem } from '@components/object-selector';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import type {
    ActivePoliciesDataWithSlaResponseDto,
    PolicyGracePeriodSlaResponseDto,
    PolicyP3MatrixSlaResponseDto,
    PolicyWeekTimeFrameSlaResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { runInAction, toJS } from '@globals/mobx';
import {
    gracePeriodSLAOptions,
    type P3MatrixSLA,
    p3MatrixSLAOptions,
    weekTimeFrameSLAOptions,
} from '@models/policies';

export interface PoliciesReplacementItem {
    id: number;
    name: string;
    hasSLA: boolean;
    SLAs: {
        policyWeekTimeFrameSLAs?: PolicyWeekTimeFrameSlaResponseDto[] | null;
        policyGracePeriodSLAs?: PolicyGracePeriodSlaResponseDto[] | null;
        policyP3MatrixSLAs?: PolicyP3MatrixSlaResponseDto[] | null;
    };
}

export const getP3MatrixSLAValues: () => {
    severity: P3MatrixSLA['severity'];
    definition: string;
    examples: string;
    timeFrame: string;
}[] = () => [
    {
        severity: 'LOW',
        definition: t`Issues that affect singular users and require interaction or significant prerequisites (MitM) to trigger.`,
        examples: t`Common flaws, Debug information, Mixed Content`,
        timeFrame: 'THIRTY_DAYS',
    },
    {
        severity: 'MEDIUM',
        definition: t`Vulnerabilities that affect multiple users, and require little or no user interaction to trigger`,
        examples: t`Reflective XSS, Direct object reference, URL Redirect, some CSRF depending on impact`,
        timeFrame: 'SEVEN_DAYS',
    },
    {
        severity: 'HIGH',
        definition: t`Vulnerabilities that affect the security of the platform including the processes it supports.`,
        examples: t`Lateral authentication bypass, Stored XSS, some CSRF depending on impact`,
        timeFrame: 'THREE_DAYS',
    },
    {
        severity: 'CRITICAL',
        definition: t`Vulnerabilities that cause a privilege escalation on the platform from unprivileged to admin, allows remote code execution, financial theft, unauthorized access to/extraction of sensitive data, etc.`,
        examples: t`Vulnerabilities that result in Remote Code Execution such as Vertical Authentication bypass, SSRF, XXE, SQL Injection, User authentication bypass`,
        timeFrame: 'ONE_DAY',
    },
];

export const getReplacePoliciesButtonLabel = (
    hasSelectedPolicies: boolean,
): string => {
    if (hasSelectedPolicies) {
        return t`Edit selected policies`;
    }

    return t`Select policies to replace`;
};

export const getReplacePoliciesSelectedText = (
    selectedPoliciesCount: number,
): string => {
    if (selectedPoliciesCount === 1) {
        return t`1 policy selected`;
    }
    const policiesText = t`policies selected`;

    return `${selectedPoliciesCount} ${policiesText}`;
};

export type SelectedItem = ObjectItem<ActivePoliciesDataWithSlaResponseDto>;

export const mapToPoliciesReplacementData = (
    items: SelectedItem[],
): PoliciesReplacementItem[] =>
    items.map((item) => ({
        id: item.objectData.id,
        name: item.label,
        hasSLA: Boolean(
            item.objectData.policyWeekTimeFrameSLAs?.length ||
                item.objectData.policyGracePeriodSLAs?.length ||
                item.objectData.policyP3MatrixSLAs?.length,
        ),
        SLAs: {
            policyWeekTimeFrameSLAs: toJS(
                item.objectData.policyWeekTimeFrameSLAs,
            ),
            policyGracePeriodSLAs: toJS(item.objectData.policyGracePeriodSLAs),
            policyP3MatrixSLAs:
                toJS(item.objectData.policyP3MatrixSLAs) ?? null,
        },
    }));

export const collectGracePeriodSLAs = (
    policies: PoliciesReplacementItem[],
): { id?: number | string; label?: string }[] =>
    policies
        .filter((p) => p.hasSLA && Array.isArray(p.SLAs.policyGracePeriodSLAs))
        .flatMap((p) => p.SLAs.policyGracePeriodSLAs ?? [])
        .filter(Boolean);

export const collectWeekTimeFrameSLAs = (
    policies: PoliciesReplacementItem[],
): { id?: number | string; label?: string }[] =>
    policies
        .filter(
            (p) => p.hasSLA && Array.isArray(p.SLAs.policyWeekTimeFrameSLAs),
        )
        .flatMap((p) => p.SLAs.policyWeekTimeFrameSLAs ?? [])
        .filter(Boolean);

export const collectP3MatrixSLAs = (
    policies: PoliciesReplacementItem[],
): PolicyP3MatrixSlaResponseDto[] =>
    policies
        .filter((p) => p.hasSLA && Array.isArray(p.SLAs.policyP3MatrixSLAs))
        .flatMap((p) => p.SLAs.policyP3MatrixSLAs ?? [])
        .filter(Boolean);

export const applyGracePeriodSLAs = (
    formSetValue: (
        name: string,
        value: unknown,
        options?: {
            shouldDirty?: boolean;
            shouldTouch?: boolean;
            shouldValidate?: boolean;
        },
    ) => void,
    policyGracePeriodSLAs: { id?: number | string; label?: string }[],
): void => {
    if (isEmpty(policyGracePeriodSLAs)) {
        runInAction(() => {
            sharedPolicyBuilderController.setIsAddingVersionGracePeriodSLAs(
                false,
            );
        });

        return;
    }

    const fieldValues = policyGracePeriodSLAs.map((sla) => {
        const foundGracePeriodSLAOption = gracePeriodSLAOptions().find(
            (gracePeriodSLAOption) => gracePeriodSLAOption.value === 'ONE_DAY',
        );

        if (!foundGracePeriodSLAOption) {
            throw new Error('GracePeriodSLA Option not found');
        }
        const { value, label, id } = foundGracePeriodSLAOption;

        return {
            period: {
                id,
                label,
                value,
                meta: { label: sla.label },
            },
        };
    });

    formSetValue('slaConfiguration.gracePeriodSLAs', fieldValues as never, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
    });
    runInAction(() => {
        sharedPolicyBuilderController.setIsAddingVersionGracePeriodSLAs(true);
    });
};

export const applyWeekTimeFrameSLAs = (
    formSetValue: (
        name: string,
        value: unknown,
        options?: {
            shouldDirty?: boolean;
            shouldTouch?: boolean;
            shouldValidate?: boolean;
        },
    ) => void,
    policyWeekTimeFrameSLAs: { id?: number | string; label?: string }[],
): void => {
    if (isEmpty(policyWeekTimeFrameSLAs)) {
        runInAction(() => {
            sharedPolicyBuilderController.setIsAddingVersionWeekTimeFrameSLAs(
                false,
            );
        });

        return;
    }

    const fieldValues = policyWeekTimeFrameSLAs.map((sla, index) => {
        const foundOption = weekTimeFrameSLAOptions().find(
            (opt) => opt.value === 'ONE_DAY',
        );

        if (!foundOption) {
            throw new Error('WeekTimeFrameSLA Option not found');
        }
        const { value, label } = foundOption;

        return {
            timeFrame: {
                id: `${(sla as { id?: number | string }).id}`,
                label,
                value,
                meta: {
                    labels: {
                        [index]: (sla as { label?: string }).label as string,
                    },
                },
            },
        };
    });

    formSetValue('slaConfiguration.weekTimeFrameSLAs', fieldValues as never, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
    });
    runInAction(() => {
        sharedPolicyBuilderController.setIsAddingVersionWeekTimeFrameSLAs(true);
    });
};

export const applyP3MatrixSLAs = (
    formSetValue: (
        name: string,
        value: unknown,
        options?: {
            shouldDirty?: boolean;
            shouldTouch?: boolean;
            shouldValidate?: boolean;
        },
    ) => void,
    policyP3MatrixSLAs: PolicyP3MatrixSlaResponseDto[],
): void => {
    if (isEmpty(policyP3MatrixSLAs)) {
        runInAction(() => {
            sharedPolicyBuilderController.setIsAddingVersionP3MatrixSLAs(false);
        });

        return;
    }

    const fieldValues = getP3MatrixSLAValues().map(
        ({ definition, examples, severity, timeFrame }, index) => {
            const foundOption = p3MatrixSLAOptions().find(
                (opt) => opt.value === timeFrame,
            );

            if (!foundOption) {
                throw new Error('P3 matrix option not found');
            }
            const { value, label } = foundOption;

            return {
                definition,
                examples,
                severity,
                timeFrame: {
                    id: `${index}`,
                    label,
                    value,
                },
            };
        },
    );

    formSetValue('slaConfiguration.p3MatrixSLAs', fieldValues as never, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
    });
    runInAction(() => {
        sharedPolicyBuilderController.setIsAddingVersionP3MatrixSLAs(true);
    });
};

export const handleReplacePoliciesSelection = (
    selectedItems:
        | ObjectItem<ActivePoliciesDataWithSlaResponseDto>[]
        | ObjectItem<ActivePoliciesDataWithSlaResponseDto>,
    formSetValue: (
        name: string,
        value: unknown,
        options?: {
            shouldDirty?: boolean;
            shouldTouch?: boolean;
            shouldValidate?: boolean;
        },
    ) => void,
    setValue: (value: { id: number; name: string }[]) => void,
): void => {
    const items = Array.isArray(selectedItems)
        ? selectedItems
        : [selectedItems];

    // ALWAYS reset SLA flags first to ensure clean state for each selection change
    runInAction(() => {
        sharedPolicyBuilderController.resetAddingVersionSLAs();
    });

    // Handle empty selection case
    if (isEmpty(items)) {
        // Clear selected policies list
        setValue([]);

        // Clear SLA form fields
        formSetValue('slaConfiguration.gracePeriodSLAs', [] as never, {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
        });
        formSetValue('slaConfiguration.weekTimeFrameSLAs', [] as never, {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
        });
        formSetValue('slaConfiguration.p3MatrixSLAs', [] as never, {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
        });

        return;
    }

    const policiesReplacementData = mapToPoliciesReplacementData(items);

    const policyGracePeriodSLAs = collectGracePeriodSLAs(
        policiesReplacementData,
    );
    const policyWeekTimeFrameSLAs = collectWeekTimeFrameSLAs(
        policiesReplacementData,
    );
    const policyP3MatrixSLAs = collectP3MatrixSLAs(policiesReplacementData);

    // Apply SLA values and set visibility flags appropriately
    applyGracePeriodSLAs(formSetValue, policyGracePeriodSLAs);
    applyWeekTimeFrameSLAs(formSetValue, policyWeekTimeFrameSLAs);
    applyP3MatrixSLAs(formSetValue, policyP3MatrixSLAs);

    setValue(policiesReplacementData.map((p) => ({ id: p.id, name: p.name })));
};
