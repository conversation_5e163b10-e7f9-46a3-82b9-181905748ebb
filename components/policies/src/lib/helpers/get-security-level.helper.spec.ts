import { describe, expect, test } from 'vitest';
import { getSecurityLevel } from './get-security-level.helper';

// Note: i18n is initialized in vitest.setup.ts

describe('getSecurityLevel', () => {
    test('maps severities to human readable labels', () => {
        expect(getSecurityLevel('LOW')).toBe('Low severity level');
        expect(getSecurityLevel('MEDIUM')).toBe('Medium severity level');
        expect(getSecurityLevel('HIGH')).toBe('High severity');
        expect(getSecurityLevel('CRITICAL')).toBe('Critical severity level');
    });

    test('returns Unknown for unrecognized values', () => {
        // @ts-expect-error testing invalid input
        expect(getSecurityLevel('UNKNOWN')).toBe('Unknown');
    });
});
