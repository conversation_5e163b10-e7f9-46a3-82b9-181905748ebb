import { useEffect, useState } from 'react';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { FieldLabel } from '@cosmos/components/field-label';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { addMonths, convertToISO8601String } from '@helpers/date-time';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useUniversalFieldController,
} from '@ui/forms';

/**
 * Renewal schedule type constants.
 */
type RenewalScheduleType =
    | 'ONE_MONTH'
    | 'TWO_MONTHS'
    | 'THREE_MONTHS'
    | 'SIX_MONTHS'
    | 'ONE_YEAR'
    | 'CUSTOM';

const getRenewalScheduleTypeMonthValue = (
    renewalScheduleType: RenewalScheduleType,
): number => {
    switch (renewalScheduleType) {
        case 'ONE_MONTH': {
            return 1;
        }
        case 'TWO_MONTHS': {
            return 2;
        }
        case 'THREE_MONTHS': {
            return 3;
        }
        case 'SIX_MONTHS': {
            return 6;
        }
        case 'ONE_YEAR': {
            return 12;
        }
        case 'CUSTOM': {
            return 0;
        }
    }
};

export interface PoliciesDetailsStepRenewalDateFieldProps
    extends CustomFieldRenderProps {
    value: {
        renewalDate?: TDateISODate;
        renewalFrequency?: RenewalScheduleType;
    };
}
/**
 * Calculate renewal date based on the selected interval.
 *
 * @param intervalValue - The selected renewal interval value.
 * @returns The calculated renewal date as ISO string.
 */
const calculateRenewalDate = (
    intervalValue: RenewalScheduleType,
): TDateISODate => {
    const startDate = new Date();

    // If custom is selected, return today's date for manual selection
    if (intervalValue === 'CUSTOM') {
        return convertToISO8601String(startDate) as TDateISODate;
    }

    // Get the number of months to add
    const monthsToAdd = getRenewalScheduleTypeMonthValue(
        intervalValue as RenewalScheduleType,
    );

    // Calculate the new date
    const renewalDate = new Date(startDate);

    const newRenewalDate = addMonths(renewalDate, monthsToAdd);

    return convertToISO8601String(newRenewalDate) as TDateISODate;
};

export const PoliciesDetailsStepRenewalDateFieldComponent = ({
    'data-id': dataId,
    formId,
    name,
}: PoliciesDetailsStepRenewalDateFieldProps): React.JSX.Element => {
    const [renewalFrequency] = useUniversalFieldController<'select'>(
        `${name}.renewalFrequency`,
    );
    const [renewalDate] = useUniversalFieldController<'date'>(
        `${name}.renewalDate`,
    );

    const [lastAutoDate, setLastAutoDate] = useState<TDateISODate>(
        convertToISO8601String(new Date()) as TDateISODate,
    );
    const [dateInitialized, setDateInitialized] = useState(false);

    useEffect(() => {
        // eslint-disable-next-line react-you-might-not-need-an-effect/no-event-handler -- Required to set initialization flag after mount to prevent premature execution of dependent effects
        if (!dateInitialized) {
            return;
        }
        if (!renewalFrequency.value) {
            return;
        }

        if (renewalFrequency.value.value === 'CUSTOM') {
            return;
        }

        const tempFrequency = renewalFrequency.value;

        const newDate = calculateRenewalDate(
            tempFrequency.value as RenewalScheduleType,
        );

        if (newDate === renewalDate.value) {
            return;
        }
        setLastAutoDate(newDate);
        renewalDate.setValue(newDate);

        // eslint-disable-next-line react-hooks/exhaustive-deps -- need this to avoid infinite loops
    }, [renewalFrequency.value, renewalDate]);

    useEffect(() => {
        // eslint-disable-next-line react-you-might-not-need-an-effect/no-event-handler -- need this for the same as above
        if (!dateInitialized) {
            return;
        }
        if (!renewalDate.value) {
            return;
        }
        if (
            renewalFrequency.value?.value !== 'CUSTOM' &&
            renewalDate.value.valueOf() !== lastAutoDate.valueOf()
        ) {
            const customOption = {
                id: 'renewal-interval-custom',
                label: 'Custom - One time',
                value: 'CUSTOM',
            };

            renewalFrequency.setValue(customOption);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps -- need this to avoid infinite loops
    }, [renewalDate.value]);

    useEffect(() => {
        // eslint-disable-next-line react-you-might-not-need-an-effect/no-initialize-state -- Required to set initialization flag after mount to prevent premature execution of dependent effects
        setDateInitialized(true);
    }, []);

    return (
        <Stack
            direction="column"
            data-testid="PoliciesDetailsStepRenewalDateFieldComponent"
            data-id="_EVYVjyq"
        >
            <FieldLabel
                data-id={`${dataId}.label-frequency-date`}
                gridArea="label"
                htmlFor={'renewal-date'}
                label={t`Renewal date`}
                labelId={`${name}.renewal-frequency`}
                helpText={t`Tasks and monitored tests are automated based on renewal dates.`}
                helpTextId={`${name}-renewal-frequency-help-id`}
                size="sm"
                type="title"
            />
            <Stack
                direction="row"
                align="end"
                gap="xl"
                data-testid="PoliciesDetailsStepRenewalDateFieldComponent"
                data-id="XNZYnjwT"
            >
                <UniversalFormField
                    name={`${name}.renewalFrequency`}
                    formId={formId}
                    data-id={`${dataId}.renewal-frequency`}
                />
                <UniversalFormField
                    name={`${name}.renewalDate`}
                    formId={formId}
                    data-id={`${dataId}.renewal-date`}
                />
            </Stack>
        </Stack>
    );
};
