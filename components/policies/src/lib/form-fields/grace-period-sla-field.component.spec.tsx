import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import { cleanup, render, screen } from '@testing-library/react';
import type { CustomFieldRenderProps } from '@ui/forms';
import { GracePeriodSLAField } from './grace-period-sla-field.component';

// Mock UniversalFormField to avoid needing full form context
vi.mock('@ui/forms', async () => {
    const actual = await vi.importActual('@ui/forms');

    return {
        ...actual,
        UniversalFormField: ({
            name,
            label,
        }: {
            name: string;
            label: string;
        }) => (
            <div
                data-testid="mock-universal-form-field"
                data-id="mock-universal-form-field"
            >
                <label>{label}</label>
                <select name={name}>
                    <option>1 Day</option>
                </select>
            </div>
        ),
    };
});

const formId = 'test-form-id';

describe('gracePeriodSLAField', () => {
    beforeEach(() => {
        cleanup();
    });
    afterEach(() => {
        cleanup();
    });

    test('renders nothing when value is undefined', () => {
        const props = {
            'data-id': 'test',
            name: 'field',
            label: 'Grace',
            formId,
            value: undefined,
            required: false,
            type: 'custom' as const,
            isShown: true,
            fieldSchemaProps: {} as unknown,
            error: undefined,
            isDirty: false,
            isTouched: false,
            isValid: true,
            isValidating: false,
            setValue: vi.fn(),
            onBlur: vi.fn(),
            onChange: vi.fn(),
            ref: null as unknown,
            invalid: false,
            optionalText: '',
            feedback: undefined,
        } as CustomFieldRenderProps;

        render(<GracePeriodSLAField {...props} />);

        // Component returns empty fragment
        const element = screen.queryByTestId('GracePeriodSLAField');

        expect(element).toBeNull();
    });

    test('renders list of periods with labels from meta', () => {
        const props = {
            'data-id': 'test',
            name: 'field',
            label: 'Grace',
            formId,
            value: [
                {
                    period: {
                        id: 1,
                        label: '1 Day',
                        value: 'ONE_DAY',
                        meta: {
                            label: 'Policy Acceptance',
                            id: 1,
                        },
                    },
                },
            ],
            required: false,
            type: 'custom' as const,
            isShown: true,
            fieldSchemaProps: {} as unknown,
            error: undefined,
            isDirty: false,
            isTouched: false,
            isValid: true,
            isValidating: false,
            setValue: vi.fn(),
            onBlur: vi.fn(),
            onChange: vi.fn(),
            ref: null as unknown,
            invalid: false,
            optionalText: '',
            feedback: undefined,
        } as CustomFieldRenderProps;

        render(<GracePeriodSLAField {...props} />);

        // Verify the card renders with the correct title
        expect(screen.getByText('Grace')).toBeDefined();

        // Verify the UniversalFormField is rendered with the correct label from meta
        expect(screen.getByText('Policy Acceptance')).toBeDefined();

        // Verify the select element has the expected name attribute
        const selectElement = screen.getByRole('combobox');

        expect(selectElement).toBeDefined();
        expect(selectElement.getAttribute('name')).toBe('field[0].period');
    });
});
