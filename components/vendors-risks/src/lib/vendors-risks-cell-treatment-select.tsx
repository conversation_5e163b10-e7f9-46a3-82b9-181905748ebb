import { noop } from 'lodash-es';
import { useMemo } from 'react';
import { VendorCurrentRiskUpdateMutationController } from '@controllers/vendors';
import { SelectField } from '@cosmos/components/select-field';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import { getTreatmentPlanOptions } from './constants/vendors-risks.constants';
import { validateTreatmentPlanValue } from './helpers/validate-treatment-plan-value.helper';
import type { VendorsRisksCellTreatmentSelectProps } from './types/vendors-risks.types';

export const VendorsRisksCellTreatmentSelect = observer(
    ({
        row,
    }: VendorsRisksCellTreatmentSelectProps): React.JSX.Element | null => {
        const { treatmentPlan, riskId, title, description } = row.original;

        const mutationController = useMemo(
            () => new VendorCurrentRiskUpdateMutationController(),
            [],
        );
        const { hasError, isPending, updateRiskPartially } = mutationController;

        const treatmentPlanOptions = getTreatmentPlanOptions();

        return (
            <Form
                hasExternalSubmitButton
                formId="risk-vendor-row-treatment-plan"
                data-id="rtHg6THh"
                schema={{
                    treatmentPlan: {
                        type: 'custom',
                        label: t`Treatment`,
                        isOptional: true,
                        loaderLabel: 'Loading...',
                        disabled:
                            isPending ||
                            sharedFeatureAccessModel.isRiskManagerWithRestrictedView,
                        render: (fieldProps) => {
                            const initialValue = treatmentPlanOptions.find(
                                (option) => option.value === treatmentPlan,
                            );

                            return (
                                <SelectField
                                    shouldHideLabel
                                    formId={fieldProps.formId}
                                    label={fieldProps.label}
                                    name={fieldProps.name}
                                    disabled={fieldProps.disabled}
                                    required={fieldProps.required}
                                    data-id={fieldProps['data-id']}
                                    options={treatmentPlanOptions}
                                    defaultValue={initialValue}
                                    value={
                                        hasError
                                            ? initialValue
                                            : fieldProps.value
                                    }
                                    onChange={({ value = 'UNTREATED' }) => {
                                        if (
                                            !validateTreatmentPlanValue(value)
                                        ) {
                                            return;
                                        }

                                        fieldProps.onChange();
                                        updateRiskPartially(riskId, {
                                            title,
                                            description,
                                            treatmentPlan: value,
                                        });
                                    }}
                                />
                            );
                        },
                    },
                }}
                onSubmit={noop}
            />
        );
    },
);
