import { getTreatmentPlanLabel } from '../helpers/vendors-risks-cell-treatment.helpers';
import type { TreatmentPlanOption } from '../types/vendors-risks.types';

export const TREATMENT_PLAN_VALUES = [
    'UNTREATED',
    'MITIGATE',
    'ACCEPT',
    'TRANSFER',
    'AVOID',
];

export const getTreatmentPlanOptions = (): TreatmentPlanOption[] =>
    TREATMENT_PLAN_VALUES.map((value, index) => {
        return {
            id: `${value}-${index}`,
            label: getTreatmentPlanLabel(value),
            value,
        };
    });

const RISK_SCORE_LABELS = {
    1: '1',
    2: '2',
    3: '3',
    4: '4',
    5: '5',
    6: '6',
    7: '7',
    8: '8',
    9: '9',
    10: '10',
};

export const RISK_SCORE_OPTIONS = Object.entries(RISK_SCORE_LABELS).map(
    ([value, label], index) => {
        return {
            id: `${value}-${index}`,
            label,
            value,
        };
    },
);
