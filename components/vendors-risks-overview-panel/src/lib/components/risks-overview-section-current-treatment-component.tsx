import { capitalize, isEmpty, isNil } from 'lodash-es';
import { getTreatmentPlanOptions } from '@components/vendors-risks';
import { sharedVendorsRisksController } from '@controllers/vendors';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getFullName, getInitials } from '@helpers/formatters';
import { getScoreIntensityByThresholds } from '@helpers/risk-score';

interface RisksOverviewSectionCurrentTreatmentComponentProps {
    vendorRiskDetail: RiskWithCustomFieldsResponseDto;
}

export const RisksOverviewSectionCurrentTreatmentComponent = observer(
    ({
        vendorRiskDetail,
    }: RisksOverviewSectionCurrentTreatmentComponentProps): React.JSX.Element => {
        const {
            treatmentPlan,
            treatmentDetails,
            residualImpact,
            residualLikelihood,
            residualScore,
            anticipatedCompletionDate,
            completionDate,
            reviewers,
        } = vendorRiskDetail;

        const { settings, isLoading } = sharedVendorsRisksController;

        const anticipatedDate = isNil(anticipatedCompletionDate)
            ? null
            : formatDate('sentence', anticipatedCompletionDate.split('T')[0]);

        const completedDate = isNil(completionDate)
            ? null
            : formatDate('sentence', completionDate.split('T')[0]);

        const treatmentPlanOptions = getTreatmentPlanOptions();
        const selectedTreatmentPlan = treatmentPlanOptions.find(
            (option) => option.value === treatmentPlan,
        );

        const residualSeverity = getScoreIntensityByThresholds(
            residualScore,
            settings?.thresholds ?? [],
        );

        // Apply conditional display logic based on treatment plan value
        const shouldShowTreatmentPlan = treatmentPlan !== 'UNTREATED';
        const shouldShowResidualFields =
            treatmentPlan === 'MITIGATE' || treatmentPlan === 'TRANSFER';
        const shouldShowAnticipatedDate =
            treatmentPlan === 'MITIGATE' || treatmentPlan === 'TRANSFER';
        const shouldShowCompletedDate = treatmentPlan !== 'UNTREATED';

        return (
            <>
                <Text type="headline" size="400">
                    {t`Current treatment`}
                </Text>

                <KeyValuePair
                    type="TEXT"
                    label={t`Treatment option`}
                    value={selectedTreatmentPlan?.label}
                    showEmptyValue={isNil(selectedTreatmentPlan?.label)}
                />

                {shouldShowTreatmentPlan && (
                    <KeyValuePair
                        type="TEXT"
                        label={t`Treatment plan`}
                        value={treatmentDetails}
                        showEmptyValue={isNil(treatmentDetails)}
                    />
                )}

                {shouldShowResidualFields && (
                    <Grid flow="column" columns="3" gap="6x">
                        <KeyValuePair
                            type="TEXT"
                            label={t`Residual impact`}
                            showEmptyValue={isNil(residualImpact)}
                            value={
                                isNil(residualImpact)
                                    ? null
                                    : residualImpact.toString()
                            }
                        />

                        <KeyValuePair
                            type="TEXT"
                            label={t`Residual likelihood`}
                            showEmptyValue={isNil(residualLikelihood)}
                            value={
                                isNil(residualLikelihood)
                                    ? null
                                    : residualLikelihood.toString()
                            }
                        />

                        <Stack direction="column">
                            <Text type="title" size="100">
                                {t`Residual score`}
                            </Text>
                            {isLoading ? (
                                <Loader isSpinnerOnly label={t`Loading...`} />
                            ) : (
                                <RiskScore
                                    severity={residualSeverity}
                                    size="md"
                                    intensity="strong"
                                    scoreNumber={residualScore}
                                    label={capitalize(residualSeverity)}
                                />
                            )}
                        </Stack>
                    </Grid>
                )}

                <Stack direction="column" gap="6x">
                    {shouldShowAnticipatedDate && (
                        <KeyValuePair
                            type="TEXT"
                            label={t`Anticipated completion date`}
                            value={anticipatedDate}
                            showEmptyValue={isNil(anticipatedDate)}
                        />
                    )}
                    {shouldShowCompletedDate && (
                        <KeyValuePair
                            type="TEXT"
                            label={t`Completed date`}
                            value={completedDate}
                            showEmptyValue={isNil(completedDate)}
                        />
                    )}
                </Stack>

                {shouldShowCompletedDate && (
                    <KeyValuePair
                        type="REACT_NODE"
                        label={t`Reviewers`}
                        value={
                            isEmpty(reviewers) ? (
                                <EmptyValue label={t`No reviewers assigned`} />
                            ) : (
                                <StackedList
                                    aria-label={t`Risk reviewers list`}
                                >
                                    {reviewers.map((reviewer) => {
                                        const {
                                            firstName,
                                            lastName,
                                            id,
                                            avatarUrl,
                                            email,
                                        } = reviewer;
                                        const fullName = getFullName(
                                            firstName,
                                            lastName,
                                        );
                                        const fallbackText =
                                            getInitials(fullName);

                                        return (
                                            <StackedListItem
                                                key={id}
                                                data-id="NF7ZiEip"
                                                primaryColumn={
                                                    <AvatarIdentity
                                                        primaryLabel={fullName}
                                                        secondaryLabel={email}
                                                        imgSrc={avatarUrl}
                                                        size="sm"
                                                        fallbackText={
                                                            fallbackText
                                                        }
                                                    />
                                                }
                                            />
                                        );
                                    })}
                                </StackedList>
                            )
                        }
                    />
                )}

                <Divider />
            </>
        );
    },
);
