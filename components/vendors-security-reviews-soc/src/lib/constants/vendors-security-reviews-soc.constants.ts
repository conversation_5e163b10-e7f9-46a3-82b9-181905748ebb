import type { ComponentProps } from 'react';
import type { FileUploadField } from '@cosmos/components/file-upload-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { RadioOption } from '@cosmos/components/radio-field-group';
import type {
    VendorReviewFindingResponseDto,
    VendorReviewLocationResponseDto,
    VendorReviewRequestDto,
} from '@globals/api-sdk/types';
import {
    enumValuesToSelectOptions,
    getEndUserControlDocumentedLabel,
    getReportCertificationLabel,
    getReportOpinionLabel,
    getScopeInternalTypeLabel,
    getSubserviceOrgsUseInclusiveMethodsLabel,
    getTrustServiceCriteriaLabel,
    getYesNoLabel,
} from '../helpers/soc-constants-helpers.helper';
import type { YesNoOption } from '../types/soc-review-form-values.type';
import type {
    ComplianceReportOpinionOption,
    ComplianceReportScopeCertification,
    ControlsEncompassBusinessNeedsOption,
    EndUserControlDocumentedInPlaceOption,
    EndUserControlInPlaceOption,
    ReportFindingsMaterialImpactOption,
    SocReportScopeType,
    SubserviceOrgsUseInclusiveMethodsOption,
} from '../types/vendors-security-reviews-soc.types';

const SOC_REPORT_VALUES = [
    'SOC_1',
    'SOC_2',
    'SOC_3',
] as const satisfies VendorReviewRequestDto['socReport'][];

export const SOC_FORM_ID = 'vendor-security-review-soc';

export const BRIDGE_LETTER_FILE_UPLOAD_ACCEPTED_FORMATS = [
    'docx',
    'pdf',
] as const satisfies ComponentProps<typeof FileUploadField>['acceptedFormats'];

export const getComplianceReportScopeCertificationOptions =
    (): ListBoxItemData[] =>
        enumValuesToSelectOptions<
            NonNullable<VendorReviewRequestDto['socReport']>
        >(SOC_REPORT_VALUES, getReportCertificationLabel);

/**
 * TODO: generic type should be VendorReviewTrustServiceCategoryMapResponseDto but api response is numbers instead (ticket: https://drata.atlassian.net/browse/ENG-66687).
 */
export const getTrustServiceCriteriaOptions = (): ListBoxItemData[] =>
    enumValuesToSelectOptions<string>(
        ['1', '2', '3', '4', '5'],
        getTrustServiceCriteriaLabel,
    );

export const getComplianceReportScopeTypeOptions = (): ListBoxItemData[] =>
    enumValuesToSelectOptions<SocReportScopeType>(
        ['type1', 'type2'],
        getScopeInternalTypeLabel,
    );

export const getReportOpinionOptions = (): ComplianceReportOpinionOption[] => [
    { id: '1-0', label: getReportOpinionLabel('1'), value: '1' },
    { id: '2-1', label: getReportOpinionLabel('2'), value: '2' },
    { id: '3-2', label: getReportOpinionLabel('3'), value: '3' },
    { id: '4-3', label: getReportOpinionLabel('4'), value: '4' },
];

/**
 * Common helper for YES/NO radio options.
 */
const getYesNoRadioOptions = (): RadioOption[] => [
    { label: getYesNoLabel('YES'), value: 'YES' },
    { label: getYesNoLabel('NO'), value: 'NO' },
];

export const getControlsEncompassBusinessNeedsOptions =
    (): ControlsEncompassBusinessNeedsOption[] => getYesNoRadioOptions();

export const getReportFindingsMaterialImpactOptions =
    (): ReportFindingsMaterialImpactOption[] => getYesNoRadioOptions();

export const getEndUserControlDocumentedInPlaceOptions =
    (): EndUserControlDocumentedInPlaceOption[] =>
        [
            {
                label: getEndUserControlDocumentedLabel('YES_TO_ALL'),
                value: 'YES_TO_ALL',
            },
            {
                label: getEndUserControlDocumentedLabel('NO_TO_ALL'),
                value: 'NO_TO_ALL',
            },
        ] as RadioOption[];

export const getEndUserControlInPlaceOptions =
    (): EndUserControlInPlaceOption[] => getYesNoRadioOptions();

export const getSubserviceOrgsUseInclusiveMethodsOptions =
    (): SubserviceOrgsUseInclusiveMethodsOption[] =>
        [
            {
                label: getSubserviceOrgsUseInclusiveMethodsLabel('YES'),
                value: 'YES',
            },
            {
                label: getSubserviceOrgsUseInclusiveMethodsLabel('NO'),
                value: 'NO',
            },
            {
                label: getSubserviceOrgsUseInclusiveMethodsLabel('NA'),
                value: 'NA',
            },
        ] as RadioOption[];

export const MIN_FINDINGS_LENGTH = 1;
export const MIN_END_USER_CONTROL_LENGTH = 1;
export const MIN_SERVICES_LENGTH = 0;
export const MIN_LOCATIONS_LENGTH = 0;
export const SOC_REPORT_TYPE_1_VALUE = 1;
export const SOC_REPORT_TYPE_2_VALUE = 2;
export const SOC_REPORT_TYPE_3_VALUE = 3;

export const DEFAULT_FINDING: VendorReviewFindingResponseDto = {
    id: 0,
    description: '',
};
export const DEFAULT_END_USER_CONTROL: {
    id: number;
    control: string;
    inPlace: YesNoOption;
} = {
    id: 0,
    control: '',
    inPlace: 'NO',
};

export const DEFAULT_SERVICE: {
    id: number;
    service: string;
} = {
    id: 0,
    service: '',
};

export const DEFAULT_LOCATION: VendorReviewLocationResponseDto = {
    id: 0,
    city: '',
    stateCountry: '',
};

export const CERTIFICATION_SCOPE_COMPLETED_TOTALS = {
    SOC1: 3,
    SOC2: 4,
    SOC3: 2,
} as const satisfies Record<ComplianceReportScopeCertification, number>;

export const CERTIFICATION_SCOPE_COMPLETED_TOTALS_DEFAULT = 4;
export const REVIEWER_INFORMATION_COMPLETED_TOTAL = 2;
export const REPORT_OPINION_COMPLETED_TOTAL = 1;
export const END_USER_CONTROLS_COUNT_TOTAL = 1;
export const NO_REPORT_FINDINGS_NOT_CHECKED = 2;
export const NO_REPORT_FINDINGS_CHECKED = 1;
export const CERTIFICATION_TYPE_PREFIX = 'SOC_';
