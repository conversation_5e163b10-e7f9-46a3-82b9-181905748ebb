import { sharedConnectionsController } from '@controllers/connections';
import {
    type CreateTicketFn,
    type CreateTicketPayload,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import {
    activeMonitoringDetailsController,
    sharedMonitoringDetailsTicketsMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import { sharedUtilitiesTicketsController } from '@controllers/utilities';
import { DEFAULT_PARAMS } from '@cosmos/components/datatable';
import { runInAction, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { CreateTicketModalView } from '@views/create-ticket-modal';

const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';

const openMonitorTicketModal = (
    onCreateTicket: CreateTicketFn,
    defaultDescription?: string,
): void => {
    runInAction(() => {
        sharedTicketAutomationController.loadTicketAutomation(DEFAULT_PARAMS);
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedCreateTicketController.initialize(onCreateTicket, {
            description: defaultDescription,
        });
    });
    modalController.openModal({
        id: CREATE_TICKET_MODAL_ID,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
        content: () => <CreateTicketModalView data-id="MonitorTicketModal" />,
    });
};

export const handleCreateMonitorTicket = (monitorId: string): void => {
    const createMonitorTicket = async (
        payload: CreateTicketPayload,
    ): Promise<void> => {
        await sharedMonitoringDetailsTicketsMutationController.createTicket(
            payload,
            Number(monitorId),
        );

        // Open the utilities sidebar when ticket creation is successful
        when(
            () =>
                !sharedMonitoringDetailsTicketsMutationController
                    .createTicketMutation.isPending,
            () => {
                if (
                    !sharedMonitoringDetailsTicketsMutationController
                        .createTicketMutation.hasError
                ) {
                    sharedUtilitiesTicketsController.openUtility();
                }
            },
        );
    };

    let defaultDescription = `Created in Drata for Monitor ID: ${monitorId}`;

    runInAction(() => {
        const workspaceName = sharedWorkspacesController.currentWorkspace?.name;
        const monitorName =
            sharedMonitoringTestDetailsController.testName ||
            activeMonitoringDetailsController.monitorDetailsData?.testName ||
            `Monitor ${monitorId}`;

        defaultDescription = workspaceName
            ? `Created in Drata for ${workspaceName}'s test: ${monitorName}`
            : `Created in Drata for test: ${monitorName}`;
    });

    openMonitorTicketModal(createMonitorTicket, defaultDescription);
};
