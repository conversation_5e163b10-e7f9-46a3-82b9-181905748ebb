import { activeTicketsMetadataController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedUtilitiesTicketsController } from '@controllers/utilities';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t, Trans } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { useParams } from '@remix-run/react';
import { handleCreateMonitorTicket } from '../helpers/monitor-ticket-creation.helper';

export const ManageCard = observer((): React.JSX.Element => {
    const { monitorId, codebaseId } = useParams();
    const { ticketsInProgress } = activeTicketsMetadataController;
    const {
        isMonitorFailinWithgNoFindingsAndWithFixNowPath,
        checkResultStatus,
    } = sharedMonitoringTestDetailsController;
    const testId = monitorId || codebaseId;

    return (
        <Box
            data-id="Q2_Sn4lR"
            borderRadius="borderRadiusLg"
            borderColor="neutralBorderFaded"
            borderWidth="borderWidth1"
            p="lg"
            width="100%"
        >
            <Stack direction="column" align="start" gap="md" height="100%">
                <Text type="title" size="200">
                    <Trans>Manage</Trans>
                </Text>
                <Stack height="100%">
                    <StatBlock
                        as="div"
                        title={t`Tickets in progress`}
                        statValue={ticketsInProgress}
                        state="static"
                    />
                </Stack>

                <Stack
                    align="start"
                    gap={
                        isMonitorFailinWithgNoFindingsAndWithFixNowPath ||
                        checkResultStatus === 'PASSED'
                            ? '4x'
                            : '2x'
                    }
                    direction={
                        isMonitorFailinWithgNoFindingsAndWithFixNowPath ||
                        checkResultStatus === 'PASSED'
                            ? 'row'
                            : 'column'
                    }
                >
                    {ticketsInProgress > 0 && (
                        <Button
                            hasPadding={false}
                            label={t`View tickets`}
                            level="tertiary"
                            size="sm"
                            onClick={() => {
                                runInAction(() => {
                                    sharedUtilitiesTicketsController.openUtility();
                                });
                            }}
                        />
                    )}
                    <Button
                        hasPadding={false}
                        label={t`Create ticket`}
                        level="tertiary"
                        size="sm"
                        onClick={() => {
                            if (testId) {
                                handleCreateMonitorTicket(testId);
                            }
                        }}
                    />
                </Stack>
            </Stack>
        </Box>
    );
});
