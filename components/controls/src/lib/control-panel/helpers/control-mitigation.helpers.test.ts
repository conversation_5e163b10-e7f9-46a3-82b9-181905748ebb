import { describe, expect, test } from 'vitest';
import {
    createMetadataIfCount,
    getKeyValuePairStructure,
    getMonitoringHealthStatus,
} from './control-mitigation.helpers';

describe('createMetadataIfCount', () => {
    test('should return success color scheme when count is 0', () => {
        const result = createMetadataIfCount(0, true);

        expect(result).toStrictEqual({
            label: '0',
            type: 'number',
            colorScheme: 'success',
        });
    });

    test('should return metadata object when count is greater than 0', () => {
        const result = createMetadataIfCount(5);

        expect(result).toStrictEqual({
            label: '5',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should return metadata object when count is 1', () => {
        const result = createMetadataIfCount(1);

        expect(result).toStrictEqual({
            label: '1',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should convert count to string in label', () => {
        const result = createMetadataIfCount(123);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show "99+" for numbers greater than 99', () => {
        const result = createMetadataIfCount(100);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show "99+" for very large numbers', () => {
        const result = createMetadataIfCount(9999);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show exact number for 99', () => {
        const result = createMetadataIfCount(99);

        expect(result).toStrictEqual({
            label: '99',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show "99+" for 101', () => {
        const result = createMetadataIfCount(101);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should return undefined for negative numbers (edge case)', () => {
        const result = createMetadataIfCount(-1);

        // Based on current logic, negative numbers would return metadata
        // but this might be an edge case to consider
        expect(result).toStrictEqual({
            label: '-1',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should always use "number" type', () => {
        const result = createMetadataIfCount(42);

        expect(result.type).toBe('number');
    });

    test('should always use "critical" color scheme', () => {
        const result = createMetadataIfCount(42);

        expect(result.colorScheme).toBe('critical');
    });
});

describe('getKeyValuePairStructure', () => {
    test('should return correct structure for FAILED status', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'ENABLED',
            'Test Monitor Name',
            '2024-01-15T10:30:00Z',
        );

        expect(result).toHaveLength(4);
        expect(result[0]).toStrictEqual({
            id: 'control-action-panel-header-test-name',
            'data-id': 'control-action-panel-header-test-name',
            label: 'Test name',
            value: 'Test Monitor Name',
            type: 'TEXT',
        });
        expect(result[1]).toStrictEqual({
            id: 'control-action-panel-header-result',
            'data-id': 'control-action-panel-header-result',
            label: 'Result',
            value: {
                label: 'Failed',
                colorScheme: 'critical',
                type: 'status',
            },
            type: 'BADGE',
        });
        // Validate last run structure without checking specific date value
        expect(result[2]).toMatchObject({
            id: 'control-action-panel-header-last-run',
            'data-id': 'control-action-panel-header-last-run',
            label: 'Last run',
            type: 'TEXT',
        });
        expect(typeof result[2].value).toBe('string');
        expect(result[2].value).not.toBe('—');
    });

    test('should return correct structure for PASSED status', () => {
        const result = getKeyValuePairStructure(
            'PASSED',
            'ENABLED',
            'Successful Test',
            '2024-02-20T14:45:00Z',
        );

        expect(result).toHaveLength(4);
        expect(result[0]).toMatchObject({
            id: 'control-action-panel-header-test-name',
            'data-id': 'control-action-panel-header-test-name',
            label: 'Test name',
            value: 'Successful Test',
            type: 'TEXT',
        });
        expect(result[1]).toStrictEqual({
            id: 'control-action-panel-header-result',
            'data-id': 'control-action-panel-header-result',
            label: 'Result',
            value: {
                label: 'Passed',
                colorScheme: 'success',
                type: 'status',
            },
            type: 'BADGE',
        });
        // Validate last run structure without checking specific date value
        expect(result[2]).toMatchObject({
            id: 'control-action-panel-header-last-run',
            'data-id': 'control-action-panel-header-last-run',
            label: 'Last run',
            type: 'TEXT',
        });
        expect(typeof result[2].value).toBe('string');
        expect(result[2].value).not.toBe('—');
    });

    test('should handle missing lastCheck date', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'UNUSED',
            'Test Without Date',
        );

        expect(result[2].value).toBe('—');
    });

    test('should handle UNUSED status with dash for last run', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'UNUSED',
            'Unused Test',
            '2024-01-15T10:30:00Z',
        );

        expect(result[2].value).toBe('—');
    });

    test('should display open tickets count when provided', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'ENABLED',
            'Test Monitor Name',
            '2024-01-15T10:30:00Z',
            5,
        );

        expect(result).toHaveLength(4);
        expect(result[3]).toStrictEqual({
            id: 'control-action-panel-header-open-tickets',
            'data-id': 'control-action-panel-header-open-tickets',
            label: 'Open tickets',
            value: '5',
            type: 'TEXT',
        });
    });

    test('should display dash when open tickets count is not provided', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'ENABLED',
            'Test Monitor Name',
            '2024-01-15T10:30:00Z',
        );

        expect(result).toHaveLength(4);
        expect(result[3]).toStrictEqual({
            id: 'control-action-panel-header-open-tickets',
            'data-id': 'control-action-panel-header-open-tickets',
            label: 'Open tickets',
            value: '—',
            type: 'TEXT',
        });
    });
});

describe('getMonitoringHealthStatus', () => {
    test('should return true when findingsListTotal is 0', () => {
        const result = getMonitoringHealthStatus(0);

        expect(result).toBeTruthy();
    });

    test('should return false when findingsListTotal is greater than 0', () => {
        const result = getMonitoringHealthStatus(5);

        expect(result).toBeFalsy();
    });

    test('should return false when findingsListTotal is 1', () => {
        const result = getMonitoringHealthStatus(1);

        expect(result).toBeFalsy();
    });

    test('should return false for negative findingsListTotal', () => {
        const result = getMonitoringHealthStatus(-1);

        expect(result).toBeFalsy();
    });
});
