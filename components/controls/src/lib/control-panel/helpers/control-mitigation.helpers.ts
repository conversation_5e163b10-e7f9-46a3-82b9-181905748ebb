import { capitalize } from 'lodash-es';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { MetadataProps } from '@cosmos/components/metadata';
import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';
import { CheckResultStatus, CheckStatus } from '@helpers/evidence';
import { getStatusColorScheme } from '@models/monitoring-details';

export const createMetadataIfCount = (
    count: number,
    isHealthy = false,
): MetadataProps => {
    const label = count > 99 ? '99+' : count.toString();
    const colorScheme = isHealthy || count === 0 ? 'success' : 'critical';

    return {
        label,
        type: 'number',
        colorScheme,
    };
};

/**
 * Determines if monitoring is in a healthy state (no issues detected).
 */
export const getMonitoringHealthStatus = (
    findingsListTotal: number,
): boolean => {
    return findingsListTotal === 0;
};

export const getKeyValuePairStructure = (
    checkResultStatus: MonitorV2ControlTestInstanceOverviewResponseDto['checkResultStatus'],
    checkStatus: MonitorV2ControlTestInstanceOverviewResponseDto['checkStatus'],
    name: string,
    lastCheck?: string,
    openTicketsCount?: number,
): KeyValuePairProps[] => {
    const testResultMetadata: Pick<KeyValuePairProps, 'value' | 'type'> =
        CheckResultStatus[checkResultStatus] === CheckResultStatus.READY ||
        CheckStatus[checkStatus] === CheckStatus.UNUSED
            ? {
                  value: '—',
                  type: 'TEXT',
              }
            : {
                  value: {
                      label: capitalize(checkResultStatus),
                      colorScheme: getStatusColorScheme(checkResultStatus),
                      type: 'status' as const,
                  },
                  type: 'BADGE',
              };

    return [
        {
            id: 'control-action-panel-header-test-name',
            'data-id': 'control-action-panel-header-test-name',
            label: t`Test name`,
            value: name,
            type: 'TEXT',
        },
        {
            id: 'control-action-panel-header-result',
            'data-id': 'control-action-panel-header-result',
            label: t`Result`,
            ...testResultMetadata,
        },
        {
            id: 'control-action-panel-header-last-run',
            'data-id': 'control-action-panel-header-last-run',
            label: t`Last run`,
            value:
                CheckStatus[checkStatus] === CheckStatus.UNUSED
                    ? '—'
                    : formatDate('field_time', lastCheck || undefined),
            type: 'TEXT',
        },
        {
            id: 'control-action-panel-header-open-tickets',
            'data-id': 'control-action-panel-header-open-tickets',
            label: t`Open tickets`,
            value: openTicketsCount?.toString() ?? '—',
            type: 'TEXT',
        },
    ] as KeyValuePairProps[];
};
