import { useEffect } from 'react';
import { sharedControlPanelModel } from '@components/controls';
import { sharedMonitorsController } from '@controllers/monitors';
import { panelController } from '@controllers/panel';
import { Metadata } from '@cosmos/components/metadata';
import {
    PanelBody,
    PanelControls,
    PanelHeader,
} from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { Tabs } from '@cosmos/components/tabs';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { action, observer, runInAction } from '@globals/mobx';
import { MonitoringContent } from './components/content-mitigation-panel-tabs/monitoring-content';
import { NotReadyEmptyState } from './components/empty-states/not-ready-empty-state';
import {
    sharedControlMitigationPanelController,
    sharedMonitoringFindingsTableController,
} from './controllers';
import {
    createMetadataIfCount,
    getMonitoringHealthStatus,
} from './helpers/control-mitigation.helpers';

const handleClosePanel = action(() => {
    panelController.closePanel();
});

export const ControlMitigationPanel = observer((): React.JSX.Element => {
    const {
        activeTab,
        setActiveTab,
        controlDetails,
        currentItem,
        totalItems,
        goToControlDetails,
        handleNextPage,
        handlePrevPage,
    } = sharedControlMitigationPanelController;

    useEffect(() => {
        if (!controlDetails?.id) {
            return;
        }
        runInAction(() => {
            sharedControlPanelModel.establishGlobalControlStats();
            sharedMonitorsController.resetSelectedMonitor();
        });
    }, [controlDetails?.id]);

    const handleTabChange = (tabId: string) => {
        setActiveTab(tabId);
    };

    const dynamicPagination = {
        currentItem,
        totalItems,
        onNextPageClick: handleNextPage,
        onPrevPageClick: handlePrevPage,
    };

    const {
        testsCount,
        policiesCount,
        evidencesCount,
        approvalsCount,
        totalCount,
    } = sharedControlPanelModel.controlIssueStats;

    const { total: findingsListTotal } =
        sharedMonitoringFindingsTableController;

    const tabs = [
        {
            tabId: 'monitoring',
            label: t`Monitoring`,
            metadata: createMetadataIfCount(
                testsCount,
                getMonitoringHealthStatus(findingsListTotal),
            ),
            content: (
                <Stack direction="column" mt={'4x'}>
                    <MonitoringContent />
                </Stack>
            ),
        },
        {
            tabId: 'policies',
            label: t`Policies`,
            metadata: createMetadataIfCount(policiesCount),
            content: (
                <Stack direction="column">
                    <Text>{t`Policies content coming soon`}</Text>
                </Stack>
            ),
        },
        {
            tabId: 'evidence',
            label: t`Evidence`,
            metadata: createMetadataIfCount(evidencesCount),
            content: (
                <Stack direction="column">
                    <Text>{t`Evidence content coming soon`}</Text>
                </Stack>
            ),
        },
        {
            tabId: 'approvals',
            label: t`Approvals`,
            metadata: createMetadataIfCount(approvalsCount),
            content: (
                <Stack direction="column">
                    <Text>{t`Approvals content coming soon`}</Text>
                </Stack>
            ),
        },
    ];

    return (
        <Stack
            data-testid="ControlMitigationPanel"
            direction="column"
            height="100%"
            minHeight="0"
            data-id="e3aNT5Ps"
        >
            <PanelControls
                closeButtonLabel={t`Close`}
                data-id="control-mitigation-panel-controls"
                pagination={dynamicPagination}
                onClose={handleClosePanel}
            />
            <PanelHeader
                data-id="control-mitigation-panel-header"
                title={controlDetails?.name ?? ''}
                action={{
                    label: t`Open Details`,
                    level: 'secondary',
                    size: 'sm',
                    colorScheme: 'primary',
                    endIconName: 'Expand',
                    onClick: goToControlDetails,
                }}
                slot={
                    <Metadata
                        label={controlDetails?.code ?? ''}
                        type="tag"
                        colorScheme={
                            controlDetails?.isReady ? 'success' : 'critical'
                        }
                    />
                }
            />

            <PanelBody data-id="panel-body">
                {totalCount === 0 ? (
                    <NotReadyEmptyState />
                ) : (
                    <Tabs
                        tabs={tabs}
                        defaultTabId={activeTab}
                        overflowLeftLabel={t`Scroll tabs to the left`}
                        overflowRightLabel={t`Scroll tabs to the right`}
                        data-id="control-mitigation-tabs"
                        hasPadding={false}
                        onTabChange={handleTabChange}
                    />
                )}
            </PanelBody>
        </Stack>
    );
});
