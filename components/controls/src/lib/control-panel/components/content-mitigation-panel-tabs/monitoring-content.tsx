import { isEmpty } from 'lodash-es';
import { sharedControlPanelModel } from '@components/controls';
import { sharedMonitorsController } from '@controllers/monitors';
import { Feedback } from '@cosmos/components/feedback';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Callout } from '@cosmos-lab/components/callout';
import { List } from '@cosmos-lab/components/list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringFindingsTableController } from '../../controllers/monitoring-findings-table.controller';
import { ControlPanelMonitoringEmptyState } from '../empty-states/control-panel-monitoring-empty-state';
import { MonitoringFindingsTable } from './monitoring-findings-table';

export const MonitoringContent = observer((): React.JSX.Element => {
    const {
        isLoadingControlMonitorsWithIssues,
        setMonitorSelectedValue,
        selectedMonitoredTests,
        monitoringTestsOptions,
    } = sharedMonitorsController;

    const { total: findingsListTotal } =
        sharedMonitoringFindingsTableController;

    const testKeyValuePairs =
        sharedControlPanelModel.getMonitoringKeyValuePairs();

    const hasSelectFieldEnabled = monitoringTestsOptions.length > 1;
    const hasValidSelectedMonitor = !isEmpty(selectedMonitoredTests.value);

    const findingsList = [
        t`This test is failing due to ${findingsListTotal} findings. You can fix the issues or exclude them to pass.`,
    ];

    return (
        <Stack
            direction="column"
            gap={'6x'}
            data-testid="ControlActionPanelMonitoringContent"
            data-id="dvezWLRr"
        >
            {hasSelectFieldEnabled && (
                <Stack
                    direction="column"
                    data-id="monitoring-content"
                    gap={'2x'}
                >
                    <Stack direction="column" width={'50%'}>
                        <SelectField
                            label={t`Failing tests`}
                            placeholder={t`Select a test`}
                            formId="monitoring-test-select"
                            loaderLabel="Loading"
                            name={'monitoringActionsSelect'}
                            value={selectedMonitoredTests}
                            isLoading={isLoadingControlMonitorsWithIssues}
                            options={monitoringTestsOptions}
                            onChange={setMonitorSelectedValue}
                        />
                    </Stack>

                    <Feedback
                        title={t`You're viewing details for one test. To see information for another, select a different test from the dropdown.`}
                        severity="primary"
                    />
                </Stack>
            )}

            {hasValidSelectedMonitor ? (
                <>
                    <Callout
                        data-id="monitoring-control-action-panel-callout"
                        illustrationName="DoubleCheck"
                        illustrationPosition="top"
                        primaryLabelText={t`Test impacting control status`}
                        size="sm"
                        colorScheme="primary"
                        secondaryLabelText={
                            <Stack direction="column" gap={'2x'}>
                                <Text>{t`Take action:`}</Text>
                                <List items={findingsList} />
                            </Stack>
                        }
                    />

                    {!isEmpty(testKeyValuePairs) && (
                        <>
                            <Text allowBold as="h4" size="400">
                                <strong>{t`Findings`}</strong>
                            </Text>

                            <Stack
                                width="100%"
                                gap="2xl"
                                align="center"
                                wrap="wrap"
                                data-id="monitoring-test-details-kvps"
                            >
                                {testKeyValuePairs}
                            </Stack>
                        </>
                    )}
                    <MonitoringFindingsTable />
                </>
            ) : (
                <ControlPanelMonitoringEmptyState />
            )}
        </Stack>
    );
});
