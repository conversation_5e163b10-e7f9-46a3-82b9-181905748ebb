import { noop } from 'lodash-es';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { t } from '@globals/i18n/macro';

export const ControlPanelMonitoringEmptyState = (): React.JSX.Element => {
    return (
        <EmptyState
            illustrationName="Highfive"
            imageSize="md"
            data-testid="ControlPanelMonitoringEmptyState"
            data-id="control-panel-monitoring-empty-state"
            title={t`No mapped test issue detected`}
            description={t`This Control is currently marked as Not Ready, but there are no mapped test failures contributing to that status. Either no tests are mapped, or all mapped tests are passing.`}
            leftAction={
                <Button
                    label={t`View test details`}
                    level="tertiary"
                    onClick={noop}
                />
            }
        />
    );
};
