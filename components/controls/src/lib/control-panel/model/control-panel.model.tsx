import { get, isEmpty, isNil } from 'lodash-es';
import { REVIEW_APPROVAL_STATUS } from '@components/controls';
import { sharedAuditHubControlsController } from '@controllers/audit-hub';
import {
    sharedControlApprovalReviewersController,
    sharedControlApprovalsController,
    sharedControlDetailsController,
    sharedControlFrameworksController,
    sharedControlOwnersController,
    sharedControlRisksController,
    sharedControlsController,
} from '@controllers/controls';
import {
    sharedEvidenceDetailsController,
    sharedEvidenceMutationController,
} from '@controllers/evidence-library';
import {
    activeTicketsMetadataController,
    sharedMonitoringDetailsControlsController,
    sharedMonitoringUnmapControlMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedMonitorsController } from '@controllers/monitors';
import { panelController } from '@controllers/panel';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { sharedPolicyControlsController } from '@controllers/policy-controls';
import {
    sharedRequirementDetailsController,
    sharedRequirementDissociateControlsMutationController,
} from '@controllers/requirements';
import {
    sharedRiskDetailsController,
    sharedRiskMitigationControlsController,
} from '@controllers/risk-details';
import type { ButtonProps } from '@cosmos/components/button';
import {
    KeyValuePair,
    type KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import type { BaseMetadataProps } from '@cosmos/components/metadata';
import type { AvatarStackProps } from '@cosmos-lab/components/avatar-stack';
import type {
    ApprovalUserResponseDto,
    AuditHubCustomerRequestControlResponseDto,
    ControlListResponseDto,
    ControlMonitorResponseDto,
    PolicyControlSummaryResponseDto,
    RiskResponseDto,
} from '@globals/api-sdk/types';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import { sharedEvidenceDetailsModel } from '@models/evidence-library-details';
import type { ControlPanelSource } from '../control-panel-props.type';
import { sharedControlMitigationPanelController } from '../controllers';
import { getKeyValuePairStructure } from '../helpers/control-mitigation.helpers';

interface IGlobalControlStats {
    controlId: number | null;
    testsCount: number;
    policiesCount: number;
    evidencesCount: number;
    approvalsCount: number;
    totalCount: number;
}

export class ControlPanelModel {
    constructor() {
        makeAutoObservable(this);
    }

    controlPanelSource: ControlPanelSource | undefined;

    /**
     * Global control stats to persist during searches.
     */
    globalControlStats: IGlobalControlStats = {
        controlId: null,
        testsCount: 0,
        policiesCount: 0,
        evidencesCount: 0,
        approvalsCount: 0,
        totalCount: 0,
    };

    setControlPanelSource = (controlPanelSource: ControlPanelSource): void => {
        this.controlPanelSource = controlPanelSource;
    };

    get controlsToUse():
        | ControlMonitorResponseDto[]
        | RiskResponseDto[]
        | ControlListResponseDto[]
        | PolicyControlSummaryResponseDto[]
        | AuditHubCustomerRequestControlResponseDto[] {
        switch (this.controlPanelSource) {
            case 'EVIDENCE': {
                return sharedEvidenceDetailsController.evidenceDetailsLinkedControls;
            }
            case 'RISKS': {
                return sharedControlRisksController.controlRisks;
            }
            case 'RISK_MITIGATION_CONTROLS': {
                return sharedRiskDetailsController.controls;
            }
            case 'FRAMEWORK_REQUIREMENTS': {
                return sharedControlsController.controls;
            }
            case 'MONITORING_DETAILS': {
                return sharedMonitoringDetailsControlsController.monitoringDetailsControlsData;
            }
            case 'POLICY': {
                return sharedPolicyBuilderController.policyControlsAssociated;
            }
            case 'CUSTOMER_REQUEST': {
                return sharedAuditHubControlsController.auditCustomerRequestControls;
            }
            default: {
                return [];
            }
        }
    }

    get shouldDisplayControlsPageLink(): boolean {
        return this.controlPanelSource !== 'CUSTOMER_REQUEST';
    }

    get shouldDisplayReadinessSection(): boolean {
        return !['CUSTOMER_REQUEST'].includes(
            this.controlPanelSource as string,
        );
    }

    get shouldDisplayName(): boolean {
        return ['CUSTOMER_REQUEST'].includes(this.controlPanelSource as string);
    }

    get shouldDisplayCode(): boolean {
        return ['CUSTOMER_REQUEST'].includes(this.controlPanelSource as string);
    }

    get shouldDisplayFrameworks(): boolean {
        return this.controlPanelSource !== 'CUSTOMER_REQUEST';
    }

    get shouldDisplayEvidences(): boolean {
        return (
            this.controlPanelSource === 'CUSTOMER_REQUEST' &&
            this.quantityOfRegularEvidences > 0
        );
    }

    get codeColorScheme(): BaseMetadataProps['colorScheme'] {
        const { controlDetails } = sharedControlDetailsController;

        if (!controlDetails || this.shouldDisplayCode) {
            return 'neutral';
        }

        return controlDetails.isReady ? 'success' : 'critical';
    }

    get currentControlIndex(): number {
        return this.controlsToUse.findIndex(
            (control) =>
                control.id === sharedControlDetailsController.controlId,
        );
    }

    get quantityOfRegularEvidences(): number {
        const currentControl = this.controlsToUse.find(
            (control) =>
                control.id === sharedControlDetailsController.controlId,
        );

        if (currentControl && this.controlPanelSource === 'CUSTOMER_REQUEST') {
            return (currentControl as AuditHubCustomerRequestControlResponseDto)
                .quantityOfRegularValidEvidence;
        }

        return 0;
    }

    get controlReviewers(): {
        id: string;
        reviewer: ApprovalUserResponseDto;
        isApproved: boolean;
    }[] {
        const { currentControlApproval } = sharedControlApprovalsController;
        const { controlApprovalsReviewers } =
            sharedControlApprovalReviewersController;

        const isApproved =
            currentControlApproval?.approvalStatus ===
            REVIEW_APPROVAL_STATUS.COMPLETED;

        if (!currentControlApproval) {
            return [];
        }

        return controlApprovalsReviewers.map((reviewer) => {
            const approvalStatus = isApproved
                ? REVIEW_APPROVAL_STATUS.COMPLETED
                : false;

            return {
                id: `${reviewer.id}`,
                reviewer,
                isApproved: approvalStatus
                    ? currentControlApproval.updatedBy?.id === reviewer.id
                    : false,
            };
        });
    }

    get owners(): AvatarStackProps['avatarData'] {
        const { controlOwners } = sharedControlOwnersController;

        return controlOwners.map((owner) => {
            const { firstName, lastName, avatarUrl, email } = owner;
            const fullName = getFullName(firstName, lastName);

            return {
                fallbackText: getInitials(fullName),
                primaryLabel: fullName,
                secondaryLabel: email,
                imgSrc: avatarUrl ?? undefined,
            };
        });
    }

    get frameworksTags(): BaseMetadataProps[] {
        const { allControlFrameworks } = sharedControlFrameworksController;

        if (isEmpty(allControlFrameworks)) {
            return [];
        }

        const frameworksWithPotentialDuplicates = allControlFrameworks.map(
            (framework) => framework.frameworkPill,
        );

        const uniqueFrameworks = [
            ...new Set(frameworksWithPotentialDuplicates),
        ];

        return uniqueFrameworks.map((frameworkPill) => ({
            label: frameworkPill,
            colorScheme: 'neutral',
        }));
    }

    get shouldDisplayMonitoringCard(): boolean {
        const { controlDetails } = sharedControlDetailsController;
        const { isMapControlsTestsEnabled } = sharedEntitlementFlagController;

        return (
            isMapControlsTestsEnabled || (controlDetails?.isMonitored ?? false)
        );
    }

    get panelFooterActions(): ButtonProps[] {
        const { controlDetails } = sharedControlDetailsController;
        const { unlinkControl: unlinkEvidenceControl } =
            sharedEvidenceMutationController;
        const { unlinkControl: unlinkRequirementControl } =
            sharedRequirementDissociateControlsMutationController;
        const { requirement } = sharedRequirementDetailsController;
        const { hasWriteEvidenceLibraryPermission, hasWriteControlPermission } =
            sharedFeatureAccessModel;
        const { isTestEvidence } = sharedEvidenceDetailsModel;

        if (!controlDetails) {
            return [];
        }

        switch (this.controlPanelSource) {
            case 'EVIDENCE': {
                if (!hasWriteEvidenceLibraryPermission || isTestEvidence) {
                    return [];
                }

                return [
                    {
                        label: t`Unmap control`,
                        colorScheme: 'danger',
                        level: 'tertiary',
                        onClick: action(() => {
                            unlinkEvidenceControl(
                                {
                                    id: controlDetails.id,
                                    code: controlDetails.code,
                                },
                                panelController.closePanel,
                            );
                        }),
                    },
                ];
            }
            case 'RISKS': {
                return [];
            }
            case 'RISK_MITIGATION_CONTROLS': {
                const { canUnmapControls } =
                    sharedRiskMitigationControlsController;

                if (!canUnmapControls) {
                    return [];
                }

                return [
                    {
                        label: t`Unmap control`,
                        colorScheme: 'danger',
                        level: 'tertiary',
                        onClick: action(() => {
                            const { riskDetails } = sharedRiskDetailsController;

                            if (riskDetails) {
                                sharedRiskMitigationControlsController.handleUnmapControl(
                                    riskDetails.riskId.toString(),
                                    controlDetails,
                                    panelController.closePanel,
                                );
                            }
                        }),
                    },
                ];
            }
            case 'FRAMEWORK_REQUIREMENTS': {
                if (!hasWriteControlPermission || !requirement) {
                    return [];
                }

                return [
                    {
                        label: t`Unmap control`,
                        colorScheme: 'danger',
                        level: 'tertiary',
                        onClick: action(() => {
                            unlinkRequirementControl(
                                controlDetails.id,
                                requirement.id,
                                panelController.closePanel,
                            );
                        }),
                    },
                ];
            }
            case 'POLICY': {
                return [
                    {
                        label: t`Unlink control`,
                        colorScheme: 'primary',
                        level: 'tertiary',
                        onClick: action(() => {
                            sharedPolicyControlsController.unlinkControlFromPolicy(
                                controlDetails.id,
                                panelController.closePanel,
                            );
                        }),
                    },
                ];
            }
            case 'MONITORING_DETAILS': {
                return this.buildMonitoringFooterOptions(controlDetails.id);
            }
            case 'CUSTOMER_REQUEST': {
                return [];
            }
            default: {
                return [];
            }
        }
    }

    private buildMonitoringFooterOptions(controlId: number): ButtonProps[] {
        const { hasWriteControlPermission, isMapControlsTestsEnabled } =
            sharedFeatureAccessModel;

        if (!hasWriteControlPermission || !isMapControlsTestsEnabled) {
            return [];
        }

        const { unmapControlFromMonitor } =
            sharedMonitoringUnmapControlMutationController;
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return [];
        }

        return [
            {
                label: t`Unmap control`,
                colorScheme: 'danger',
                level: 'tertiary',
                onClick: action(() => {
                    unmapControlFromMonitor(
                        testDetails.testId,
                        controlId,
                        panelController.closePanel,
                    );
                }),
            },
        ];
    }

    getControlById = (controlId?: number): ControlListResponseDto | null => {
        const { controls } = sharedControlsController;

        return controls.find((control) => control.id === controlId) ?? null;
    };

    getControlIssuesTotal(controlId: number): number {
        const controlDetails = this.getControlById(controlId);

        return get(controlDetails, 'issues.total', 0);
    }

    getControlIssueLabel(controlId: number): string {
        const controlDetails = this.getControlById(controlId);
        const controlIssuesTotal = this.getControlIssuesTotal(controlId);

        return isNil(controlDetails) || controlIssuesTotal === 0
            ? '0'
            : String(controlIssuesTotal);
    }

    get openTicketsCount(): number | undefined {
        return activeTicketsMetadataController.ticketsInProgress || undefined;
    }

    loadTicketsMetadata(testId: number): void {
        activeTicketsMetadataController.loadTicketsMetadata(testId);

        when(
            () =>
                !activeTicketsMetadataController.monitorTicketsMetadataQuery
                    .isLoading &&
                activeTicketsMetadataController.monitorTicketsMetadataQuery
                    .hasError,
            () => {
                logger.error({
                    message: 'Failed to load tickets metadata',
                    additionalInfo: {
                        testId,
                        action: 'loadTicketsMetadata',
                    },
                    errorObject: {
                        message:
                            activeTicketsMetadataController
                                .monitorTicketsMetadataQuery.error?.message ??
                            'Unknown error',
                        statusCode: 'unknown',
                    },
                });
            },
        );
    }

    getTestDetailsKeyValuePairContent(): KeyValuePairProps[] {
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return [];
        }

        if (!activeTicketsMetadataController.ticketsMetadata) {
            this.loadTicketsMetadata(testDetails.testId);
        }

        const { checkResultStatus, lastCheck, checkStatus, name } = testDetails;

        const keyValuePairStructure = getKeyValuePairStructure(
            checkResultStatus,
            checkStatus,
            name,
            lastCheck,
            this.openTicketsCount,
        );

        return [...keyValuePairStructure];
    }

    getMonitoringKeyValuePairs(): React.JSX.Element[] {
        const keyValuePairsContent = this.getTestDetailsKeyValuePairContent();

        return keyValuePairsContent.map(
            ({ id, label, type, value, 'data-id': dataId }) => {
                return (
                    <KeyValuePair
                        key={id}
                        label={label}
                        type={type}
                        value={value}
                        data-id={dataId}
                    />
                );
            },
        );
    }

    setGlobalControlStats = (
        controlId: number,
        testsCount: number,
        policiesCount: number,
        evidencesCount: number,
        approvalsCount: number,
        totalCount: number,
    ): void => {
        this.globalControlStats = {
            controlId,
            testsCount,
            policiesCount,
            evidencesCount,
            approvalsCount,
            totalCount,
        };

        if (controlId) {
            sharedMonitorsController.loadAllMonitorsWithIssuesLinkedToControl(
                controlId,
            );
        }
    };

    establishGlobalControlStats = (): void => {
        const { controlDetails } = sharedControlMitigationPanelController;
        const controlIssues = this.getControlById(controlDetails?.id);

        const { tests, policies, evidence, approvals, total } =
            controlIssues?.issues ?? {};

        const requiredValues = [
            tests,
            policies,
            evidence,
            approvals,
            controlDetails,
        ];

        when(
            () => requiredValues.every((value) => !isNil(value)),
            () => {
                this.setGlobalControlStats(
                    controlDetails?.id ?? 0,
                    tests as number,
                    policies as number,
                    evidence as number,
                    approvals as number,
                    total as number,
                );
            },
        );
    };

    get controlIssueStats(): IGlobalControlStats {
        return this.globalControlStats;
    }
}

export const sharedControlPanelModel = new ControlPanelModel();
