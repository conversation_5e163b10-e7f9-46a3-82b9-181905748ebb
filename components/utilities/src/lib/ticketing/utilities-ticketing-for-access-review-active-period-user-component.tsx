import type React from 'react';
import {
    sharedAccessReviewActivePeriodUserTicketCreationController,
    sharedAccessReviewActivePeriodUserTicketsController,
} from '@controllers/access-reviews';
import { sharedConnectionsController } from '@controllers/connections';
import {
    type CreateTicketFn,
    type CreateTicketFormData,
    type CreateTicketPayload,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';
import { CreateTicketModalView } from '@views/create-ticket-modal';
import { openClosedTicketsModal } from './helpers/open-closed-tickets-modal.helper';
import { UtilitiesTicketingBaseComponent } from './utilities-ticketing-base-component';

const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';
/**
 *TODO: review a common controller for this on https://drata.atlassian.net/browse/ENG-70832.
 */
const openCreateTicketModal = action(
    (
        onCreateTicket: CreateTicketFn,
        initData?: Partial<CreateTicketFormData>,
    ): void => {
        sharedTicketAutomationController.loadTicketAutomation({
            globalFilter: {
                search: '',
                filters: {},
            },
            sorting: [],
            pagination: {
                pageIndex: 0,
                pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
                pageSize: DEFAULT_PAGE_SIZE,
            },
        });
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedCreateTicketController.initialize(onCreateTicket, initData);
        modalController.openModal({
            id: CREATE_TICKET_MODAL_ID,
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: true,
            content: () => (
                <CreateTicketModalView data-id="utilities-ticketing-for-access-review-active-period-user-create-ticket-modal" />
            ),
        });
    },
);

export const UtilitiesTicketingForAccessReviewActivePeriodUserComponent =
    observer((): React.JSX.Element => {
        const { applicationId, periodId, personnelId: userId } = useParams();
        //TODO: review a common controller for this on https://drata.atlassian.net/browse/ENG-70832

        const {
            objectName,
            ticketsInProgress,
            isLoadingTicketsInProgress,
            isLoadingTicketsCompleted,
            totalTicketsInProgress,
            totalTicketsCompleted,
            userHasPermissionToCreateTicket,
            hasNextPage,
            loadNextPage,
            unlinkTicket,
            isUnlinkingTicket,
            loadClosedTicketsInfinite,
        } = sharedAccessReviewActivePeriodUserTicketsController;

        const createAccessReviewTicket = async (
            payload: CreateTicketPayload,
        ): Promise<void> => {
            if (!applicationId || !periodId || !userId) {
                return;
            }

            return sharedAccessReviewActivePeriodUserTicketCreationController.createAccessReviewTicket(
                payload,
                {
                    applicationId: Number(applicationId),
                    periodId: Number(periodId),
                    userId: Number(userId),
                },
            );
        };

        const handleCreateTicket = () => {
            if (!applicationId || !periodId || !userId) {
                return;
            }

            const summary = t`Created in Drata for Access Review - Application ID: ${applicationId}, Period ID: ${periodId}, User ID: ${userId}`;
            const description = t`Created in Drata for Access Review - Application ID: ${applicationId}, Period ID: ${periodId}, User ID: ${userId}`;

            openCreateTicketModal(createAccessReviewTicket, {
                summary,
                description,
            });
        };

        const handleViewClosedTickets = () => {
            if (!userId) {
                return;
            }
            // Load the infinite query for closed tickets first
            loadClosedTicketsInfinite(Number(userId));

            openClosedTicketsModal({
                objectName,
                objectId: Number(userId),
                ticketsController:
                    sharedAccessReviewActivePeriodUserTicketsController,
            });
        };

        return (
            <UtilitiesTicketingBaseComponent
                objectName={objectName}
                ticketsInProgress={ticketsInProgress}
                isLoadingTicketsInProgress={isLoadingTicketsInProgress}
                isLoadingTicketsCompleted={isLoadingTicketsCompleted}
                totalTicketsInProgress={totalTicketsInProgress}
                totalTicketsCompleted={totalTicketsCompleted}
                hasNextPage={hasNextPage}
                loadNextPage={loadNextPage}
                isUnlinkingTicket={isUnlinkingTicket}
                data-id="utilities-ticketing-for-access-review-active-period-user"
                unlinkTicket={(ticketId) => {
                    if (!applicationId || !periodId || !userId) {
                        return;
                    }

                    unlinkTicket(ticketId, Number(userId));
                }}
                userHasPermissionToCreateTicket={
                    userHasPermissionToCreateTicket
                }
                onCreateTicket={handleCreateTicket}
                onViewClosedTickets={handleViewClosedTickets}
            />
        );
    });
