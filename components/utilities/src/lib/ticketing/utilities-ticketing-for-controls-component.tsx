import type React from 'react';
import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedControlDetailsController,
    sharedControlTicketCreationController,
    sharedControlTicketsController,
} from '@controllers/controls';
import {
    type CreateTicketFn,
    type CreateTicketFormData,
    type CreateTicketPayload,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useParams } from '@remix-run/react';
import { CreateTicketModalView } from '@views/create-ticket-modal';
import { openClosedTicketsModal } from './helpers/open-closed-tickets-modal.helper';
import { UtilitiesTicketingBaseComponent } from './utilities-ticketing-base-component';

const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';

const openCreateTicketModal = action(
    (
        onCreateTicket: CreateTicketFn,
        initData?: Partial<CreateTicketFormData>,
    ): void => {
        sharedTicketAutomationController.loadTicketAutomation({
            globalFilter: {
                search: '',
                filters: {},
            },
            sorting: [],
            pagination: {
                pageIndex: 0,
                pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
                pageSize: DEFAULT_PAGE_SIZE,
            },
        });
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedCreateTicketController.initialize(onCreateTicket, initData);
        modalController.openModal({
            id: CREATE_TICKET_MODAL_ID,
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: true,
            content: () => <CreateTicketModalView data-id="I8pa5g9P" />,
        });
    },
);

export const UtilitiesTicketingForControlsComponent = observer(
    (): React.JSX.Element => {
        const { controlId } = useParams();

        const {
            objectName,
            ticketsInProgress,
            isLoadingTicketsInProgress,
            isLoadingTicketsCompleted,
            totalTicketsInProgress,
            totalTicketsCompleted,
            userHasPermissionToCreateTicket,
            hasNextPage,
            loadNextPage,
            unlinkTicket,
            isUnlinkingTicket,
            loadClosedTicketsInfinite,
        } = sharedControlTicketsController;

        const createControlTicket = (
            payload: CreateTicketPayload,
        ): Promise<void> => {
            return sharedControlTicketCreationController.createControlTicket(
                payload,
                Number(controlId),
            );
        };

        const handleCreateTicket = () => {
            const { code: controlCode, name: controlName } =
                sharedControlDetailsController.controlDetails ?? {};
            const { name: workspaceName } =
                sharedWorkspacesController.currentWorkspace ?? {};

            openCreateTicketModal(createControlTicket, {
                summary: `Drata | ${workspaceName} | ${controlCode} | ${controlName}`,
                description: `Created in Drata for ${workspaceName}'s ${controlCode}: ${controlName}`,
            });
        };

        const handleViewClosedTickets = () => {
            if (!controlId) {
                return;
            }

            // Load the infinite query for closed tickets first
            loadClosedTicketsInfinite(Number(controlId));

            // Open the modal with the controller reference so it can observe data changes
            openClosedTicketsModal({
                objectName,
                objectId: Number(controlId),
                ticketsController: sharedControlTicketsController,
            });
        };

        return (
            <UtilitiesTicketingBaseComponent
                objectName={objectName}
                ticketsInProgress={ticketsInProgress}
                isLoadingTicketsInProgress={isLoadingTicketsInProgress}
                isLoadingTicketsCompleted={isLoadingTicketsCompleted}
                totalTicketsInProgress={totalTicketsInProgress}
                totalTicketsCompleted={totalTicketsCompleted}
                hasNextPage={hasNextPage}
                loadNextPage={loadNextPage}
                isUnlinkingTicket={isUnlinkingTicket}
                data-id="6sPccBP6"
                unlinkTicket={(ticketId) => {
                    if (!controlId) {
                        return;
                    }

                    unlinkTicket(ticketId, Number(controlId));
                }}
                userHasPermissionToCreateTicket={
                    userHasPermissionToCreateTicket
                }
                onCreateTicket={handleCreateTicket}
                onViewClosedTickets={handleViewClosedTickets}
            />
        );
    },
);
