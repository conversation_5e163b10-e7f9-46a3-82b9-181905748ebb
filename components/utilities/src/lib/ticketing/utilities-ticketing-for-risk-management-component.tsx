import { isEmpty } from 'lodash-es';
import type React from 'react';
import { sharedConnectionsController } from '@controllers/connections';
import {
    type CreateTicketFn,
    type CreateTicketFormData,
    type CreateTicketPayload,
    sharedCreateTicketController,
} from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import {
    sharedRiskTicketCreationController,
    sharedRiskTicketsController,
} from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { useParams } from '@remix-run/react';
import { CreateTicketModalView } from '@views/create-ticket-modal';
import { openClosedTicketsModal } from './helpers/open-closed-tickets-modal.helper';
import { UtilitiesTicketingBaseComponent } from './utilities-ticketing-base-component';

const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';

const openCreateTicketModal = action(
    (
        onCreateTicket: CreateTicketFn,
        initData?: Partial<CreateTicketFormData>,
    ): void => {
        sharedTicketAutomationController.loadTicketAutomation({
            globalFilter: {
                search: '',
                filters: {},
            },
            sorting: [],
            pagination: {
                pageIndex: 0,
                pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
                pageSize: DEFAULT_PAGE_SIZE,
            },
        });
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedCreateTicketController.initialize(onCreateTicket, initData);
        modalController.openModal({
            id: CREATE_TICKET_MODAL_ID,
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: true,
            content: () => <CreateTicketModalView data-id="I8pa5g9P" />,
        });
    },
);

export const UtilitiesTicketingForRiskManagementComponent = observer(
    (): React.JSX.Element => {
        const { riskId } = useParams();

        const {
            objectName,
            ticketsInProgress,
            isLoadingTicketsInProgress,
            isLoadingTicketsCompleted,
            totalTicketsInProgress,
            totalTicketsCompleted,
            userHasPermissionToCreateTicket,
            hasNextPage,
            loadNextPage,
            unlinkTicket,
            isUnlinkingTicket,
            loadClosedTicketsInfinite,
        } = sharedRiskTicketsController;

        const createRiskTicket = (
            payload: CreateTicketPayload,
        ): Promise<void> => {
            if (!riskId) {
                return Promise.resolve();
            }

            return sharedRiskTicketCreationController.createRiskTicket(
                payload,
                riskId,
            );
        };

        const handleCreateTicket = () => {
            const { title, description, categories, owners } =
                sharedRiskDetailsController.riskDetails ?? {};

            const categoryNames =
                categories?.map((category) => category.name) ?? [];
            const categoryLabel = t`Categories`;
            const categoryLine = isEmpty(categoryNames)
                ? ''
                : `\n${categoryLabel}: ${categoryNames.join(', ')}`;

            const ownerNames =
                owners?.map((owner) =>
                    getFullName(owner.firstName, owner.lastName),
                ) ?? [];
            const ownerLabel = t`Owners`;
            const ownerLine = isEmpty(ownerNames)
                ? ''
                : `\n${ownerLabel}: ${ownerNames.join(', ')}`;

            openCreateTicketModal(createRiskTicket, {
                summary: `Drata | ${riskId} | ${title}`,
                description: `Drata | ${riskId} | ${title}\n${description}${categoryLine}${ownerLine}`,
            });
        };

        const handleViewClosedTickets = () => {
            if (!riskId) {
                return;
            }

            loadClosedTicketsInfinite(riskId);

            openClosedTicketsModal({
                objectName,
                objectId: 0,
                ticketsController: sharedRiskTicketsController,
            });
        };

        return (
            <UtilitiesTicketingBaseComponent
                objectName={objectName}
                ticketsInProgress={ticketsInProgress}
                isLoadingTicketsInProgress={isLoadingTicketsInProgress}
                isLoadingTicketsCompleted={isLoadingTicketsCompleted}
                totalTicketsInProgress={totalTicketsInProgress}
                totalTicketsCompleted={totalTicketsCompleted}
                hasNextPage={hasNextPage}
                loadNextPage={loadNextPage}
                isUnlinkingTicket={isUnlinkingTicket}
                data-id="6sPccBP6"
                unlinkTicket={(ticketId) => {
                    unlinkTicket(ticketId);
                }}
                userHasPermissionToCreateTicket={
                    userHasPermissionToCreateTicket
                }
                onCreateTicket={handleCreateTicket}
                onViewClosedTickets={handleViewClosedTickets}
            />
        );
    },
);
