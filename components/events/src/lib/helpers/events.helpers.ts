import type { IconName } from '@cosmos/components/icon';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { ColorScheme } from '@cosmos/components/metadata';
import type { EventDetailsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { EVENT_TYPES } from '../constants/events.constants';

export const getEventCategoryLabel = (
    category: EventDetailsResponseDto['category'],
): string => {
    // eslint-disable-next-line sonarjs/max-switch-cases -- comprehensive mapping to enforce API parity in tests
    switch (category) {
        case 'AGENT': {
            return t`Agent`;
        }
        case 'ASSESSMENT': {
            return t`Assessment`;
        }
        case 'ASSET': {
            return t`Asset`;
        }
        case 'AUDIT': {
            return t`Audit`;
        }
        case 'AUTOPILOT': {
            return t`Autopilot`;
        }
        case 'CODEBASE': {
            return t`Codebase`;
        }
        case 'COMPANY': {
            return t`Company`;
        }
        case 'CONNECTION': {
            return t`Connection`;
        }
        case 'GRC': {
            return t`Governance, Risk, and Compliance`;
        }
        case 'MONITOR': {
            return t`Monitor`;
        }
        case 'PERSONNEL': {
            return t`Personnel`;
        }
        case 'POLICY': {
            return t`Policy`;
        }
        case 'REPORT': {
            return t`Report`;
        }
        case 'USER': {
            return t`User`;
        }
        case 'VENDOR': {
            return t`Vendor`;
        }
        case 'MDM': {
            return t`Mobile Device Management`;
        }
        case 'QUESTIONNAIRE': {
            return t`Questionnaire`;
        }
        case 'DEVICE': {
            return t`Device`;
        }
        case 'TRUST_PAGES': {
            return t`Trust Pages`;
        }
        case 'TRUST_CENTER_PRIVATE_ACCESS': {
            return t`Trust Center Private Access`;
        }
        case 'COMPANY_NOTIFICATION': {
            return t`Company Notification`;
        }
        case 'CUSTOM_FRAMEWORKS': {
            return t`Custom Frameworks`;
        }
        case 'RISK': {
            return t`Risk`;
        }
        case 'PUBLIC_API_KEY': {
            return t`API Key`;
        }
        case 'MULTIPLE_PRODUCT_SUPPORT': {
            return t`Multiple Product Support`;
        }
        case 'SERVICE_PROVIDER': {
            return t`Guest Administrator`;
        }
        case 'TASK': {
            return t`Task`;
        }
        case 'EVIDENCE': {
            return t`Evidence`;
        }
        case 'RESYNC': {
            return t`Resync`;
        }
        case 'AUTOPILOT_RECIPE_SCHEDULE': {
            return t`Autopilot Recipe Schedule`;
        }
        case 'AUTOPILOT_RECIPE': {
            return t`Autopilot Recipe`;
        }
        case 'ACCESS_REVIEW': {
            return t`Access Review`;
        }
        case 'DOCUMENT_SCANNED': {
            return t`Document Scanned`;
        }
        case 'CLOUD_STORAGE': {
            return t`Cloud Storage`;
        }
        case 'VENDOR_PROFILE': {
            return t`Vendor Profile`;
        }
        case 'EXCEPTION_MANAGEMENT': {
            return t`Exception Management`;
        }
        case 'WORKFLOWS': {
            return t`Workflows`;
        }
        case 'CUSTOM_CONNECTION': {
            return t`Custom Connection`;
        }
        case 'TRUST_CENTER_REPORTS': {
            return t`Trust Center Reports`;
        }
        case 'OUTBOUND_WEBHOOKS': {
            return t`Outbound Webhooks`;
        }
        case 'SAFEBASE_MIGRATION': {
            return t`SafeBase Migration`;
        }
        default: {
            return category;
        }
    }
};

export const getEventStatusColor = (
    status: EventDetailsResponseDto['status'],
): ColorScheme => {
    switch (status) {
        case 'PASSED': {
            return 'success';
        }
        case 'FAILED': {
            return 'critical';
        }
        case 'ERROR': {
            return 'warning';
        }
        case 'READY': {
            return 'primary';
        }
        case 'PREAUDIT': {
            return 'neutral';
        }
        default: {
            return 'neutral';
        }
    }
};

export const getEventStatusIcon = (
    status: EventDetailsResponseDto['status'],
): IconName | undefined => {
    switch (status) {
        case 'PASSED': {
            return 'CheckCircle';
        }
        case 'FAILED': {
            return 'NotReady';
        }
        case 'ERROR': {
            return 'WarningTriangle';
        }
        default: {
            return undefined;
        }
    }
};

export const getEventStatusLabel = (
    status: EventDetailsResponseDto['status'],
): string => {
    switch (status) {
        case 'PASSED': {
            return t`Passed`;
        }
        case 'FAILED': {
            return t`Failed`;
        }
        case 'ERROR': {
            return t`Error`;
        }
        case 'READY': {
            return t`Ready`;
        }
        case 'PREAUDIT': {
            return t`'Pre-Audit`;
        }
        default: {
            return status;
        }
    }
};

export const getEventTypeLabel = (
    type: EventDetailsResponseDto['type'],
): string => {
    // eslint-disable-next-line sonarjs/max-switch-cases -- we want this to be over 30
    switch (type) {
        case 'AUDITOR_TOGGLE_READ_ONLY': {
            return t`Auditor Toggle Read Only`;
        }
        case 'POLICY_VERSION_STATUS_DRAFT_FINALIZED': {
            return t`Policy draft finalized`;
        }
        case 'POLICY_VERSION_STATUS_DRAFT_CREATED': {
            return t`Policy draft created`;
        }
        case 'POLICY_VERSION_STATUS_DRAFT_DELETED': {
            return t`Policy deleted`;
        }
        case 'POLICY_VERSION_APPROVED': {
            return t`Policy version approved`;
        }
        case 'POLICY_VERSION_PUBLISHED': {
            return t`Policy version published`;
        }
        case 'POLICY_VERSION_ALERT_CONFIGURED': {
            return t`Policy acknowledgment notification configured`;
        }
        case 'POLICY_VERSION_STATUS_CANCEL_APPROVAL': {
            return t`Policy approval cancelled`;
        }
        case 'POLICY_VERSION_AUTO_ACKNOWLEDGMENT': {
            return t`User policy automatically acknowledged`;
        }
        case 'AGENT_UNREGISTERED': {
            return t`Drata Agent Unregistered`;
        }
        case 'AGENT_USER_DATA_UPDATED': {
            return t`Drata Agent User Data Updated`;
        }
        case 'AP_AGENT_ANTI_VIRUS_APPLICATION': {
            return t`Autopilot Agent Antivirus Application`;
        }
        case 'AP_AGENT_AUTO_UPDATES_ENABLED': {
            return t`Autopilot Agent Auto Updates Enabled`;
        }
        case 'AP_AGENT_PASSWORD_MANAGER': {
            return t`Autopilot Agent Password Manager`;
        }
        case 'AP_COMPANY_BOD_UNIQUE': {
            return t`Autopilot Company Bod Unique`;
        }
        case 'AP_COMPANY_INFRASTRUCTURE_MFA_ENABLED': {
            return t`Autopilot Company Infrastructure MFA Enabled`;
        }
        case 'AP_COMPANY_INFRASTRUCTURE_SSL_ENABLED': {
            return t`Autopilot Company Infrastructure SSL Enabled`;
        }
        case 'AP_COMPANY_POLICIES_APPROVED': {
            return t`Autopilot Company Policies Approved`;
        }
        case 'AP_COMPANY_POLICIES_CREATED': {
            return t`Autopilot Company Policies Created`;
        }
        case 'AP_COMPANY_PRIORITIZE_SECURITY_ISSUES': {
            return t`Autopilot Company Prioritize Security Issues`;
        }
        case 'AP_COMPANY_WEBSITE_SSL_CERT_NOT_EXPIRED': {
            return t`Autopilot Company Website SSL Cert Not Expired`;
        }
        case 'AP_COMPANY_WEBSITE_SSL_CERT_STRONG_CIPHERS': {
            return t`Autopilot Company Website SSL Cert Strong Ciphers`;
        }
        case 'AP_COMPANY_WEBSITE_SSL_ENFORCED': {
            return t`Autopilot Company Website SSL Enforced`;
        }
        case 'AP_COMPANY_WEBSITE_SSL_HAS_KNOWN_ISSUES': {
            return t`Autopilot Company Website SSL Has Known Issues`;
        }
        case 'AP_COMPANY_SECURITY_COMMITTEE': {
            return t`Autopilot Company Security Committee`;
        }
        case 'AP_CONTRACTORS_ACCEPTABLE_USE_POLICY': {
            return t`Autopilot Contractors Acceptable Use Policy`;
        }
        case 'AP_CONTRACTORS_CODE_OF_CONDUCT_POLICY': {
            return t`Autopilot Contractors Code Of Conduct Policy`;
        }
        case 'AP_CONTRACTORS_BACKGROUND_CHECK_COMPLIANCE': {
            return t`Autopilot Contractors Background Check Compliance Completed`;
        }
        case 'AP_ENCRYPTION_IN_TRANSIT': {
            return t`Autopilot Encryption In Transit`;
        }
        case 'AP_EXCESSIVE_PRIVILEGES_ASSIGNED': {
            return t`Autopilot Excessive Privileges Assigned`;
        }
        case 'AP_EXTERNAL_EXPOSURE_OF_CLOUD_RESOURCES': {
            return t`Autopilot External Exposure Of Cloud Resources`;
        }
        case 'AP_EMPLOYEES_ACCEPTABLE_USE_POLICY': {
            return t`Autopilot Employees Acceptable Use Policy`;
        }
        case 'AP_EMPLOYEES_CODE_OF_CONDUCT_POLICY': {
            return t`Autopilot Employees Code Of Conduct Policy`;
        }
        case 'AP_EMPLOYEES_DATA_PROTECTION_POLICY': {
            return t`Autopilot Employees Data Protection Policy`;
        }
        case 'AP_EMPLOYEES_POLICIES_APPROVED': {
            return t`Autopilot Employees Policies Approved`;
        }
        case 'AP_EMPLOYEES_SECURITY_COMPLIANCE': {
            return t`Autopilot Employees Security Compliance Completed`;
        }
        case 'AP_EMPLOYEES_BACKGROUND_CHECK_COMPLIANCE': {
            return t`Autopilot Employees Background Check Compliance Completed`;
        }
        case 'AP_EMPLOYEES_LOCK_SCREEN_COMPLIANCE': {
            return t`Autopilot Employees Lock Screen Compliance Completed`;
        }
        case 'AP_EMPLOYEES_HARD_DRIVE_ENCRYPTED_COMPLIANCE': {
            return t`Autopilot Employees Hard Drive Encryption Compliance Completed`;
        }
        case 'AP_IDENTITY_PROVIDER_MFA_ENABLED': {
            return t`Autopilot Identity Provider MFA Enabled`;
        }
        case 'AP_IDENTITY_PROVIDER_UNKNOWN_EMAIL': {
            return t`Autopilot Identity Provider Unknown Email`;
        }
        case 'AP_INFRASTRUCTURE_ASSETS_SYNCED': {
            return t`Autopilot Infrastructure Assets Sync Completed`;
        }
        case 'AP_INFRASTRUCTURE_AUTHORIZATION_DETAILS': {
            return t`Autopilot Infrastructure Authorization Details`;
        }
        case 'AP_INFRASTRUCTURE_CLOUD_DATA_FREE_STORAGE': {
            return t`Autopilot Infrastructure Cloud Data Free Storage`;
        }
        case 'AP_INFRASTRUCTURE_CLOUD_DATA_MONITOR_CPU_LOAD': {
            return t`Autopilot Infrastructure Cloud Data Monitor CPU Load`;
        }
        case 'AP_INFRASTRUCTURE_CREDENTIAL_REPORT': {
            return t`Autopilot Infrastructure Credential Report`;
        }
        case 'AP_INFRASTRUCTURE_DATA_ENCRYPTED': {
            return t`Autopilot Infrastructure Data Encrypted`;
        }
        case 'AP_INFRASTRUCTURE_DATA_MONITOR_CPU': {
            return t`Autopilot Infrastructure Data Monitor CPU`;
        }
        case 'AP_INFRASTRUCTURE_DATA_MONITOR_FREE_STORAGE': {
            return t`Autopilot Infrastructure Data Monitor Free Storage`;
        }
        case 'AP_INFRASTRUCTURE_DATA_MONITOR_READ_IO': {
            return t`Autopilot Infrastructure Data Monitor Read IO`;
        }
        case 'AP_INFRASTRUCTURE_DATA_MONITOR_WRITE_IO': {
            return t`Autopilot Infrastructure Data Monitor Write IO`;
        }
        case 'AP_INFRASTRUCTURE_DATA_MULTI_AZ': {
            return t`Autopilot Infrastructure Data Multi Az`;
        }
        case 'AP_INFRASTRUCTURE_DISALLOWS_TRAFFIC': {
            return t`Autopilot Infrastructure Disallows Traffic`;
        }
        case 'AP_INFRASTRUCTURE_HAS_BALANCERS': {
            return t`Autopilot Infrastructure Has Balancers`;
        }
        case 'AP_INFRASTRUCTURE_HAS_WAF': {
            return t`Autopilot Infrastructure Has WAF`;
        }
        case 'AP_INFRASTRUCTURE_INSTANCE_MONITOR_CPU': {
            return t`Autopilot Infrastructure Instance Monitor CPU`;
        }
        case 'AP_INFRASTRUCTURE_PUBLIC_SSH_DENIED': {
            return t`Autopilot Infrastructure Public SSH Denied`;
        }
        case 'AP_INFRASTRUCTURE_QUEUE_MONITOR_MESSAGE_AGE': {
            return t`Autopilot Infrastructure Queue Monitor Message Age`;
        }
        case 'AP_INFRASTRUCTURE_STORAGE_ENCRYPTED': {
            return t`Autopilot Infrastructure Storage Encrypted`;
        }
        case 'AP_INFRASTRUCTURE_ROOT_ACCOUNT_UNUSED': {
            return t`Autopilot Infrastructure Root Account Unused`;
        }
        case 'AP_INFRASTRUCTURE_FAILED_BACKUPS_ADDRESSED_IN_TIMELY_MANNER': {
            return t`Autopilot Infrastructure Failed Backups Addressed in Timely Manner`;
        }
        case 'AP_INFRASTRUCTURE_CLOUDTRAIL_LOG_FILE_INTEGRITY': {
            return t`Autopilot Infrastructure CloudTrail Log File Integrity`;
        }
        case 'AP_INFRASTRUCTURE_SQL_FREEABLE_MEMORY_MONITORED': {
            return t`Autopilot Infrastructure SQL Freeable Memory Monitored`;
        }
        case 'AP_INFRASTRUCTURE_FAILED_BACKUP_ALERTS': {
            return t`Autopilot Infrastructure Failed Backup Alerts`;
        }
        case 'AP_INFRASTRUCTURE_DAILY_BACKUP_JOB_STATUS_MONITORED': {
            return t`Autopilot Infrastructure Daily Backup Job Status Monitored`;
        }
        case 'AP_AUTO_SCALE_SERVER_INSTANCES': {
            return t`Autopilot Infrastructure Autoscale Server Instances`;
        }
        case 'AP_INFRASTRUCTURE_IAM_PASSWORD_MINIMUM_LENGTH': {
            return t`Autopilot Infrastructure IAM Password Minimum Length`;
        }
        case 'AP_INFRASTRUCTURE_IAM_PASSWORD_REUSE': {
            return t`Autopilot Infrastructure IAM Password Reuse`;
        }
        case 'AP_INFRASTRUCTURE_RDS_MINIMUM_VERSION_UPGRADE': {
            return t`Autopilot Infrastructure RDS Minimum Version Upgrade`;
        }
        case 'AP_INFRASTRUCTURE_CMK_ROTATION': {
            return t`Autopilot Infrastructure CMK Rotation`;
        }
        case 'AP_INFRASTRUCTURE_HARDWARE_MFA_FOR_ROOT_ACCOUNT': {
            return t`Autopilot Infrastructure Hardware MFA for root account`;
        }
        case 'AP_INFRASTRUCTURE_S3_OBJECT_LEVEL_LOGGING_FOR_RW_EVENTS': {
            return t`Autopilot Infrastructure S3 Object Level Logging For Read and Write Events`;
        }
        case 'AP_INFRASTRUCTURE_MFA_FOR_ROOT_ACCOUNT': {
            return t`Autopilot Infrastructure MFA for root account`;
        }
        case 'AP_INFRASTRUCTURE_EBS_VOLUME_ENCRYPTION': {
            return t`Autopilot Infrastructure EBS Volume Encryption`;
        }
        case 'AP_INFRASTRUCTURE_IAM_PRINCIPLE_OF_LEAST_PRIVILEGE': {
            return t`Autopilot Infrastructure IAM Principle of Least Privilege`;
        }
        case 'AP_INFRASTRUCTURE_EFS_ENCRYPTED_AT_REST': {
            return t`Autopilot Infrastructure EFS Encrypted at Rest`;
        }
        case 'AP_INFRASTRUCTURE_BUCKET_ACCESS_LOGGING': {
            return t`Autopilot Infrastructure Bucket Access Logging`;
        }
        case 'AP_INFRASTRUCTURE_ACLS_PUBLIC_REMOTE_ADMIN_ACCESS_RESTRICTED': {
            return t`Autopilot Infrastructure ACLs Public Remote Administration Access Restricted`;
        }
        case 'AP_INFRASTRUCTURE_IAM_GROUP_BASED_ACCESS_CONTROL': {
            return t`Autopilot Infrastructure IAM Group Based Access Control`;
        }
        case 'AP_INTERNAL_DOCUMENT_ORGANIZATIONAL_CHART': {
            return t`Autopilot Internal Document Organizational Chart`;
        }
        case 'AP_INTERNAL_DOCUMENT_PERFORMANCE_EVALUATION': {
            return t`Autopilot Internal Document Performance Evaluation`;
        }
        case 'AP_INTERNAL_DOCUMENT_EMPLOYEE_AGREEMENT': {
            return t`Autopilot Internal Document Employee Agreement`;
        }
        case 'AP_INTERNAL_DOCUMENT_ENGINEERING_JOB_DESCRIPTION': {
            return t`Autopilot Internal Document Engineering Job Description`;
        }
        case 'AP_INTERNAL_DOCUMENT_MSA': {
            return t`Autopilot Internal Document Master Service Agreement`;
        }
        case 'AP_INTERNAL_POLICY_ACCEPTABLE_USE': {
            return t`Autopilot Internal Policy Acceptable Use`;
        }
        case 'AP_INTERNAL_POLICY_ACCESS_CONTROL': {
            return t`Autopilot Internal Policy Access Control`;
        }
        case 'AP_INTERNAL_POLICY_BACKUPS': {
            return t`Autopilot Internal Policy Backups`;
        }
        case 'AP_INTERNAL_POLICY_SDLC': {
            return t`Autopilot Internal Policy Software Development Life Cycle`;
        }
        case 'AP_INTERNAL_POLICY_CODE_OF_CONDUCT': {
            return t`Autopilot Internal Policy Code Of Conduct`;
        }
        case 'AP_INTERNAL_POLICY_DATA_CLASSIFICATION': {
            return t`Autopilot Internal Policy Data Classification`;
        }
        case 'AP_INTERNAL_POLICY_DATA_PROTECTION': {
            return t`Autopilot Internal Policy Data Protection`;
        }
        case 'AP_INTERNAL_POLICY_DISASTER_RECOVERY': {
            return t`Autopilot Internal Policy Disaster Recovery`;
        }
        case 'AP_INTERNAL_POLICY_EMPLOYEE_ACCESS_TO_CUSTOMER_DATA': {
            return t`Autopilot Internal Policy Employee Access To Customer Data`;
        }
        case 'AP_INTERNAL_POLICY_EMPLOYEE_CONFIDENTIALITY': {
            return t`Autopilot Internal Policy Employee Confidentiality`;
        }
        case 'AP_INTERNAL_POLICY_ENCRYPTION': {
            return t`Autopilot Internal Policy Encryption`;
        }
        case 'AP_INTERNAL_POLICY_INCIDENT_RESPONSE_FOLLOW_UPS': {
            return t`Autopilot Internal Policy Incident Response Follow Ups`;
        }
        case 'AP_INTERNAL_POLICY_INCIDENT_RESPONSE_LESSONS_LEARNED': {
            return t`Autopilot Internal Policy Incident Response Lessons Learned`;
        }
        case 'AP_INTERNAL_POLICY_INCIDENT_RESPONSE_TEAM': {
            return t`Autopilot Internal Policy Incident Response Team`;
        }
        case 'AP_INTERNAL_POLICY_INCIDENT_RESPONSE': {
            return t`Autopilot Internal Policy Incident Response`;
        }
        case 'AP_INTERNAL_POLICY_INFORMATION_SECURITY': {
            return t`Autopilot Internal Policy Information Security`;
        }
        case 'AP_INTERNAL_POLICY_PASSWORD': {
            return t`Autopilot Internal Policy Password`;
        }
        case 'AP_INTERNAL_POLICY_PHYSICAL_SECURITY': {
            return t`Autopilot Internal Policy Physical Security`;
        }
        case 'AP_INTERNAL_POLICY_RESPONSIBLE_DISCLOSURE': {
            return t`Autopilot Internal Policy Responsible Disclosure`;
        }
        case 'AP_INTERNAL_POLICY_RISK_ASSESSMENT': {
            return t`Autopilot Internal Policy Risk Assessment`;
        }
        case 'AP_INTERNAL_POLICY_RISK_REMEDIATION': {
            return t`Autopilot Internal Policy Risk Remediation`;
        }
        case // deprecated
        'AP_INTERNAL_POLICY_SYSTEM_ACCESS_CONTROL': {
            return t`Autopilot Internal Policy System Access Control`;
        }
        case 'AP_INTERNAL_POLICY_VULNERABILITY_MANAGEMENT': {
            return t`Autopilot Internal Policy Vulnerability Management`;
        }
        case 'AP_INTERNAL_POLICY_DATA_RETENTION': {
            return t`Autopilot Internal Policy Data Retention`;
        }
        case 'AP_INTERNAL_POLICY_CUSTOMER_DATA_RETENTION': {
            return t`Autopilot Internal Policy Customer Data Retention`;
        }
        case 'AP_INTERNAL_POLICY_CLEAN_DESK': {
            return t`Autopilot Internal Policy Clean Desk`;
        }
        case 'AP_INTERNAL_POLICY_SENSITIVE_DATA_DISPOSAL': {
            return t`Autopilot Internal Policy Sensitive Data Disposal`;
        }
        case 'AP_INTERNAL_REPORT_ARCHITECTURAL_DIAGRAM': {
            return t`Autopilot Internal Report Architectural Diagram`;
        }
        case 'AP_INTERNAL_REPORT_BACKUP_AND_COMPLETENESS': {
            return t`Autopilot Internal Report Backup And Completeness`;
        }
        case 'AP_INTERNAL_REPORT_DISASTER_RECOVERY': {
            return t`Autopilot Internal Report Disaster Recovery`;
        }
        case 'AP_INTERNAL_REPORT_NETWORK_DIAGRAM': {
            return t`Autopilot Internal Report Network Diagram`;
        }
        case 'AP_INTERNAL_REPORT_PENETRATION': {
            return t`Autopilot Internal Report Penetration`;
        }
        case 'AP_INTERNAL_REPORT_RISK_ASSESSMENT': {
            return t`Autopilot Internal Report Risk Assessment`;
        }
        case 'AP_INTERNAL_REPORT_RISK_REMEDIATION': {
            return t`Autopilot Internal Report Risk Remediation`;
        }
        case 'AP_INTERNAL_REPORT_VULNERABILITY': {
            return t`Autopilot Internal Report Vulnerability`;
        }
        case 'AP_INTERNAL_URL_PRIVACY_POLICY': {
            return t`Autopilot Internal URL Privacy Policy`;
        }
        case 'AP_INTERNAL_URL_SUPPORT': {
            return t`Autopilot Internal URL Support`;
        }
        case 'AP_INTERNAL_URL_TERMS_OF_SERVICE': {
            return t`Autopilot Internal URL Terms Of Service`;
        }
        case 'AP_INTERNAL_URL_EXTERNAL_JOBS': {
            return t`Autopilot Internal URL External Jobs`;
        }
        case 'AP_VERSION_CONTROL_CODE_REVIEW_PROCESS': {
            return t`Autopilot Version Control Code Review Process`;
        }
        case 'AP_VERSION_CONTROL_ENABLED': {
            return t`Autopilot Version Control Enabled`;
        }
        case 'AP_VERSION_CONTROL_MFA_ENABLED': {
            return t`Autopilot Version Control MFA Enabled`;
        }
        case 'AP_INTERNAL_POLICY_PASSWORD_MANAGER_REQUIRED': {
            return t`Autopilot Internal Policy Password Manager Required`;
        }
        case 'AP_INTERNAL_POLICY_SECURITY_AWARENESS': {
            return t`Autopilot Internal Policy Security Awareness`;
        }
        case 'AP_INTERNAL_POLICY_SECURITY_ENCRYPTION': {
            return t`Autopilot Internal Policy Security Encryption`;
        }
        case 'AP_INFRASTRUCTURE_STORAGE_RESTRICTED': {
            return t`Autopilot Infrastructure Storage Restricted`;
        }
        case 'AP_INFRASTRUCTURE_STORAGE_VERSIONING': {
            return t`Autopilot Infrastructure Storage Versioned`;
        }
        case 'AP_INFRASTRUCTURE_DATA_BACKUPS': {
            return t`Autopilot Infrastructure Data Backups`;
        }
        case 'AP_INTERNAL_INFRASTRUCTURE_IDENTITIES_REMOVED': {
            return t`Autopilot Infrastructure Accounts Removed`;
        }
        case 'AP_INTERNAL_INFRASTRUCTURE_IDENTITIES_UNIQUE': {
            return t`Autopilot Infrastructure Unique Accounts`;
        }
        case 'AP_INFRASTRUCTURE_STORAGE_WAF': {
            return t`Autopilot Infrastructure Storage Protected`;
        }
        case 'AP_INTERNAL_VERSION_CONTROL_IDENTITIES_REMOVED': {
            return t`Autopilot Version Control Identities Removed`;
        }
        case 'AP_INTERNAL_VERSION_CONTROL_IDENTITIES_UNIQUE': {
            return t`Autopilot Version Control Unique Accounts`;
        }
        case 'AP_INTERNAL_VERSION_CONTROL_IDENTITIES_ONLY_AUTHORIZED_ACCESS': {
            return t`Autopilot Version Control Access`;
        }
        case 'AP_VERSION_CONTROL_WRITE_ACCESS_TO_REPOSITORY': {
            return t`Autopilot Version Control Write Access`;
        }
        case 'AP_INTERNAL_INFRASTRUCTURE_LINKED': {
            return t`Autopilot Version Infrastructure Linked`;
        }
        case 'AP_VERSION_CONTROL_WRITE_ACCESS_TO_PRODUCTION_CODE': {
            return t`Autopilot Version Control Production Write Access`;
        }
        case 'AP_CONTRACTORS_POLICIES_APPROVED': {
            return t`Autopilot Contractor Policies are Accepted`;
        }
        case 'AP_CONTRACTORS_DATA_PROTECTION_POLICY': {
            return t`Autopilot Contractors Accept The Data Protection Policy`;
        }
        case 'AP_FORMER_PERSONNEL_OFFBOARDED': {
            return t`Autopilot Former Personnel Offboarded`;
        }
        case 'AUTOPILOT_COMPLIANCE_CHECK_UPDATED': {
            return t`Autopilot Compliance Check Updated`;
        }
        case 'AUTOPILOT_FREQUENCY_UPDATED': {
            return t`Autopilot Frequency Updated`;
        }
        case 'AUTOPILOT_PERSONNEL_SEPARATED': {
            return t`Autopilot Personnel Separated`;
        }
        case 'AUTOPILOT_USER_CREATED': {
            return t`Autopilot User Created`;
        }
        case 'AUTOPILOT_USER_UPDATED': {
            return t`Autopilot User Updated`;
        }
        case 'BACKGROUND_CHECK_STARTED': {
            return t`Background Check Status`;
        }
        case 'BACKGROUND_CHECK_MANUAL': {
            return t`Background Check Manual Upload`;
        }
        case 'BACKGROUND_CHECK_DELETED': {
            return t`Background Check Manual Deleted`;
        }
        case 'COMPANY_DATA_UPDATED': {
            return t`Company Data Updated`;
        }
        case 'COMPANY_DOCUMENT_DELETED': {
            return t`Company Document Deleted`;
        }
        case 'COMPANY_DOCUMENT_DOWNLOADED': {
            return t`Company Document Downloaded`;
        }
        case 'COMPANY_DOCUMENT_UPLOADED': {
            return t`Company Document Uploaded`;
        }
        case 'COMPANY_HUMAN_RESOURCES_DATA_UPDATED': {
            return t`Company Human Resources Data Updated`;
        }
        case 'COMPANY_KEY_PERSONNEL_UPDATED': {
            return t`Company Key Personnel Updated`;
        }
        case 'COMPANY_LOGO_UPDATED': {
            return t`Company Logo Updated`;
        }
        case 'COMPANY_ROLES_UPDATED': {
            return t`Company Roles Updated`;
        }
        case 'COMPANY_SECURITY_DATA_UPDATED': {
            return t`Company Security Data Updated`;
        }
        case 'COMPANY_SECURITY_REPORT_SETTING_UPDATED': {
            return t`Company Security Report Setting Updated`;
        }
        case 'COMPANY_ARCHIVED_DOWNLOADED': {
            return t`Company Data Archived`;
        }
        case 'CONTROL_OWNER_ADDED': {
            return t`Control Owner Added`;
        }
        case 'CONTROL_OWNER_DELETED': {
            return t`Control Owner Removed`;
        }
        case 'CONTROL_TEST_ADDED': {
            return t`Mapped test to a control`;
        }
        case 'CONTROL_TEST_DELETED': {
            return t`Unmapped test from a control`;
        }
        case 'CREATE_CONTROL': {
            return t`Control Created`;
        }
        case 'EDIT_CONTROL_INFO': {
            return t`Control Info Edited`;
        }
        case 'CONTROL_INFORMATION_LINKED_ACROSS_OTHER_WORKSPACES': {
            return t`Control Information Linked Across Other Workspaces`;
        }
        case 'EMPLOYMENT_STATUS_UPDATED': {
            return t`Employment Status Updated`;
        }
        case 'FINDING_EXCLUDED': {
            return t`Finding Excluded`;
        }
        case 'FINDING_INCLUDED': {
            return t`Finding Included`;
        }
        case 'POLICIES_DOWNLOAD_ALL': {
            return t`Downloaded all of the active policies`;
        }
        case 'POLICY_ARCHIVED': {
            return t`Policy Archived`;
        }
        case 'POLICY_REPLACED': {
            return t`Policy Replaced`;
        }
        case 'POLICY_RESTORE_ARCHIVED': {
            return t`Archived Policy Restored`;
        }
        case 'POLICY_RESTORE_REPLACED': {
            return t`Replaced Policy Restored`;
        }
        case 'POLICY_ADDED': {
            return t`Policy Added`;
        }
        case 'POLICY_DOWNLOADED': {
            return t`Policy Download`;
        }
        case 'POLICY_OWNER_APPROVED': {
            return t`Policy Owner Approved`;
        }
        case 'POLICY_VERSION_ADDED': {
            return t`Policy Version Added`;
        }
        case 'REQUIREMENT_UNASSOCIATED_TO_CONTROL': {
            return t`Requirement Unassociated to Control`;
        }
        case 'MANUAL_ASSET_CREATED': {
            return t`Asset Added`;
        }
        case 'MANUAL_ASSET_UPDATED': {
            return t`Asset Edited`;
        }
        case 'MANUAL_ASSET_DELETED': {
            return t`Asset Removed`;
        }
        case 'ASSET_NOTES_UPDATED': {
            return t`Asset Notes Updated`;
        }
        case 'MDM_USER_DATA_UPDATED': {
            return t`MDM User Data Updated`;
        }
        case 'ASSETS_DOWNLOAD_ALL': {
            return t`Archived and downloaded all of the assets`;
        }
        case 'COMPANY_WORKSTATION_CONFIGURATION_UPDATED': {
            return t`Company Workstation Configuration Updated`;
        }
        case 'COMPANY_LINKS_DOWNLOADED': {
            return t`Company links Downloaded`;
        }
        case 'COMPANY_NIST_AI_TRAINING_DATA_UPDATED': {
            return t`NIST AI Configuration Updated`;
        }
        case 'COMPANY_PACKAGE_DOWNLOADED': {
            return t`Download Center Package Downloaded`;
        }
        case 'POLICY_REVERT_TO_LATEST_TEMPLATE': {
            return t`Policy reverted to latest template`;
        }
        case 'POLICY_REVERT_TO_LATEST_VERSION': {
            return t`Policy reverted to latest version`;
        }
        case 'POLICY_RESTART_FROM_LATEST_TEMPLATE': {
            return t`Policy restarted with latest template`;
        }
        case 'POLICY_RESTART_FROM_DRAFT': {
            return t`Policy restarted with last draft`;
        }
        case 'FORCE_SYNC_ON_ALL': {
            return t`Force HRIS/IdP Sync on all Personnel`;
        }
        case 'FORCE_SYNC_ON_SOME': {
            return t`Force HRIS/IdP Sync on some Personnel`;
        }
        case 'FORCE_SYNC_ON_A_PERSON': {
            return t`Force HRIS/IdP Sync on a Person`;
        }
        case 'POLICY_VERSION_UPDATED': {
            return t`Policy Version Updated`;
        }
        case 'POLICY_DETAILS_UPDATED': {
            return t`Policy Details Updated`;
        }
        case 'POLICY_VERSION_DELETED': {
            return t`Policy Version Deleted`;
        }
        case 'PROVIDER_CONNECTION_CREATED': {
            return t`Provider Connection Created`;
        }
        case 'PROVIDER_CONNECTION_REMOVED': {
            return t`Provider Connection Removed`;
        }
        case 'PROVIDER_WORKSPACE_CONNECTION_REMOVED': {
            return t`Workspace Removed from Connection`;
        }
        case 'PROVIDER_WORKSPACE_CONNECTION_CREATED': {
            return t`Workspace Added to Connection`;
        }
        case 'PROVIDER_CONNECTION_UPDATED': {
            return t`Provider Connection Updated`;
        }
        case 'REMINDER_EMAIL_SENT': {
            return t`Reminder Email Sent`;
        }
        case 'REPORT_CREATED': {
            return t`Report Created`;
        }
        case 'REPORT_DELETED': {
            return t`Report Deleted`;
        }
        case 'REPORT_DOWNLOADED': {
            return t`Report Downloaded`;
        }
        case 'REPORT_UPDATED': {
            return t`Report Updated`;
        }
        case 'REPORTS_DOWNLOAD_ALL': {
            return t`Archived and downloaded all of the reports`;
        }
        case 'TERMS_AGREED': {
            return t`Terms Agreed`;
        }
        case 'USER_DOCUMENT_DELETED': {
            return t`User Document Deleted`;
        }
        case 'USER_DOCUMENT_DOWNLOADED': {
            return t`User Document Downloaded`;
        }
        case 'USER_DOCUMENT_UPLOADED': {
            return t`User Document Uploaded`;
        }
        case 'USER_IDENTITY_INFRASTRUCTURE_LINK_UPDATED': {
            return t`User Identity Infrastructure Link Updated`;
        }
        case 'USER_IDENTITY_INFRASTRUCTURE_SERVICE_ACCOUNT_UPDATED': {
            return t`User Identity Infrastructure Service Account Updated`;
        }
        case 'USER_IDENTITY_INFRASTRUCTURE_SYNCED': {
            return t`User Identity Infrastructure Synced`;
        }
        case 'USER_IDENTITY_INFRASTRUCTURE_TOGGLE_UPDATED': {
            return t`User Identity Infrastructure Toggle Updated`;
        }
        case 'USER_IDENTITY_INFRASTRUCTURE_USER_CREATED': {
            return t`User Identity Infrastructure User Created`;
        }
        case 'USER_IDENTITY_INFRASTRUCTURE_USER_DISCONNECTED': {
            return t`User Identity Infrastructure User Disconnected`;
        }
        case 'USER_IDENTITY_INFRASTRUCTURE_USER_UPDATED': {
            return t`User Identity Infrastructure User Updated`;
        }
        case 'USER_IDENTITY_PERSONNEL_SYNCED': {
            return t`User Identity Personnel Synced`;
        }
        case 'USER_IDENTITY_VERSION_CONTROL_LINK_UPDATED': {
            return t`User Identity Version Control Link Updated`;
        }
        case 'USER_IDENTITY_VERSION_CONTROL_TOGGLE_UPDATED': {
            return t`User Identity Version Control Toggle Updated`;
        }
        case 'USER_IDENTITY_VERSION_CONTROL_SERVICE_ACCOUNT_UPDATED': {
            return t`User Identity Version Control Service Account Updated`;
        }
        case 'USER_IDENTITY_VERSION_CONTROL_SYNCED': {
            return t`User Identity Version Control Synced`;
        }
        case 'USER_IDENTITY_VERSION_CONTROL_USER_CREATED': {
            return t`User Identity Version Control User Created`;
        }
        case 'USER_IDENTITY_VERSION_CONTROL_USER_DISCONNECTED': {
            return t`User Identity Version Control User Disconnected`;
        }
        case 'USER_IDENTITY_VERSION_CONTROL_USER_UPDATED': {
            return t`User Identity Version Control User Updated`;
        }
        case 'USER_POLICY_ACCEPTED': {
            return t`User Policy Accepted`;
        }
        case 'USER_ROLE_CREATED': {
            return t`User Role Created`;
        }
        case 'USER_ROLE_DELETED': {
            return t`User Role Deleted`;
        }
        case 'AUTOPILOT_USER_DELETED_ROLE': {
            return t`Autopilot User Role Deleted`;
        }
        case 'VENDOR_CREATED': {
            return t`Vendor Created`;
        }
        case 'VENDOR_DELETED': {
            return t`Vendor Deleted`;
        }
        case 'VENDOR_DOCUMENT_DELETED': {
            return t`Vendor Document Deleted`;
        }
        case 'VENDOR_DOCUMENT_DOWNLOADED': {
            return t`Vendor Document Downloaded`;
        }
        case 'VENDOR_DOCUMENT_UPLOADED': {
            return t`Vendor Document Uploaded`;
        }
        case 'VENDORS_DOWNLOAD_ALL': {
            return t`Archived and downloaded all of the vendors`;
        }
        case 'MONITOR_EXCLUSION_CREATED': {
            return t`Control Test Exclusion Created`;
        }
        case 'MONITOR_EXCLUSION_UPDATED': {
            return t`Control Test Exclusion Updated`;
        }
        case 'MONITOR_EXCLUSION_DELETED': {
            return t`Control Test Exclusion Deleted`;
        }
        case 'BACKGROUND_CHECK_COMPLETED': {
            return t`Background Check Completed`;
        }
        case 'AUTOPILOT_BACKGROUND_CHECK_LINKED_TO_PERSONNEL': {
            return t`Autopilot Background Check Linked To Personnel`;
        }
        case 'ARCHIVE_CONTROL': {
            return t`Out of Scope Control`;
        }
        case 'UNARCHIVE_CONTROL': {
            return t`In Scope Control`;
        }
        case 'EXTERNAL_EVIDENCE_DELETED': {
            return t`External Evidence Deleted`;
        }
        case 'REPORT_ASSOCIATED_TO_CONTROL': {
            return t`Report Associated to Control`;
        }
        case 'REPORT_UNASSOCIATED_TO_CONTROL': {
            return t`Report Unassociated to Control`;
        }
        case 'UPLOAD_EXTERNAL_EVIDENCE': {
            return t`Upload External Evidence`;
        }
        case 'POLICY_ASSOCIATED_TO_CONTROL': {
            return t`Policy Associated to Control`;
        }
        case 'POLICY_UNASSOCIATED_TO_CONTROL': {
            return t`Policy Unassociated to Control`;
        }
        case 'REQUIREMENT_ASSOCIATED_TO_CONTROL': {
            return t`Requirement Associated to Control`;
        }
        case 'CONNECTIONS_AUDIT_PACKAGE_DOWNLOADED': {
            return t`Connections Audit Package Downloaded`;
        }
        case 'HUMAN_RESOURCES_AUDIT_PACKAGE_DOWNLOADED': {
            return t`Human Resources Audit Package Downloaded`;
        }
        case 'INFRASTRUCTURE_ACCESS_AUDIT_PACKAGE_DOWNLOADED': {
            return t`Infrastructure Access Audit Package Downloaded`;
        }
        case 'VERSION_CONTROL_AUDIT_PACKAGE_DOWNLOADED': {
            return t`Version Control Audit Package Downloaded`;
        }
        case 'CONTROL_EVIDENCE_PACKAGE_GENERATED': {
            return t`Control Evidence Package Creation`;
        }
        case 'VENDOR_MANUAL_UPLOAD': {
            return t`Attachments Manually Uploaded`;
        }
        case 'VENDOR_QUESTIONNAIRE_RESPONSE_SAVED': {
            return t`Vendor Questionnaire Response Received`;
        }
        case 'VENDOR_QUESTIONNAIRE_ANSWER_SAVED': {
            return t`Vendor Questionnaire Answers Saved`;
        }
        case 'VENDOR_QUESTIONNAIRE_EMAIL_SENT': {
            return t`Vendor Security Questionnaire Sent`;
        }
        case 'QUESTIONNAIRE_ARCHIVE_DOWNLOADED': {
            return t`Vendor Questionnaire files Downloaded`;
        }
        case 'FRAMEWORK_AUDIT_DETAILS_EDITED': {
            return t`Audit details edited`;
        }
        case 'BACKGROUND_LINKED_TO_PERSONNEL': {
            return t`Background Check Linked To Personnel`;
        }
        case 'BACKGROUND_UNLINKED_FROM_PERSONNEL': {
            return t`Background Check Unlinked From Personnel`;
        }
        case 'GROUPS_IDENTITY_CREATED': {
            return t`Autopilot Group(s) added to Drata`;
        }
        case 'GROUPS_IDENTITY_DELETED': {
            return t`Group(s) removed from Drata`;
        }
        case 'GROUPS_PERSONNEL_IDENTITY_ADDED': {
            return t`Autopilot Personnel added to Group`;
        }
        case 'GROUPS_PERSONNEL_IDENTITY_REMOVED': {
            return t`Personnel deleted from IdP and Drata`;
        }
        case 'GROUPS_IDENTITY_UPDATED': {
            return t`Group(s) metadata updated in Drata`;
        }
        case 'POLICY_ASSIGNED_ALL': {
            return t`Policy assigned to All personnel`;
        }
        case 'POLICY_ASSIGNED_NONE': {
            return t`Policy assigned to None`;
        }
        case 'POLICY_ASSIGNED_GROUPS': {
            return t`Policy assigned to Specific Groups`;
        }
        case 'GROUPS_PERSONNEL_DELETED': {
            return t`Autopilot Groups Personnel Policy Removal`;
        }
        case 'GROUPS_PERSONNEL_ADDED': {
            return t`Autopilot Groups Personnel Added`;
        }
        case 'GROUPS_DELETED': {
            return t`Autopilot Policy Groups Removal`;
        }
        case 'POLICY_BECAME_ORPHAN': {
            return t`Autopilot Groups Policy assigned to None because of Group Deletion`;
        }
        case 'SECURITY_AWARENESS_TRAINING_RESET': {
            return t`Security Awareness Training Reset`;
        }
        case 'ACCOUNT_INFORMATION_PACKAGE_CREATION': {
            return t`Account Information Package Creation`;
        }
        case 'AUDITOR_ADDED': {
            return t`Auditor Added`;
        }
        case 'AUDITOR_REMOVED': {
            return t`Auditor Removed`;
        }
        case 'CONTROL_EVIDENCE_DOWNLOADED': {
            return t`Control Evidence Downloaded`;
        }
        case 'CONTROL_NOTE_ADDED': {
            return t`Control Note Added`;
        }
        case 'CONTROL_NOTE_EDITED': {
            return t`Control Note Edited`;
        }
        case 'CONTROL_NOTE_DELETED': {
            return t`Control Note Deleted`;
        }
        case 'CONTROL_TEST_INSTANCE_NOTE_ADDED': {
            return t`Control Test Instance Note Added`;
        }
        case 'CONTROL_TEST_INSTANCE_NOTE_EDITED': {
            return t`Control Test Instance Note Edited`;
        }
        case 'CONTROL_TEST_INSTANCE_NOTE_DELETED': {
            return t`Control Test Instance Note Deleted`;
        }
        case 'CONTROL_TEST_INSTANCE_DRAFT_TEST_CREATED': {
            return t`Draft test created`;
        }
        case 'CONTROL_TEST_INSTANCE_CUSTOM_TEST_EDITED': {
            return t`Custom test logic edited`;
        }
        case 'CONTROL_TEST_INSTANCE_DRAFT_TEST_PUBLISHED': {
            return t`Draft test published`;
        }
        case 'CONTROL_TEST_INSTANCE_DRAFT_TEST_DELETED': {
            return t`Draft test deleted`;
        }
        case 'CONTROL_TEST_INSTANCE_PUBLISH_TEST_DELETED': {
            return t`Publish test deleted`;
        }
        case 'CONTROL_TEST_INSTANCE_DRAFT_TEST_LOGIC_EDITED': {
            return t`Draft test logic edited`;
        }
        case 'HIPAA_TRAINING_RESET': {
            return t`HIPAA Training Reset`;
        }
        case 'AUDITOR_HAS_ENTERED_TO_TENANT_DETAILS': {
            return t`Auditor View Accessed`;
        }
        case 'AUDITOR_DOES_A_READ_ONLY': {
            return t`Auditor Signed In`;
        }
        case 'EXTERNAL_EVIDENCE_EDITED': {
            return t`External Evidence Edited`;
        }
        case 'EXTERNAL_EVIDENCE_URL_EDITED': {
            return t`External Evidence Url Edited`;
        }
        case 'REQUIREMENT_IN_SCOPE': {
            return t`Requirement In Scope`;
        }
        case 'REQUIREMENT_OUT_OF_SCOPE': {
            return t`Requirement Out Of Scope`;
        }
        case 'DEVICE_UNLINKED': {
            return t`Device Unlinked`;
        }
        case 'DEVICE_LINKED': {
            return t`Device Linked`;
        }
        case 'DEVICE_DOCUMENT_DELETED': {
            return t`Device Document Deleted`;
        }
        case 'SECURITY_AWARENESS_TRAINING_UPDATED': {
            return t`Security Awareness Training Updated`;
        }
        case 'HIPAA_TRAINING_UPDATED': {
            return t`HIPAA Training Updated`;
        }
        case 'NIST_AI_TRAINING_UPDATED': {
            return t`NIST AI Training Updated`;
        }
        case 'AP_SECURITY_AWARENESS_TRAINING': {
            return t`Autopilot Security Awareness Training`;
        }
        case 'TRUST_PAGES_SAVED': {
            return t`Trust Pages Saved`;
        }
        case 'DEVICE_SWITCHED': {
            return t`Device Owner Switched`;
        }
        case 'DEVICE_DOCUMENT_DOWNLOADED': {
            return t`Device Document Downloaded`;
        }
        case 'APPROVED_REQUEST': {
            return t`Approved Request`;
        }
        case 'DENIED_REQUEST': {
            return t`Denied Request`;
        }
        case 'NEW_REQUEST': {
            return t`New Request`;
        }
        case 'AUTO_APPROVE_REQUEST': {
            return t`Auto-approved Request`;
        }
        case 'CONTROLS_ASSOCIATED': {
            return t`Controls Associated to Requirement`;
        }
        case 'CONTROLS_UNASSOCIATED': {
            return t`Controls Unassociated from Requirement`;
        }
        case 'USER_IDENTITY_OBSERVABILITY_USER_CREATED': {
            return t`User Identity Observability User Created`;
        }
        case 'USER_IDENTITY_OBSERVABILITY_USER_UPDATED': {
            return t`User Identity Observability User Updated`;
        }
        case 'USER_IDENTITY_OBSERVABILITY_SYNCED': {
            return t`User Identity Observability Synced`;
        }
        case 'USER_IDENTITY_OBSERVABILITY_USER_DISCONNECTED': {
            return t`User Identity Observability User Disconnected`;
        }
        case 'USER_IDENTITY_OBSERVABILITY_LINK_UPDATED': {
            return t`User Identity Observability Link Updated`;
        }
        case 'USER_IDENTITY_OBSERVABILITY_SERVICE_ACCOUNT_UPDATED': {
            return t`User Identity Observability Service Account Updated`;
        }
        case 'AP_ONLY_AUTHORIZED_USERS_CAN_ACCESS_LOG_SINKS': {
            return t`Autopilot Only Authorized Users Can Access Log Sinks`;
        }
        case 'AP_LOGS_ARE_CENTRALLY_STORED': {
            return t`Autopilot Logs Are Centrally Stored`;
        }
        case 'AP_LOGS_ARE_RETAINED_FOR_365_DAYS': {
            return t`Autopilot Logs Are Retained For 365 Days`;
        }
        case 'AP_CAPACITY_AND_USAGE_MONITORING': {
            return t`Autopilot Capacity and Usage Monitoring`;
        }
        case 'AP_LOGS_MONITORED_FOR_SUSPICIOUS_ACTIVITY': {
            return t`Autopilot Logs Monitored for Suspicious Activity`;
        }
        case 'OBSERVABILITY_ACCESS_AUDIT_PACKAGE_DOWNLOADED': {
            return t`Observability Access Audit Package Downloaded`;
        }
        case 'RISK_ADDED': {
            return t`New Risk Added`;
        }
        case 'RISK_EDITED': {
            return t`Risk Edited`;
        }
        case 'RISK_ASSESSMENT_REPORT_GENERATED': {
            return t`Risk Assessment Report Generated`;
        }
        case 'RISK_TREATMENT_REPORT_GENERATED': {
            return t`Risk Treatment Generated`;
        }
        case 'RISKS_DOWNLOADED': {
            return t`All Risks Downloaded`;
        }
        case 'RISKS_FILTERED_VIEW_DOWNLOADED': {
            return t`Risks Filtered View Downloaded`;
        }
        case 'RISK_CATEGORY_CREATED': {
            return t`Category created`;
        }
        case 'RISK_CATEGORY_REMOVED': {
            return t`Categories removed`;
        }
        case 'RISK_OWNERS_ASSIGNED': {
            return t`Risk Owners Assigned`;
        }
        case 'RISK_SET_APPLICABLE': {
            return t`Risk was moved to risk register`;
        }
        case 'RISK_SET_NOT_APPLICABLE': {
            return t`Risk was moved to risk library`;
        }
        case 'RISK_SET_ACTIVE': {
            return t`Risk marked active`;
        }
        case 'RISK_ARCHIVED': {
            return t`Risk archived`;
        }
        case 'RISK_CLOSED': {
            return t`Risk closed`;
        }
        case 'RISK_ADDED_FROM_LIBRARY': {
            return t`Risk added from library`;
        }
        case 'RISK_TREATMENT_SET': {
            return t`Risk Treatment Set`;
        }
        case 'RISK_SCORED': {
            return t`Risk Scored`;
        }
        case 'RISK_ALL_REGISTERED_SCORED': {
            return t`All Registered Risks Scored`;
        }
        case 'RISK_ALL_REGISTERED_TREATED': {
            return t`All Registered Risks Treated`;
        }
        case 'RISK_MAPPED_TO_CONTROL': {
            return t`Risk associated to control`;
        }
        case 'RISK_UNMAPPED_FROM_CONTROL': {
            return t`Risk unassociated from control`;
        }
        case 'COMPANY_NOTIFICATION_CREATED': {
            return t`Company Notification Created`;
        }
        case 'COMPANY_NOTIFICATION_DELETED': {
            return t`Company Notification Deleted`;
        }
        case 'COMPANY_NOTIFICATION_EDITED': {
            return t`Company Notification Edited`;
        }
        case 'COMPANY_NOTIFICATION_DISABLED': {
            return t`Company Notification Disabled`;
        }
        case 'COMPANY_NOTIFICATION_ENABLED': {
            return t`Company Notification Enabled`;
        }
        case 'CUSTOM_FRAMEWORK_CREATED': {
            return t`Framework Created`;
        }
        case 'CUSTOM_FRAMEWORK_DELETED': {
            return t`Framework Deleted`;
        }
        case 'CUSTOM_REQUIREMENTS_CREATED': {
            return t`Requirements Created`;
        }
        case 'CUSTOM_REQUIREMENTS_UPDATED': {
            return t`Requirements Updated`;
        }
        case 'CUSTOM_REQUIREMENTS_DELETED': {
            return t`Requirement Deleted`;
        }
        case 'TICKET_CREATED_MONITOR_INSTANCE': {
            return t`Ticket Created for Test`;
        }
        case 'TICKET_CREATED_CONTROL': {
            return t`Ticket Created for Control`;
        }
        case 'TICKET_CREATED_RISK': {
            return t`Ticket Created for Risk`;
        }
        case 'TICKET_CREATED_APPLICATION_USERS': {
            return t`Ticket Created for Application Users`;
        }
        case 'TICKET_UNLINKED_RISK': {
            return t`Ticket Unlinked from Risk`;
        }
        case 'TICKET_UNLINKED_MONITOR_INSTANCE': {
            return t`Ticket Unlinked from Test`;
        }
        case 'TICKET_UNLINKED_CONTROL': {
            return t`Ticket Unlinked from Control`;
        }
        case 'PRE_APPROVED_DOMAIN_ADDED': {
            return t`Pre-Approved Domain Added`;
        }
        case 'PRE_APPROVED_DOMAIN_REMOVED': {
            return t`Pre-Approved Domain Removed`;
        }
        case 'PUBLIC_API_KEY_CREATED': {
            return t`API Key Created`;
        }
        case 'PUBLIC_API_KEY_UPDATED': {
            return t`API Key Updated`;
        }
        case 'PUBLIC_API_KEY_REVOKED': {
            return t`API Key Revoked`;
        }
        case 'PUBLIC_API_KEY_ALLOW_LIST_IP_ADDRESSES_EDITED': {
            return t`API Key Allowed IP Address List Edited`;
        }
        case 'DRATA_SUPPORT_ACCESS_REMOVED': {
            return t`Drata Support Access Removed`;
        }
        case 'DRATA_SUPPORT_ACCESS_GRANTED': {
            return t`Drata Support Access Granted`;
        }
        case 'DEVICE_DOCUMENT_UPLOADED': {
            return t`Device Document Uploaded`;
        }
        case 'PERSONNEL_OFFBOARDING_TICKET_LINKED': {
            return t`Ticket linked as Offboarding Evidence`;
        }
        case 'PERSONNEL_OFFBOARDING_TICKET_UNLINKED': {
            return t`Ticket unlinked from Offboarding Evidence`;
        }
        case 'AUTOMATED_OFFBOARDING_TOGGLE': {
            return t`Automatic Offboarding Evidence Collection toggle changed`;
        }
        case 'AUTOMATED_OFFBOARDING_CONFIGURED': {
            return t`Automated Offboarding Evidence Collection configured`;
        }
        case 'AUTOMATED_OFFBOARDING_FAILED': {
            return t`Daily Automated Offboarding Evidence Collection failed`;
        }
        case 'WORKSPACE_CREATED': {
            return t`Workspace Added`;
        }
        case 'WORKSPACE_EDITED': {
            return t`Workspace Edited`;
        }
        case 'EXTERNAL_DOCUMENT_LINKED_TO_POLICY': {
            return t`External Document Linked to Policy Template`;
        }
        case 'VULNERABILITY_AUDIT_PACKAGE_DOWNLOAD': {
            return t`Vulnerability Audit Package Download`;
        }
        case 'RISK_NOTE_ADDED': {
            return t`Risk Note Added`;
        }
        case 'RISK_NOTE_UPDATED': {
            return t`Risk Note Updated`;
        }
        case 'RISK_NOTE_DELETED': {
            return t`Risk Note Deleted`;
        }
        case 'DEVICE_DELETED': {
            return t`Device deleted`;
        }
        case 'NDA_UPLOADED': {
            return t`NDA Uploaded`;
        }
        case 'NDA_DELETED': {
            return t`NDA Deleted`;
        }
        case 'VENDOR_SECURITY_QUESTIONNAIRE_CREATED': {
            return t`Vendor Security Questionnaire Added`;
        }
        case 'VENDOR_SECURITY_QUESTIONNAIRE_UPDATED': {
            return t`Vendor Security Questionnaire Modified`;
        }
        case 'VENDOR_SECURITY_QUESTIONNAIRE_DELETED': {
            return t`Vendor Security Questionnaire Deleted`;
        }
        case 'RISK_DASHBOARD_DOWNLOADED': {
            return t`Risk dashboard downloaded`;
        }
        case 'SERVICE_PROVIDER_DELETED': {
            return t`Guest Administrator Removed`;
        }
        case 'SERVICE_PROVIDER_ADDED': {
            return t`Guest Administrator Added`;
        }
        case 'TASK_CREATED': {
            return t`Task Created`;
        }
        case 'TASK_UPDATED': {
            return t`Task Updated`;
        }
        case 'TASK_DELETED': {
            return t`Task Deleted`;
        }
        case 'TASK_COMPLETED': {
            return t`Task Completed`;
        }
        case 'TASK_UNCOMPLETED': {
            return t`Task Not Completed`;
        }
        case 'GENERAL_TASK_CREATED': {
            return t`General Task Created`;
        }
        case 'GENERAL_TASK_UPDATED': {
            return t`General Task Updated`;
        }
        case 'GENERAL_TASK_DELETED': {
            return t`General Task Deleted`;
        }
        case 'GENERAL_TASK_COMPLETED': {
            return t`General Task Completed`;
        }
        case 'GENERAL_TASK_UNCOMPLETED': {
            return t`General Task Not Completed`;
        }
        case 'CONTROL_TASK_CREATED': {
            return t`Control Task Created`;
        }
        case 'CONTROL_TASK_UPDATED': {
            return t`Control Task Updated`;
        }
        case 'CONTROL_TASK_DELETED': {
            return t`Control Task Deleted`;
        }
        case 'CONTROL_TASK_COMPLETED': {
            return t`Control Task Completed`;
        }
        case 'CONTROL_TASK_UNCOMPLETED': {
            return t`Control Task Not Completed`;
        }
        case 'RISK_TASK_CREATED': {
            return t`Risk Task Created`;
        }
        case 'RISK_TASK_UPDATED': {
            return t`Risk Task Updated`;
        }
        case 'RISK_TASK_DELETED': {
            return t`Risk Task Deleted`;
        }
        case 'RISK_TASK_COMPLETED': {
            return t`Risk Task Completed`;
        }
        case 'RISK_TASK_UNCOMPLETED': {
            return t`Risk Task Not Completed`;
        }
        case 'RISK_ASSESSMENT_OWNER_ASSIGNED': {
            return t`Risk Assessment Owner Assigned`;
        }
        case 'RISK_ASSESSMENT_DUE_DATE_SET': {
            return t`Risk Assessment Due Date Set`;
        }
        case 'FILE_UPLOAD_REJECTED': {
            return t`File Upload Rejected`;
        }
        case 'FILE_UPLOAD_UNSUPPORTED': {
            return t`File Upload Unsupported`;
        }
        case 'EVIDENCE_DELETED': {
            return t`Evidence Deleted`;
        }
        case 'EVIDENCE_BULK_DELETED': {
            return t`Evidence Bulk Deleted`;
        }
        case 'EVIDENCE_CREATED': {
            return t`Evidence Created`;
        }
        case 'EVIDENCE_DOWNLOADED': {
            return t`Evidence Downloaded`;
        }
        case 'EVIDENCE_UPDATED': {
            return t`Evidence Updated`;
        }
        case 'EVIDENCE_ASSOCIATED_TO_CONTROL': {
            return t`Evidence Associated to Control`;
        }
        case 'EVIDENCE_UNASSOCIATED_TO_CONTROL': {
            return t`Evidence Unassociated to Control`;
        }
        case 'EVIDENCE_DOWNLOAD_ALL': {
            return t`Archived and downloaded all of the evidence`;
        }
        case 'PERSONNEL_EXCLUSION_CREATED': {
            return t`Personnel Exclusion Created`;
        }
        case 'PERSONNEL_EXCLUSION_UPDATED': {
            return t`Personnel Exclusion Updated`;
        }
        case 'PERSONNEL_EXCLUSION_ARCHIVED': {
            return t`Personnel Exclusion Archived`;
        }
        case 'RISKS_DELETED': {
            return t`Risk(s) deleted`;
        }
        case 'RESYNC_DATA': {
            return t`Data Resync Requested`;
        }
        case 'VULNERABILITY_DOWNLOAD_ALL': {
            return t`Archived and downloaded all of the vulnerability reports`;
        }
        case 'AUDITOR_REMOVED_FROM_AUDIT': {
            return t`Auditor Removed`;
        }
        case 'AUDITOR_ADDED_TO_AUDIT': {
            return t`Auditor Added`;
        }
        case 'AUTOPILOT_SCHEDULE_CREATED': {
            return t`Autopilot Schedule Created`;
        }
        case 'AUTOPILOT_SCHEDULE_UPDATED': {
            return t`Autopilot Schedule Updated`;
        }
        case 'AUTOPILOT_SCHEDULE_DELETED': {
            return t`Autopilot Schedule Deleted`;
        }
        case 'AUTOPILOT_SCHEDULE_REVERTED': {
            return t`Autopilot Schedule Reverted`;
        }
        case 'AUTOPILOT_RECIPE_CUSTOMIZED': {
            return t`Autopilot Recipe Customized`;
        }
        case 'AUTOPILOT_RECIPE_REVERTED': {
            return t`Autopilot Recipe Reverted`;
        }
        case 'CONTROL_APPROVAL_ADDED': {
            return t`Control Approval Added`;
        }
        case 'CONTROL_APPROVAL_EDITED': {
            return t`Control Approval Updated`;
        }
        case 'CONTROL_APPROVAL_REVIEW_ADDED': {
            return t`Control Approver Added`;
        }
        case 'CONTROL_APPROVAL_REVIEW_REMOVED': {
            return t`Control Approver Removed`;
        }
        case 'CONTROL_APPROVAL_SENT_TO_APPROVERS': {
            return t`Control Approval Sent`;
        }
        case 'CONTROL_APPROVAL_REQUEST_CHANGES': {
            return t`Control Request Changes`;
        }
        case 'CONTROL_APPROVAL_APPROVE': {
            return t`Control Approved`;
        }
        case 'CONTROL_APPROVAL_REMOVED': {
            return t`Control Approval Deleted`;
        }
        case 'CUSTOMER_REQUEST_DETAILS_EDITED': {
            return t`Request details edited`;
        }
        case 'RESENT_EVIDENCE_SAMPLE_DOWNLOAD_EMAIL': {
            return t`Resent Evidence Sample Download Email`;
        }
        case 'REQUIREMENT_INFO_EDITED': {
            return t`Requirement info edited`;
        }
        case 'FRAMEWORK_BASELINE_UPDATED': {
            return t`Framework baseline updated`;
        }
        case 'FRAMEWORK_SAQ_UPDATED': {
            return t`Framework SAQ type updated`;
        }
        case 'ACCESS_REVIEW_PERIOD_CREATED': {
            return t`Access Review Period Created`;
        }
        case 'ACCESS_REVIEW_PERIOD_IN_PROGRESS': {
            return t`Access Review Period In Progress`;
        }
        case 'ACCESS_REVIEW_PERIOD_COMPLETED': {
            return t`Access Review Period Completed`;
        }
        case 'ACCESS_REVIEW_APPLICATION_REVIEWER_UPDATED': {
            return t`Access Review Application Reviewer Updated`;
        }
        case 'ACCESS_REVIEW_APPLICATION_REVIEWER_NOTIFIED': {
            return t`Access Review Application Reviewer Notified`;
        }
        case 'PERSONNEL_REVIEW_STATUS_CHANGED': {
            return t`Personnel Review Status Changed`;
        }
        case 'ACCESS_REVIEW_TICKET_LINKED_TO_PERSONNEL': {
            return t`Access Review Ticket Linked to Personnel`;
        }
        case 'ACCESS_REVIEW_MANUAL_EVIDENCE_UPLOADED': {
            return t`Access Review Manual Evidence Uploaded`;
        }
        case 'ACCESS_REVIEW_APPLICATION_COMPLETED': {
            return t`Access Review Application Completed`;
        }
        case 'DEVICE_ASSIGNATION_AGENT': {
            return t`EDR to Drata device mapping created`;
        }
        case 'DEVICE_ASSIGNATION_AGENT_DELETED': {
            return t`EDR to Drata device mapping deleted`;
        }
        case 'CONTROL_APPROVAL_STATUS_CHANGED': {
            return t`Control Approval Status Changed`;
        }
        case 'DRATA_TO_DRATA_ENABLED': {
            return t`Drata to Drata toggle enabled`;
        }
        case 'DRATA_TO_DRATA_DISABLED': {
            return t`Drata to Drata toggle disabled`;
        }
        case 'CLOUD_FILE_USER_DISCONNECTED': {
            return t`Cloud Connection Removed`;
        }
        case 'CLOUD_FILE_USER_CONNECTED': {
            return t`Cloud Connection Created`;
        }
        case 'CONTROL_TEMPLATE_APPLIED': {
            return t`Drata's templated control language applied`;
        }
        case 'FRAMEWORK_CONTROL_MAPPING_RESET': {
            return t`Mapped Controls Reset For Framework`;
        }
        case 'AP_CUSTOM_TEST': {
            return t`Autopilot Custom Test`;
        }
        case 'AP_DRAFT_TEST': {
            return t`Autopilot Draft Test`;
        }
        case 'USER_ROLE_UNRESTRICTED': {
            return t`Restricted view turned off`;
        }
        case 'USER_ROLE_RESTRICTED': {
            return t`Restricted view turned on`;
        }
        case 'CONTROL_APPROVAL_APPROVE_WITHOUT_DATELINE': {
            return t`Control Approved`;
        }
        case 'NIST_AI_TRAINING_RESET': {
            return t`AI Awareness Training Reset`;
        }
        case 'RISK_SETTINGS_UPDATED': {
            return t`Risk settings updated`;
        }
        case 'MAPPED_CONTROLS_RESET_ON_REQUIREMENT': {
            return t`Mapped controls reset on requirement`;
        }
        case 'MAPPED_TESTS_RESET_ON_CONTROL': {
            return t`Mapped tests reset on control`;
        }
        case 'VENDOR_RISK_REPORT_GENERATED': {
            return t`Risk report generated`;
        }
        case 'CONTROL_APPROVAL_SCHEDULED': {
            return t`Control Updated to Needs Approval`;
        }
        case 'CONTROL_APPROVAL_SCHEDULED_FAILED': {
            return t`Control Failed Update to Needs Approval`;
        }
        case 'MAPPED_REQUIREMENTS_RESET_ON_CONTROL': {
            return t`Mapped requirements reset on control`;
        }
        case 'SPECIAL_FORMER_PERSONNEL_ADDED': {
            return t`Former Personnel Added`;
        }
        case 'AP_INFRASTRUCTURE_AUTO_SCALING': {
            return t`Infrastructure Auto Scaling`;
        }
        case 'AP_INFRASTRUCTURE_BACKUP_RETENTION': {
            return t`Infrastructure Backup Retention`;
        }
        case 'AP_INFRASTRUCTURE_TDE_ENABLED': {
            return t`Infrastructure TDE Enabled`;
        }
        case 'AP_INFRASTRUCTURE_VERSIONING_ENABLED': {
            return t`Infrastructure Versioning Enabled`;
        }
        case 'AP_INFRASTRUCTURE_ZONE_REDUNDANCY': {
            return t`Infrastructure Zone Redundancy`;
        }
        case 'AP_INFRASTRUCTURE_LOGGING_ENABLED': {
            return t`Infrastructure Logging Enabled`;
        }
        case 'AP_INFRASTRUCTURE_TLS_VERSION': {
            return t`Infrastructure TLS Version`;
        }
        case 'AP_INFRASTRUCTURE_VPC_CONFIGURATION': {
            return t`Infrastructure VPC Configuration`;
        }
        case 'AP_INFRASTRUCTURE_ENCRYPTION_IN_TRANSIT': {
            return t`Infrastructure Encryption in Transit`;
        }
        case 'AP_INFRASTRUCTURE_SECURITY_GROUPS': {
            return t`Infrastructure Security Groups`;
        }
        case 'AP_INFRASTRUCTURE_BROAD_NETWORK_ACCESS_PATTERNS': {
            return t`Infrastructure Broad Network Access Patterns`;
        }
        case 'AP_INFRASTRUCTURE_PUBLIC_ACCESS_RESTRICTED': {
            return t`Infrastructure Public Access Restricted`;
        }
        case 'AP_INFRASTRUCTURE_DENY_BY_DEFAULT': {
            return t`Infrastructure Deny by Default`;
        }
        case 'AP_INFRASTRUCTURE_AUTOMATIC_SOFTWARE_UPDATES': {
            return t`Infrastructure Automatic Software Updates`;
        }
        case 'AP_INFRASTRUCTURE_LOG_INTEGRITY': {
            return t`Infrastructure Log Integrity`;
        }
        case 'AP_INFRASTRUCTURE_TLS_CIPHERS': {
            return t`Infrastructure TLS Ciphers`;
        }
        case 'AP_INFRASTRUCTURE_WAF_ENABLED': {
            return t`Infrastructure WAF Enabled`;
        }
        case 'AP_INFRASTRUCTURE_AUTOMATED_BACKUPS': {
            return t`Infrastructure Automated Backups`;
        }
        case 'AP_INFRASTRUCTURE_SECURE_API_VERSION': {
            return t`Infrastructure Secure API Version`;
        }
        case 'AP_INFRASTRUCTURE_LOG_RETENTION': {
            return t`Infrastructure Log Retention`;
        }
        case 'AP_INFRASTRUCTURE_DISABLE_DEFAULT_ACCOUNTS': {
            return t`Infrastructure Disable Default Accounts`;
        }
        case 'AP_INFRASTRUCTURE_BROAD_POLICY_ACCESS_PATTERNS': {
            return t`Infrastructure Broad Policy Access Patterns`;
        }
        case 'AP_INFRASTRUCTURE_RBAC': {
            return t`Infrastructure RBAC`;
        }
        case 'AP_INFRASTRUCTURE_RUNTIME_CONFIGURATION': {
            return t`Infrastructure Runtime Configuration`;
        }
        case 'AP_INFRASTRUCTURE_TAGGING': {
            return t`Infrastructure Tagging`;
        }
        case 'AP_INFRASTRUCTURE_DELETION_PROTECTION': {
            return t`Infrastructure Deletion Protection`;
        }
        case 'AP_INFRASTRUCTURE_DATA_RETENTION': {
            return t`Infrastructure Data Retention`;
        }
        case 'AP_INFRASTRUCTURE_KEY_ROTATION': {
            return t`Infrastructure Key Rotation`;
        }
        case 'AP_INFRASTRUCTURE_SECRET_ROTATION': {
            return t`Infrastructure Secret Rotation`;
        }
        case 'AP_INFRASTRUCTURE_AUTOMATIC_REPAIRS': {
            return t`Infrastructure Automatic Repairs`;
        }
        case 'AP_INFRASTRUCTURE_CONNECTION_DRAINING': {
            return t`Infrastructure Connection Draining`;
        }
        case 'AP_INFRASTRUCTURE_HIGH_AVAILABILITY': {
            return t`Infrastructure High Availability`;
        }
        case 'AP_INFRASTRUCTURE_POLICY_BASED_ACCESS_CONTROL': {
            return t`Infrastructure Policy Based Access Control`;
        }
        case 'AP_INFRASTRUCTURE_VPC_FLOW_LOGGING': {
            return t`Infrastructure VPC Flow Logging`;
        }
        case 'AP_INFRASTRUCTURE_IAM_ACCESS_KEY_ROTATION': {
            return t`Infrastructure IAM Access Key Rotation`;
        }
        case 'AP_INFRASTRUCTURE_SECURITY_GROUPS_HTTP_ACCESS_RESTRICTED': {
            return t`Infrastructure AWS Security Groups HTTP Access Restricted`;
        }
        case 'AP_INFRASTRUCTURE_S3_HTTP_REQUEST_DENIED': {
            return t`Infrastructure AWS S3 HTTP Request Denied`;
        }
        case 'AP_INFRASTRUCTURE_RDS_PUBLIC_ACCESS_RESTRICTED': {
            return t`Infrastructure RDS Public Access Restricted `;
        }
        case 'AP_INFRASTRUCTURE_IAM_UNUSED_CREDENTIALS': {
            return t`Infrastructure IAM Unused Credentials`;
        }
        case 'AP_INFRASTRUCTURE_CLOUD_TRAIL_LOGS_ENCRYPTED': {
            return t`Infrastructure Cloud Trail Logs Encrypted`;
        }
        case 'AP_INFRASTRUCTURE_SECURITY_GROUPS_RESTRICT_PUBLIC_RDP_ACCESS': {
            return t`Infrastructure Security Groups Restrict Public RDP Access`;
        }
        case 'AP_INFRASTRUCTURE_VPC_DEFAULT_SECURITY_GROUPS_RESTRICT_ALL_TRAFFIC': {
            return t`Infrastructure VPC Default Security Groups Restrict All Traffic`;
        }
        case 'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_UNHEALTHY_HOSTS_MONITORED': {
            return t`Infrastructure Application Load Balancer Unhealthy Hosts Monitored`;
        }
        case 'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_REDIRECTS_HTTP_TO_HTTPS': {
            return t`Infrastructure Application Load Balancer Redirects HTTP To HTTPS`;
        }
        case 'AP_INFRASTRUCTURE_CLASSIC_LOAD_BALANCER_LATENCY_MONITORED': {
            return t`Infrastructure Classic Load Balancer Latency Monitored`;
        }
        case 'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_SERVER_ERRORS_MONITORED': {
            return t`Infrastructure Application Load Balancer Server Errors Monitored`;
        }
        case 'AP_INFRASTRUCTURE_CLASSIC_LOAD_BALANCER_SERVER_ERRORS_MONITORED': {
            return t`Infrastructure Classic Load Balancer Server Errors Monitored`;
        }
        case 'AP_INFRASTRUCTURE_CLASSIC_LOAD_BALANCER_UNHEALTHY_HOSTS_MONITORED': {
            return t`Infrastructure Classic Load Balancer Unhealthy Hosts Monitored`;
        }
        case 'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_TARGET_RESPONSE_TIME_MONITORED': {
            return t`Infrastructure Application Load Balancer Target Response Time Monitored`;
        }
        case 'AP_INFRASTRUCTURE_LAMBDA_ERROR_RATE_MONITORED': {
            return t`Infrastructure Lambda Error Rate Monitored`;
        }
        case 'AP_INFRASTRUCTURE_DATABASE_WRITES_IO_MONITORED': {
            return t`Infrastructure Database write I/O Monitored`;
        }
        case 'AP_INFRASTRUCTURE_EC2_INSTANCES_IMDSV1_DISABLED': {
            return t`Infrastructure EC2 Instances IMDSv1 Disabled`;
        }
        case 'AP_INFRASTRUCTURE_DYNAMODB_POINT_IN_TIME_ENABLED': {
            return t`Infrastructure DynamoDB Point In Time`;
        }
        case 'AP_INFRASTRUCTURE_AUDIT_LOGS_ENABLED_FOR_EKS_CLUSTER': {
            return t`Infrastructure Audit Logs Enabled For EKS Cluster`;
        }
        case 'AP_INFRASTRUCTURE_PUBLIC_ACCESS_TO_CLUSTERS_CONTROL_PLANE_IS_RESTRICTED': {
            return t`Infrastructure Public Access To Clusters Control Plane Is Restricted`;
        }
        case 'AP_INFRASTRUCTURE_PRIVATE_ACCESS_TO_CLUSTERS_CONTROL_PLANE_IS_RESTRICTED': {
            return t`Infrastructure Private Access To Clusters Control Plane Is Restricted`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_CREATE_POLICY_ASSIGNMENT': {
            return t`Infrastructure Log Alert For Create Policy Assignment`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_MANAGE_PUBLIC_IP_ADDRESS_RULE': {
            return t`Infrastructure Log Alert For Create or Update a Public IP Address Rule`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_POLICY_ASSIGNMENT': {
            return t`Infrastructure Log Alert For Delete Policy Assignment`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_PUBLIC_IP_ADDRESS': {
            return t`Infrastructure Log Alert For Delete Public IP Address`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_MANAGE_NETWORK_SECURITY_GROUP': {
            return t`Infrastructure Log Alert For Manage Network Security Group`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_NETWORK_SECURITY_GROUP': {
            return t`Infrastructure Log Alert For Delete Network Security Group`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_MANAGE_SECURITY_SOLUTION': {
            return t`Infrastructure Log Alert For Manage Security Solution`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_SECURITY_SOLUTION': {
            return t`Infrastructure Log Alert For Delete Security Solution`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_CREATE_OR_UPDATE_SQL_SERVER_FIREWALL_RULE': {
            return t`Infrastructure Log Alert For Create or Update SQL Server Firewall Rule`;
        }
        case 'AP_INFRASTRUCTURE_NSG_SSH_PUBLIC_ACCESS_RESTRICTED': {
            return t`Infrastructure Network Security Groups SSH Public Access Restricted`;
        }
        case 'AP_INFRASTRUCTURE_WEB_APP_REDIRECTS_HTTP_TRAFFIC_TO_HTTPS': {
            return t`Infrastructure Web App Redirects HTTP Traffic To HTTPS`;
        }
        case 'CUSTOM_FIELD_CREATED': {
            return t`Custom Field Created`;
        }
        case 'CUSTOM_FIELD_UPDATED': {
            return t`Custom Field Updated`;
        }
        case 'CUSTOM_FIELD_PLACED': {
            return t`Custom Field Placed`;
        }
        case 'CUSTOM_FIELD_UNPLACED': {
            return t`Custom Field Unplaced`;
        }
        case 'CUSTOM_FIELD_DELETED': {
            return t`Custom Field Deleted`;
        }
        case 'CUSTOM_FIELD_SECTION_UPDATED': {
            return t`Custom Field Section Updated`;
        }
        case 'CUSTOM_FORMULA_CREATED': {
            return t`Custom Formula Created`;
        }
        case 'CUSTOM_FORMULA_UPDATED': {
            return t`Custom Formula Updated`;
        }
        case 'CUSTOM_FORMULA_DELETED': {
            return t`Custom Formula Deleted`;
        }
        case 'AUTO_RESET_SETTING_UPDATED': {
            return t`Auto Reset Setting Updated`;
        }
        case 'COMPANY_HIPAA_TRAINING_DATA_UPDATED': {
            return t`HIPAA Configuration Updated`;
        }
        case 'TICKET_DOWNLOAD': {
            return t`Ticket Downloaded`;
        }
        case 'PROVIDER_CONNECTION_ERROR': {
            return t`Provider Connection Error`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED': {
            return t`Autopilot Critical Vulnerability Addressed (AWS Inspector)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED': {
            return t`Autopilot High Vulnerability Addressed (AWS Inspector)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_SNYK': {
            return t`Autopilot Critical Vulnerability Addressed (Snyk)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_SNYK': {
            return t`Autopilot High Vulnerability Addressed (Snyk)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_TENABLE_VULNERABILITY_MANAGEMENT': {
            return t`Autopilot Critical Vulnerability Addressed (Tenable Vulnerability Management)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_TENABLE_VULNERABILITY_MANAGEMENT': {
            return t`Autopilot High Vulnerability Addressed (Tenable Vulnerability Management)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_QUALYS': {
            return t`Autopilot Critical Vulnerability Addressed (Qualys)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_QUALYS': {
            return t`Autopilot High Vulnerability Addressed (Qualys)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_SEMGREP': {
            return t`Autopilot Critical Vulnerability Addressed (Semgrep)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_SEMGREP': {
            return t`Autopilot High Vulnerability Addressed (Semgrep)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_SENTINELONE_SINGULARITY_VULNERABILITY_MANAGEMENT': {
            return t`Autopilot Critical Vulnerability Addressed (SentinelOne Singularity Vulnerability Management)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_SENTINELONE_SINGULARITY_VULNERABILITY_MANAGEMENT': {
            return t`Autopilot High Vulnerability Addressed (SentinelOne Singularity Vulnerability Management)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_CROWDSTRIKE_FALCON_EXPOSURE_MANAGEMENT': {
            return t`Autopilot Critical Vulnerability Addressed (CrowdStrike Falcon Exposure Management)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_CROWDSTRIKE_FALCON_EXPOSURE_MANAGEMENT': {
            return t`Autopilot High Vulnerability Addressed (CrowdStrike Falcon Exposure Management)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_MS_DEFENDER_VMS': {
            return t`Autopilot Critical Vulnerability Addressed (Microsoft Defender)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_MS_DEFENDER_VMS': {
            return t`Autopilot High Vulnerability Addressed (Microsoft Defender)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_RAPID7_VMS': {
            return t`Autopilot Critical Vulnerability Addressed (Rapid7)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_RAPID7_VMS': {
            return t`Autopilot High Vulnerability Addressed (Rapid7)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_ARNICA': {
            return t`Autopilot Critical Vulnerability Addressed (Arnica)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_ARNICA': {
            return t`Autopilot High Vulnerability Addressed (Arnica)`;
        }
        case 'EXCEPTION_WORKFLOW_DELETED': {
            return t`Exception Workflow Deleted`;
        }
        case 'EXCEPTION_WORKFLOW_CREATED': {
            return t`Exception Workflow Created`;
        }
        case 'EXCEPTION_WORKFLOW_UPDATED': {
            return t`Exception Workflow Updated`;
        }
        case 'AP_INFRASTRUCTURE_STORAGE_ACCOUNTS_ACCESSED_VIA_PRIVATE_ENDPOINTS': {
            return t`Infrastructure Storage Accounts Accessed Via Private Endpoints`;
        }
        case 'EXCEPTION_REQUEST_CREATED': {
            return t`Exception Request Created`;
        }
        case 'CUSTOM_RESOURCE_RECORD_CREATED': {
            return t`Custom Resource Created`;
        }
        case 'CUSTOM_RESOURCE_RECORD_REMOVED': {
            return t`Custom Resource Removed`;
        }
        case 'CUSTOM_RESOURCE_RECORD_UPDATED': {
            return t`Custom Resource Updated`;
        }
        case 'WORKFLOW_PUBLISHED': {
            return t`Workflow Published`;
        }
        case 'WORKFLOW_UNPUBLISHED': {
            return t`Workflow Unpublished`;
        }
        case 'WORKFLOW_DELETED': {
            return t`Workflow Deleted`;
        }
        case 'WORKFLOW_ARCHIVED': {
            return t`Workflow Archived`;
        }
        case 'WORKFLOW_RESTORED': {
            return t`Workflow Restored`;
        }
        case 'WORKFLOW_DRAFT_SAVED': {
            return t`Workflow draft saved`;
        }
        case 'NOTIFICATION_WORKFLOW_CREATED': {
            return t`Workflow Notification Created`;
        }
        case 'CUSTOM_DATA_RECORD_REMOVED': {
            return t`Record Removed from Custom Connection`;
        }
        case 'AP_INFRASTRUCTURE_SQL_SERVERS_AUDITING': {
            return t`Infrastructure SQL Servers Auditing`;
        }
        case 'AP_INFRASTRUCTURE_STORAGE_ACCOUNTS_SECURE_TLS_CONFIGURATION': {
            return t`Infrastructure Storage Accounts Secure TLS Configuration`;
        }
        case 'ENTITLEMENT_ENABLED': {
            return t`Feature enabled`;
        }
        case 'ENTITLEMENT_DISABLED': {
            return t`Feature disabled`;
        }
        case 'AP_INFRASTRUCTURE_POSTGRESQL_DATABASE_SERVER_LOG_CHECKPOINTS': {
            return t`Infrastructure PostgreSQL Database Server Log Checkpoints`;
        }
        case 'NEW_REPORT': {
            return t`New Report`;
        }
        case 'VENDOR_UPDATED': {
            return t`Vendor Updated`;
        }
        case 'COMPANY_SECURITY_REPORT_SHAREABLE_UPDATED': {
            return t`Security Report Shareable Updated`;
        }
        case 'COMPANY_SECURITY_REPORT_SHARE_TOKEN_UPDATED': {
            return t`Security Report Share Token Updated`;
        }
        case 'USER_IDENTITY_OBSERVABILITY_TOGGLE_UPDATED': {
            return t`Observability Toggle Updated`;
        }
        case 'BACKGROUND_CHECK_CANCELED': {
            return t`Background Check Canceled`;
        }
        case 'PERSONNEL_BG_CHECKS_REVOKED': {
            return t`Personnel Background Checks Revoked`;
        }
        case 'VULNERABILITY_FINDINGS_SYNCED': {
            return t`Vulnerability Findings Synced`;
        }
        case 'VULNERABILITY_FINDING_CREATED': {
            return t`Vulnerability Finding Created`;
        }
        case 'VULNERABILITY_FINDING_UPDATED': {
            return t`Vulnerability Finding Updated`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_AIKIDO': {
            return t`Autopilot vulnerability critical addressed (Aikido)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_MS_DEFENDER': {
            return t`Autopilot vulnerability critical addressed (Microsoft Defender)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_SENTINEL_ONE_VMS': {
            return t`Autopilot vulnerability critical addressed (SentinelOne VMS)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_WIZ_CODE': {
            return t`Autopilot vulnerability critical addressed (Wiz Code)`;
        }
        case 'AP_VULNERABILITY_CRITICAL_ADDRESSED_WIZ_VMS': {
            return t`Autopilot vulnerability critical addressed (Wiz VMS)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_AIKIDO': {
            return t`Autopilot vulnerability high addressed (Aikido)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_MS_DEFENDER': {
            return t`Autopilot vulnerability high addressed (Microsoft Defender)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_SENTINEL_ONE_VMS': {
            return t`Autopilot vulnerability high addressed (SentinelOne VMS)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_WIZ_CODE': {
            return t`Autopilot vulnerability high addressed (Wiz Code)`;
        }
        case 'AP_VULNERABILITY_HIGH_ADDRESSED_WIZ_VMS': {
            return t`Autopilot vulnerability high addressed (Wiz VMS)`;
        }
        case 'AP_INFRASTRUCTURE_KEY_VAULTS_KEY_EXPIRATION': {
            return t`Infrastructure Key Vaults Key Expiration`;
        }
        case 'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_SQL_SERVER_FIREWALL_RULE': {
            return t`Infrastructure Log Alert For Delete SQL Server Firewall Rule`;
        }
        case 'AP_INFRASTRUCTURE_SQL_DATA_ENCRYPTION': {
            return t`Infrastructure SQL Data Encryption`;
        }
        case 'ARTIFACT_BULK_DELETED': {
            return t`Artifact Bulk Deleted`;
        }
        case 'ASSET_OWNER_UPDATED': {
            return t`Asset Owner Updated`;
        }
        case 'AUDITHUB_REQUEST_MESSAGE_DELETED': {
            return t`AuditHub Request Message Deleted`;
        }
        case 'AUTOPILOT_PERSONNEL_EXCLUSION_ARCHIVED': {
            return t`Autopilot Personnel Exclusion Archived`;
        }
        case 'CAC_CODE_REVIEW_REQUESTED': {
            return t`CAC Code Review Requested`;
        }
        case 'CODE_REPOSITORY_ADDED': {
            return t`Code Repository Added`;
        }
        case 'CODE_REPOSITORY_REMOVED': {
            return t`Code Repository Removed`;
        }
        case 'CODE_REPOSITORY_SETTINGS_UPDATED': {
            return t`Code Repository Settings Updated`;
        }
        case 'CONTROL_INFORMATION_UNLINKED_FROM_OTHER_WORKSPACES': {
            return t`Control Information Unlinked From Other Workspaces`;
        }
        case 'CONTROL_TASK_RECURRING_DELETED': {
            return t`Control Task Recurring Deleted`;
        }
        case 'CONTROLS_ARCHIVED': {
            return t`Controls Archived`;
        }
        case 'CONTROLS_UNARCHIVED': {
            return t`Controls Unarchived`;
        }
        case 'CONTROLS_DISABLED': {
            return t`Controls Disabled`;
        }
        case 'CUSTOM_FORMULA_CALCULATED': {
            return t`Custom Formula Calculated`;
        }
        case 'CUSTOMER_REQUEST_DETAILS_EDITED_MULTIPLE_WORKSPACES': {
            return t`Customer Request Details Edited Multiple Workspaces`;
        }
        case 'DEVICE_FAILED': {
            return t`Device Failed`;
        }
        case 'EVIDENCE_BULK_OWNER_UPDATE': {
            return t`Evidence Bulk Owner Update`;
        }
        case 'FRAMEWORK_AUDIT_DETAILS_EDITED_MULTIPLE_WORKSPACES': {
            return t`Framework Audit Details Edited Multiple Workspaces`;
        }
        case 'FRAMEWORK_DISABLED': {
            return t`Framework Disabled`;
        }
        case 'FRAMEWORK_ENABLED': {
            return t`Framework Enabled`;
        }
        case 'GENERAL_TASK_RECURRING_DELETED': {
            return t`General Task Recurring Deleted`;
        }
        case 'MAPPED_POLICIES_RESET_ON_CONTROL': {
            return t`Mapped Policies Reset On Control`;
        }
        case 'NON_DISCLOSURE': {
            return t`Non Disclosure`;
        }
        case 'POLICY_DELETED': {
            return t`Policy Deleted`;
        }
        case 'POLICY_OWNER_UPDATED': {
            return t`Policy Owner Updated`;
        }
        case 'POLICY_RENEW_WITHOUT_UPDATE': {
            return t`Policy Renew Without Update`;
        }
        case 'POLICY_TEMPLATE_DOWNLOADED': {
            return t`Policy Template Downloaded`;
        }
        case 'POLICY_VERSION_APPROVAL_SETTINGS_UPDATED': {
            return t`Policy Version Approval Settings Updated`;
        }
        case 'POLICY_VERSION_APPROVED_BY_APPROVER': {
            return t`Policy Version Approved By Approver`;
        }
        case 'POLICY_VERSION_DRAFT_FINALIZED': {
            return t`Policy Version Draft Finalized`;
        }
        case 'POLICY_VERSION_MODIFIED_NO_REQUIRED_APPROVAL': {
            return t`Policy Version Modified No Required Approval`;
        }
        case 'POLICY_VERSION_MODIFIED_RESTARTED_APPROVAL': {
            return t`Policy Version Modified Restarted Approval`;
        }
        case 'POLICY_VERSION_REQUEST_CHANGES': {
            return t`Policy Version Request Changes`;
        }
        case 'PRE_APPROVED_EMAIL_ADDED': {
            return t`Pre Approved Email Added`;
        }
        case 'PRE_APPROVED_EMAIL_REMOVED': {
            return t`Pre Approved Email Removed`;
        }
        case 'REQUEST_MESSAGE_SENT': {
            return t`Request Message Sent`;
        }
        case 'REQUEST_STATUS_CHANGED': {
            return t`Request Status Changed`;
        }
        case 'RESENT_EVIDENCE_SAMPLE_DOWNLOAD_EMAIL_MULTIPLE_WORKSPACE': {
            return t`Resent Evidence Sample Download Email Multiple Workspace`;
        }
        case 'REVOKE_REQUEST': {
            return t`Revoke Request`;
        }
        case 'RISK_TASK_RECURRING_DELETED': {
            return t`Risk Task Recurring Deleted`;
        }
        case 'TASK_RECURRING_DELETED': {
            return t`Task Recurring Deleted`;
        }
        case 'TRUST_CONTENT_SAVED': {
            return t`Trust Content Saved`;
        }
        case 'USER_IDENTITY_ACCESS_REVIEW_LINK_UPDATED': {
            return t`User Identity Access Review Link Updated`;
        }
        case 'VULNERABILITY_SLA_SETTING_CREATED': {
            return t`Vulnerability SLA Setting Created`;
        }
        case 'VULNERABILITY_SLA_SETTING_UPDATED': {
            return t`Vulnerability SLA Setting Updated`;
        }
        case 'WIZARD_STEP_SAVED': {
            return t`Wizard Step Saved`;
        }
        case 'WORKFLOW_NOTIFICATION_SENT': {
            return t`Workflow Notification Sent`;
        }
        case 'WORKFLOW_WEBHOOK_SENT': {
            return t`Workflow Webhook Sent`;
        }
        case 'WORKSPACE_DELETED': {
            return t`Workspace Deleted`;
        }
        case 'FRAMEWORK_LEVEL_IMPACT_UPDATED': {
            return t`Framework Level Impact Updated`;
        }
        case 'POLICY_VERSION_APPROVED_BY_APPROVER_TIER': {
            return t`Policy approved`;
        }
        case 'POLICY_VERSION_APPROVAL_OVERRIDE': {
            return t`Policy Version Approval Override`;
        }
        case 'VENDOR_SECURITY_REVIEW_STARTED': {
            return t`Vendor Security Review Created`;
        }
        case 'VENDOR_SECURITY_REVIEW_COMPLETED': {
            return t`Vendor Security Review Completed`;
        }
        case 'VENDOR_SECURITY_REVIEW_APPROVAL_STATUS_CHANGED': {
            return t`Vendor Security Review Approval Status Changed`;
        }
        case 'VENDOR_SOC_REPORT_REVIEW_STARTED': {
            return t`Vendor SOC Report Review Created`;
        }
        case 'VENDOR_SOC_REPORT_REVIEW_COMPLETED': {
            return t`Vendor SOC Report Review Completed`;
        }
        case 'VENDOR_SOC_REPORT_REVIEW_APPROVAL_STATUS_CHANGED': {
            return t`Vendor SOC Report Review Approval Status Changed`;
        }
        case 'POLICY_VERSION_UPLOADED': {
            return t`Policy Version Uploaded`;
        }
        case 'SAFEBASE_SYNC_PENDING': {
            return t`SafeBase synchronization pending`;
        }
        case 'SAFEBASE_SYNC_ERROR': {
            return t`SafeBase synchronization error`;
        }
        case 'SAFEBASE_SYNC_COMPLETE': {
            return t`SafeBase synchronization complete`;
        }
        case 'SAFEBASE_MIGRATION_PENDING': {
            return t`SafeBase migration in progress`;
        }
        case 'SAFEBASE_MIGRATION_MIGRATED': {
            return t`SafeBase data migrated`;
        }
        case 'SAFEBASE_MIGRATION_ERROR': {
            return t`SafeBase migration error`;
        }
        case 'SAFEBASE_MIGRATION_COMPLETE': {
            return t`SafeBase migration complete`;
        }
        case 'TRUST_CENTER_INGRESS_DENIED': {
            return t`Trust Center access denied`;
        }
        case 'CONNECTION_IDP_RANKING_UPDATED': {
            return t`Identity Provider ranking updated`;
        }
        case 'MONITOR_FINDINGS_ZIP_GENERATED': {
            return t`Monitor findings ZIP generated`;
        }
        case 'MONITOR_FINDINGS_CSV_GENERATED': {
            return t`Monitor findings CSV generated`;
        }
        default: {
            return type;
        }
    }
};

export const getEventTypeOptions = (): ListBoxItemData[] => {
    return EVENT_TYPES.map((type) => {
        return {
            id: type,
            label: getEventTypeLabel(type),
            value: type,
        };
    });
};
