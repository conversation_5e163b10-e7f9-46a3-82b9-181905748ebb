import type { EventDetailsResponseDto } from '@globals/api-sdk/types';

// Helper functions have been moved to events.helpers.ts
// This file now only contains the EVENT_TYPES constant

export const EVENT_TYPES: EventDetailsResponseDto['type'][] = [
    'ACCESS_REVIEW_APPLICATION_COMPLETED',
    'ACCESS_REVIEW_APPLICATION_REVIEWER_NOTIFIED',
    'ACCESS_REVIEW_APPLICATION_REVIEWER_UPDATED',
    'ACCESS_REVIEW_MANUAL_EVIDENCE_UPLOADED',
    'ACCESS_REVIEW_PERIOD_COMPLETED',
    'ACCESS_REVIEW_PERIOD_CREATED',
    'ACCESS_REVIEW_PERIOD_IN_PROGRESS',
    'ACCESS_REVIEW_TICKET_LINKED_TO_PERSONNEL',
    'ACCOUNT_INFORMATION_PACKAGE_CREATION',
    'AGENT_UNREGISTERED',
    'AGENT_USER_DATA_UPDATED',
    'AP_AGENT_ANTI_VIRUS_APPLICATION',
    'AP_AGENT_AUTO_UPDATES_ENABLED',
    'AP_AGENT_PASSWORD_MANAGER',
    'AP_AUTO_SCALE_SERVER_INSTANCES',
    'AP_CAPACITY_AND_USAGE_MONITORING',
    'AP_COMPANY_BOD_UNIQUE',
    'AP_COMPANY_INFRASTRUCTURE_MFA_ENABLED',
    'AP_COMPANY_INFRASTRUCTURE_SSL_ENABLED',
    'AP_COMPANY_POLICIES_APPROVED',
    'AP_COMPANY_POLICIES_CREATED',
    'AP_COMPANY_PRIORITIZE_SECURITY_ISSUES',
    'AP_COMPANY_SECURITY_COMMITTEE',
    'AP_COMPANY_WEBSITE_SSL_CERT_NOT_EXPIRED',
    'AP_COMPANY_WEBSITE_SSL_CERT_STRONG_CIPHERS',
    'AP_COMPANY_WEBSITE_SSL_ENFORCED',
    'AP_COMPANY_WEBSITE_SSL_HAS_KNOWN_ISSUES',
    'AP_CONTRACTORS_ACCEPTABLE_USE_POLICY',
    'AP_CONTRACTORS_BACKGROUND_CHECK_COMPLIANCE',
    'AP_CONTRACTORS_CODE_OF_CONDUCT_POLICY',
    'AP_CONTRACTORS_DATA_PROTECTION_POLICY',
    'AP_CONTRACTORS_POLICIES_APPROVED',
    'AP_CUSTOM_TEST',
    'AP_DRAFT_TEST',
    'AP_EMPLOYEES_ACCEPTABLE_USE_POLICY',
    'AP_EMPLOYEES_BACKGROUND_CHECK_COMPLIANCE',
    'AP_EMPLOYEES_CODE_OF_CONDUCT_POLICY',
    'AP_EMPLOYEES_DATA_PROTECTION_POLICY',
    'AP_EMPLOYEES_HARD_DRIVE_ENCRYPTED_COMPLIANCE',
    'AP_EMPLOYEES_LOCK_SCREEN_COMPLIANCE',
    'AP_EMPLOYEES_POLICIES_APPROVED',
    'AP_EMPLOYEES_SECURITY_COMPLIANCE',
    'AP_ENCRYPTION_IN_TRANSIT',
    'AP_EXCESSIVE_PRIVILEGES_ASSIGNED',
    'AP_EXTERNAL_EXPOSURE_OF_CLOUD_RESOURCES',
    'AP_FORMER_PERSONNEL_OFFBOARDED',
    'AP_IDENTITY_PROVIDER_MFA_ENABLED',
    'AP_IDENTITY_PROVIDER_UNKNOWN_EMAIL',
    'AP_INFRASTRUCTURE_ACLS_PUBLIC_REMOTE_ADMIN_ACCESS_RESTRICTED',
    'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_REDIRECTS_HTTP_TO_HTTPS',
    'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_SERVER_ERRORS_MONITORED',
    'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_TARGET_RESPONSE_TIME_MONITORED',
    'AP_INFRASTRUCTURE_APPLICATION_LOAD_BALANCER_UNHEALTHY_HOSTS_MONITORED',
    'AP_INFRASTRUCTURE_ASSETS_SYNCED',
    'AP_INFRASTRUCTURE_AUDIT_LOGS_ENABLED_FOR_EKS_CLUSTER',
    'AP_INFRASTRUCTURE_AUTHORIZATION_DETAILS',
    'AP_INFRASTRUCTURE_AUTO_SCALING',
    'AP_INFRASTRUCTURE_AUTOMATED_BACKUPS',
    'AP_INFRASTRUCTURE_AUTOMATIC_REPAIRS',
    'AP_INFRASTRUCTURE_AUTOMATIC_SOFTWARE_UPDATES',
    'AP_INFRASTRUCTURE_BACKUP_RETENTION',
    'AP_INFRASTRUCTURE_BROAD_NETWORK_ACCESS_PATTERNS',
    'AP_INFRASTRUCTURE_BROAD_POLICY_ACCESS_PATTERNS',
    'AP_INFRASTRUCTURE_BUCKET_ACCESS_LOGGING',
    'AP_INFRASTRUCTURE_CLASSIC_LOAD_BALANCER_LATENCY_MONITORED',
    'AP_INFRASTRUCTURE_CLASSIC_LOAD_BALANCER_SERVER_ERRORS_MONITORED',
    'AP_INFRASTRUCTURE_CLASSIC_LOAD_BALANCER_UNHEALTHY_HOSTS_MONITORED',
    'AP_INFRASTRUCTURE_CLOUD_DATA_FREE_STORAGE',
    'AP_INFRASTRUCTURE_CLOUD_DATA_MONITOR_CPU_LOAD',
    'AP_INFRASTRUCTURE_CLOUD_TRAIL_LOGS_ENCRYPTED',
    'AP_INFRASTRUCTURE_CLOUDTRAIL_LOG_FILE_INTEGRITY',
    'AP_INFRASTRUCTURE_CMK_ROTATION',
    'AP_INFRASTRUCTURE_CONNECTION_DRAINING',
    'AP_INFRASTRUCTURE_CREDENTIAL_REPORT',
    'AP_INFRASTRUCTURE_DAILY_BACKUP_JOB_STATUS_MONITORED',
    'AP_INFRASTRUCTURE_DATA_BACKUPS',
    'AP_INFRASTRUCTURE_DATA_ENCRYPTED',
    'AP_INFRASTRUCTURE_DATA_MONITOR_CPU',
    'AP_INFRASTRUCTURE_DATA_MONITOR_FREE_STORAGE',
    'AP_INFRASTRUCTURE_DATA_MONITOR_READ_IO',
    'AP_INFRASTRUCTURE_DATA_MONITOR_WRITE_IO',
    'AP_INFRASTRUCTURE_DATA_MULTI_AZ',
    'AP_INFRASTRUCTURE_DATA_RETENTION',
    'AP_INFRASTRUCTURE_DATABASE_WRITES_IO_MONITORED',
    'AP_INFRASTRUCTURE_DELETION_PROTECTION',
    'AP_INFRASTRUCTURE_DENY_BY_DEFAULT',
    'AP_INFRASTRUCTURE_DISABLE_DEFAULT_ACCOUNTS',
    'AP_INFRASTRUCTURE_DISALLOWS_TRAFFIC',
    'AP_INFRASTRUCTURE_DYNAMODB_POINT_IN_TIME_ENABLED',
    'AP_INFRASTRUCTURE_EBS_VOLUME_ENCRYPTION',
    'AP_INFRASTRUCTURE_EC2_INSTANCES_IMDSV1_DISABLED',
    'AP_INFRASTRUCTURE_EFS_ENCRYPTED_AT_REST',
    'AP_INFRASTRUCTURE_ENCRYPTION_IN_TRANSIT',
    'AP_INFRASTRUCTURE_FAILED_BACKUP_ALERTS',
    'AP_INFRASTRUCTURE_FAILED_BACKUPS_ADDRESSED_IN_TIMELY_MANNER',
    'AP_INFRASTRUCTURE_HARDWARE_MFA_FOR_ROOT_ACCOUNT',
    'AP_INFRASTRUCTURE_HAS_BALANCERS',
    'AP_INFRASTRUCTURE_HAS_WAF',
    'AP_INFRASTRUCTURE_HIGH_AVAILABILITY',
    'AP_INFRASTRUCTURE_IAM_ACCESS_KEY_ROTATION',
    'AP_INFRASTRUCTURE_IAM_GROUP_BASED_ACCESS_CONTROL',
    'AP_INFRASTRUCTURE_IAM_PASSWORD_MINIMUM_LENGTH',
    'AP_INFRASTRUCTURE_IAM_PASSWORD_REUSE',
    'AP_INFRASTRUCTURE_IAM_PRINCIPLE_OF_LEAST_PRIVILEGE',
    'AP_INFRASTRUCTURE_IAM_UNUSED_CREDENTIALS',
    'AP_INFRASTRUCTURE_INSTANCE_MONITOR_CPU',
    'AP_INFRASTRUCTURE_KEY_ROTATION',
    'AP_INFRASTRUCTURE_KEY_VAULTS_KEY_EXPIRATION',
    'AP_INFRASTRUCTURE_LAMBDA_ERROR_RATE_MONITORED',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_CREATE_OR_UPDATE_SQL_SERVER_FIREWALL_RULE',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_CREATE_POLICY_ASSIGNMENT',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_NETWORK_SECURITY_GROUP',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_POLICY_ASSIGNMENT',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_PUBLIC_IP_ADDRESS',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_SECURITY_SOLUTION',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_DELETE_SQL_SERVER_FIREWALL_RULE',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_MANAGE_NETWORK_SECURITY_GROUP',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_MANAGE_PUBLIC_IP_ADDRESS_RULE',
    'AP_INFRASTRUCTURE_LOG_ALERT_FOR_MANAGE_SECURITY_SOLUTION',
    'AP_INFRASTRUCTURE_LOG_INTEGRITY',
    'AP_INFRASTRUCTURE_LOG_RETENTION',
    'AP_INFRASTRUCTURE_LOGGING_ENABLED',
    'AP_INFRASTRUCTURE_MFA_FOR_ROOT_ACCOUNT',
    'AP_INFRASTRUCTURE_NSG_SSH_PUBLIC_ACCESS_RESTRICTED',
    'AP_INFRASTRUCTURE_POLICY_BASED_ACCESS_CONTROL',
    'AP_INFRASTRUCTURE_POSTGRESQL_DATABASE_SERVER_LOG_CHECKPOINTS',
    'AP_INFRASTRUCTURE_PRIVATE_ACCESS_TO_CLUSTERS_CONTROL_PLANE_IS_RESTRICTED',
    'AP_INFRASTRUCTURE_PUBLIC_ACCESS_RESTRICTED',
    'AP_INFRASTRUCTURE_PUBLIC_ACCESS_TO_CLUSTERS_CONTROL_PLANE_IS_RESTRICTED',
    'AP_INFRASTRUCTURE_PUBLIC_SSH_DENIED',
    'AP_INFRASTRUCTURE_QUEUE_MONITOR_MESSAGE_AGE',
    'AP_INFRASTRUCTURE_RBAC',
    'AP_INFRASTRUCTURE_RDS_MINIMUM_VERSION_UPGRADE',
    'AP_INFRASTRUCTURE_RDS_PUBLIC_ACCESS_RESTRICTED',
    'AP_INFRASTRUCTURE_ROOT_ACCOUNT_UNUSED',
    'AP_INFRASTRUCTURE_RUNTIME_CONFIGURATION',
    'AP_INFRASTRUCTURE_S3_HTTP_REQUEST_DENIED',
    'AP_INFRASTRUCTURE_S3_OBJECT_LEVEL_LOGGING_FOR_RW_EVENTS',
    'AP_INFRASTRUCTURE_SECRET_ROTATION',
    'AP_INFRASTRUCTURE_SECURE_API_VERSION',
    'AP_INFRASTRUCTURE_SECURITY_GROUPS',
    'AP_INFRASTRUCTURE_SECURITY_GROUPS_HTTP_ACCESS_RESTRICTED',
    'AP_INFRASTRUCTURE_SECURITY_GROUPS_RESTRICT_PUBLIC_RDP_ACCESS',
    'AP_INFRASTRUCTURE_SQL_DATA_ENCRYPTION',
    'AP_INFRASTRUCTURE_SQL_FREEABLE_MEMORY_MONITORED',
    'AP_INFRASTRUCTURE_SQL_SERVERS_AUDITING',
    'AP_INFRASTRUCTURE_STORAGE_ACCOUNTS_ACCESSED_VIA_PRIVATE_ENDPOINTS',
    'AP_INFRASTRUCTURE_STORAGE_ACCOUNTS_SECURE_TLS_CONFIGURATION',
    'AP_INFRASTRUCTURE_STORAGE_ENCRYPTED',
    'AP_INFRASTRUCTURE_STORAGE_RESTRICTED',
    'AP_INFRASTRUCTURE_STORAGE_VERSIONING',
    'AP_INFRASTRUCTURE_STORAGE_WAF',
    'AP_INFRASTRUCTURE_TAGGING',
    'AP_INFRASTRUCTURE_TDE_ENABLED',
    'AP_INFRASTRUCTURE_TLS_CIPHERS',
    'AP_INFRASTRUCTURE_TLS_VERSION',
    'AP_INFRASTRUCTURE_VERSIONING_ENABLED',
    'AP_INFRASTRUCTURE_VPC_CONFIGURATION',
    'AP_INFRASTRUCTURE_VPC_DEFAULT_SECURITY_GROUPS_RESTRICT_ALL_TRAFFIC',
    'AP_INFRASTRUCTURE_VPC_FLOW_LOGGING',
    'AP_INFRASTRUCTURE_WAF_ENABLED',
    'AP_INFRASTRUCTURE_WEB_APP_REDIRECTS_HTTP_TRAFFIC_TO_HTTPS',
    'AP_INFRASTRUCTURE_ZONE_REDUNDANCY',
    'AP_INTERNAL_DOCUMENT_EMPLOYEE_AGREEMENT',
    'AP_INTERNAL_DOCUMENT_ENGINEERING_JOB_DESCRIPTION',
    'AP_INTERNAL_DOCUMENT_MSA',
    'AP_INTERNAL_DOCUMENT_ORGANIZATIONAL_CHART',
    'AP_INTERNAL_DOCUMENT_PERFORMANCE_EVALUATION',
    'AP_INTERNAL_INFRASTRUCTURE_IDENTITIES_REMOVED',
    'AP_INTERNAL_INFRASTRUCTURE_IDENTITIES_UNIQUE',
    'AP_INTERNAL_INFRASTRUCTURE_LINKED',
    'AP_INTERNAL_POLICY_ACCEPTABLE_USE',
    'AP_INTERNAL_POLICY_ACCESS_CONTROL',
    'AP_INTERNAL_POLICY_BACKUPS',
    'AP_INTERNAL_POLICY_CLEAN_DESK',
    'AP_INTERNAL_POLICY_CODE_OF_CONDUCT',
    'AP_INTERNAL_POLICY_CUSTOMER_DATA_RETENTION',
    'AP_INTERNAL_POLICY_DATA_CLASSIFICATION',
    'AP_INTERNAL_POLICY_DATA_PROTECTION',
    'AP_INTERNAL_POLICY_DATA_RETENTION',
    'AP_INTERNAL_POLICY_DISASTER_RECOVERY',
    'AP_INTERNAL_POLICY_EMPLOYEE_ACCESS_TO_CUSTOMER_DATA',
    'AP_INTERNAL_POLICY_EMPLOYEE_CONFIDENTIALITY',
    'AP_INTERNAL_POLICY_ENCRYPTION',
    'AP_INTERNAL_POLICY_INCIDENT_RESPONSE',
    'AP_INTERNAL_POLICY_INCIDENT_RESPONSE_FOLLOW_UPS',
    'AP_INTERNAL_POLICY_INCIDENT_RESPONSE_LESSONS_LEARNED',
    'AP_INTERNAL_POLICY_INCIDENT_RESPONSE_TEAM',
    'AP_INTERNAL_POLICY_INFORMATION_SECURITY',
    'AP_INTERNAL_POLICY_PASSWORD',
    'AP_INTERNAL_POLICY_PASSWORD_MANAGER_REQUIRED',
    'AP_INTERNAL_POLICY_PHYSICAL_SECURITY',
    'AP_INTERNAL_POLICY_RESPONSIBLE_DISCLOSURE',
    'AP_INTERNAL_POLICY_RISK_ASSESSMENT',
    'AP_INTERNAL_POLICY_RISK_REMEDIATION',
    'AP_INTERNAL_POLICY_SDLC',
    'AP_INTERNAL_POLICY_SECURITY_AWARENESS',
    'AP_INTERNAL_POLICY_SECURITY_ENCRYPTION',
    'AP_INTERNAL_POLICY_SENSITIVE_DATA_DISPOSAL',
    'AP_INTERNAL_POLICY_SYSTEM_ACCESS_CONTROL',
    'AP_INTERNAL_POLICY_VULNERABILITY_MANAGEMENT',
    'AP_INTERNAL_REPORT_ARCHITECTURAL_DIAGRAM',
    'AP_INTERNAL_REPORT_BACKUP_AND_COMPLETENESS',
    'AP_INTERNAL_REPORT_DISASTER_RECOVERY',
    'AP_INTERNAL_REPORT_NETWORK_DIAGRAM',
    'AP_INTERNAL_REPORT_PENETRATION',
    'AP_INTERNAL_REPORT_RISK_ASSESSMENT',
    'AP_INTERNAL_REPORT_RISK_REMEDIATION',
    'AP_INTERNAL_REPORT_VULNERABILITY',
    'AP_INTERNAL_URL_EXTERNAL_JOBS',
    'AP_INTERNAL_URL_PRIVACY_POLICY',
    'AP_INTERNAL_URL_SUPPORT',
    'AP_INTERNAL_URL_TERMS_OF_SERVICE',
    'AP_INTERNAL_VERSION_CONTROL_IDENTITIES_ONLY_AUTHORIZED_ACCESS',
    'AP_INTERNAL_VERSION_CONTROL_IDENTITIES_REMOVED',
    'AP_INTERNAL_VERSION_CONTROL_IDENTITIES_UNIQUE',
    'AP_LOGS_ARE_CENTRALLY_STORED',
    'AP_LOGS_ARE_RETAINED_FOR_365_DAYS',
    'AP_LOGS_MONITORED_FOR_SUSPICIOUS_ACTIVITY',
    'AP_ONLY_AUTHORIZED_USERS_CAN_ACCESS_LOG_SINKS',
    'AP_SECURITY_AWARENESS_TRAINING',
    'AP_VERSION_CONTROL_CODE_REVIEW_PROCESS',
    'AP_VERSION_CONTROL_ENABLED',
    'AP_VERSION_CONTROL_MFA_ENABLED',
    'AP_VERSION_CONTROL_WRITE_ACCESS_TO_PRODUCTION_CODE',
    'AP_VERSION_CONTROL_WRITE_ACCESS_TO_REPOSITORY',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_AIKIDO',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_ARNICA',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_CROWDSTRIKE_FALCON_EXPOSURE_MANAGEMENT',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_MS_DEFENDER',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_MS_DEFENDER_VMS',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_QUALYS',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_RAPID7_VMS',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_SEMGREP',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_SENTINEL_ONE_VMS',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_SENTINELONE_SINGULARITY_VULNERABILITY_MANAGEMENT',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_SNYK',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_TENABLE_VULNERABILITY_MANAGEMENT',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_WIZ_CODE',
    'AP_VULNERABILITY_CRITICAL_ADDRESSED_WIZ_VMS',
    'AP_VULNERABILITY_HIGH_ADDRESSED',
    'AP_VULNERABILITY_HIGH_ADDRESSED_AIKIDO',
    'AP_VULNERABILITY_HIGH_ADDRESSED_ARNICA',
    'AP_VULNERABILITY_HIGH_ADDRESSED_CROWDSTRIKE_FALCON_EXPOSURE_MANAGEMENT',
    'AP_VULNERABILITY_HIGH_ADDRESSED_MS_DEFENDER',
    'AP_VULNERABILITY_HIGH_ADDRESSED_MS_DEFENDER_VMS',
    'AP_VULNERABILITY_HIGH_ADDRESSED_QUALYS',
    'AP_VULNERABILITY_HIGH_ADDRESSED_RAPID7_VMS',
    'AP_VULNERABILITY_HIGH_ADDRESSED_SEMGREP',
    'AP_VULNERABILITY_HIGH_ADDRESSED_SENTINEL_ONE_VMS',
    'AP_VULNERABILITY_HIGH_ADDRESSED_SENTINELONE_SINGULARITY_VULNERABILITY_MANAGEMENT',
    'AP_VULNERABILITY_HIGH_ADDRESSED_SNYK',
    'AP_VULNERABILITY_HIGH_ADDRESSED_TENABLE_VULNERABILITY_MANAGEMENT',
    'AP_VULNERABILITY_HIGH_ADDRESSED_WIZ_CODE',
    'AP_VULNERABILITY_HIGH_ADDRESSED_WIZ_VMS',
    'APPROVED_REQUEST',
    'ARCHIVE_CONTROL',
    'ARTIFACT_BULK_DELETED',
    'ASSET_NOTES_UPDATED',
    'ASSET_OWNER_UPDATED',
    'ASSETS_DOWNLOAD_ALL',
    'AUDITHUB_REQUEST_MESSAGE_DELETED',
    'AUDITOR_ADDED',
    'AUDITOR_ADDED_TO_AUDIT',
    'AUDITOR_DOES_A_READ_ONLY',
    'AUDITOR_HAS_ENTERED_TO_TENANT_DETAILS',
    'AUDITOR_REMOVED',
    'AUDITOR_REMOVED_FROM_AUDIT',
    'AUDITOR_TOGGLE_READ_ONLY',
    'AUTO_APPROVE_REQUEST',
    'AUTO_RESET_SETTING_UPDATED',
    'AUTOMATED_OFFBOARDING_CONFIGURED',
    'AUTOMATED_OFFBOARDING_FAILED',
    'AUTOMATED_OFFBOARDING_TOGGLE',
    'AUTOPILOT_BACKGROUND_CHECK_LINKED_TO_PERSONNEL',
    'AUTOPILOT_COMPLIANCE_CHECK_UPDATED',
    'AUTOPILOT_FREQUENCY_UPDATED',
    'AUTOPILOT_PERSONNEL_EXCLUSION_ARCHIVED',
    'AUTOPILOT_PERSONNEL_SEPARATED',
    'AUTOPILOT_RECIPE_CUSTOMIZED',
    'AUTOPILOT_RECIPE_REVERTED',
    'AUTOPILOT_SCHEDULE_CREATED',
    'AUTOPILOT_SCHEDULE_DELETED',
    'AUTOPILOT_SCHEDULE_REVERTED',
    'AUTOPILOT_SCHEDULE_UPDATED',
    'AUTOPILOT_USER_CREATED',
    'AUTOPILOT_USER_DELETED_ROLE',
    'AUTOPILOT_USER_UPDATED',
    'BACKGROUND_CHECK_CANCELED',
    'BACKGROUND_CHECK_COMPLETED',
    'BACKGROUND_CHECK_DELETED',
    'BACKGROUND_CHECK_MANUAL',
    'BACKGROUND_CHECK_STARTED',
    'BACKGROUND_LINKED_TO_PERSONNEL',
    'BACKGROUND_UNLINKED_FROM_PERSONNEL',
    'CAC_CODE_REVIEW_REQUESTED',
    'CLOUD_FILE_USER_CONNECTED',
    'CLOUD_FILE_USER_DISCONNECTED',
    'CODE_REPOSITORY_ADDED',
    'CODE_REPOSITORY_REMOVED',
    'CODE_REPOSITORY_SETTINGS_UPDATED',
    'COMPANY_ARCHIVED_DOWNLOADED',
    'COMPANY_DATA_UPDATED',
    'COMPANY_DOCUMENT_DELETED',
    'COMPANY_DOCUMENT_DOWNLOADED',
    'COMPANY_DOCUMENT_UPLOADED',
    'COMPANY_HIPAA_TRAINING_DATA_UPDATED',
    'COMPANY_HUMAN_RESOURCES_DATA_UPDATED',
    'COMPANY_KEY_PERSONNEL_UPDATED',
    'COMPANY_LINKS_DOWNLOADED',
    'COMPANY_LOGO_UPDATED',
    'COMPANY_NIST_AI_TRAINING_DATA_UPDATED',
    'COMPANY_NOTIFICATION_CREATED',
    'COMPANY_NOTIFICATION_DELETED',
    'COMPANY_NOTIFICATION_DISABLED',
    'COMPANY_NOTIFICATION_EDITED',
    'COMPANY_NOTIFICATION_ENABLED',
    'COMPANY_PACKAGE_DOWNLOADED',
    'COMPANY_ROLES_UPDATED',
    'COMPANY_SECURITY_DATA_UPDATED',
    'COMPANY_SECURITY_REPORT_SETTING_UPDATED',
    'COMPANY_SECURITY_REPORT_SHARE_TOKEN_UPDATED',
    'COMPANY_SECURITY_REPORT_SHAREABLE_UPDATED',
    'COMPANY_WORKSTATION_CONFIGURATION_UPDATED',
    'CONNECTIONS_AUDIT_PACKAGE_DOWNLOADED',
    'CONTROL_APPROVAL_ADDED',
    'CONTROL_APPROVAL_APPROVE',
    'CONTROL_APPROVAL_APPROVE_WITHOUT_DATELINE',
    'CONTROL_APPROVAL_EDITED',
    'CONTROL_APPROVAL_REMOVED',
    'CONTROL_APPROVAL_REQUEST_CHANGES',
    'CONTROL_APPROVAL_REVIEW_ADDED',
    'CONTROL_APPROVAL_REVIEW_REMOVED',
    'CONTROL_APPROVAL_SCHEDULED',
    'CONTROL_APPROVAL_SCHEDULED_FAILED',
    'CONTROL_APPROVAL_SENT_TO_APPROVERS',
    'CONTROL_APPROVAL_STATUS_CHANGED',
    'CONTROL_EVIDENCE_DOWNLOADED',
    'CONTROL_EVIDENCE_PACKAGE_GENERATED',
    'CONTROL_INFORMATION_LINKED_ACROSS_OTHER_WORKSPACES',
    'CONTROL_INFORMATION_UNLINKED_FROM_OTHER_WORKSPACES',
    'CONTROL_NOTE_ADDED',
    'CONTROL_NOTE_DELETED',
    'CONTROL_NOTE_EDITED',
    'CONTROL_OWNER_ADDED',
    'CONTROL_OWNER_DELETED',
    'CONTROL_TASK_COMPLETED',
    'CONTROL_TASK_CREATED',
    'CONTROL_TASK_DELETED',
    'CONTROL_TASK_RECURRING_DELETED',
    'CONTROL_TASK_UNCOMPLETED',
    'CONTROL_TASK_UPDATED',
    'CONTROL_TEMPLATE_APPLIED',
    'CONTROL_TEST_ADDED',
    'CONTROL_TEST_DELETED',
    'CONTROL_TEST_INSTANCE_CUSTOM_TEST_EDITED',
    'CONTROL_TEST_INSTANCE_DRAFT_TEST_CREATED',
    'CONTROL_TEST_INSTANCE_DRAFT_TEST_DELETED',
    'CONTROL_TEST_INSTANCE_DRAFT_TEST_LOGIC_EDITED',
    'CONTROL_TEST_INSTANCE_DRAFT_TEST_PUBLISHED',
    'CONTROL_TEST_INSTANCE_NOTE_ADDED',
    'CONTROL_TEST_INSTANCE_NOTE_DELETED',
    'CONTROL_TEST_INSTANCE_NOTE_EDITED',
    'CONTROL_TEST_INSTANCE_PUBLISH_TEST_DELETED',
    'CONTROLS_ARCHIVED',
    'CONTROLS_ASSOCIATED',
    'CONTROLS_DISABLED',
    'CONTROLS_UNARCHIVED',
    'CONTROLS_UNASSOCIATED',
    'CREATE_CONTROL',
    'CUSTOM_DATA_RECORD_REMOVED',
    'CUSTOM_FIELD_CREATED',
    'CUSTOM_FIELD_DELETED',
    'CUSTOM_FIELD_PLACED',
    'CUSTOM_FIELD_SECTION_UPDATED',
    'CUSTOM_FIELD_UNPLACED',
    'CUSTOM_FIELD_UPDATED',
    'CUSTOM_FORMULA_CALCULATED',
    'CUSTOM_FORMULA_CREATED',
    'CUSTOM_FORMULA_DELETED',
    'CUSTOM_FORMULA_UPDATED',
    'CUSTOM_FRAMEWORK_CREATED',
    'CUSTOM_FRAMEWORK_DELETED',
    'CUSTOM_REQUIREMENTS_CREATED',
    'CUSTOM_REQUIREMENTS_DELETED',
    'CUSTOM_REQUIREMENTS_UPDATED',
    'CUSTOM_RESOURCE_RECORD_CREATED',
    'CUSTOM_RESOURCE_RECORD_REMOVED',
    'CUSTOM_RESOURCE_RECORD_UPDATED',
    'CUSTOMER_REQUEST_DETAILS_EDITED',
    'CUSTOMER_REQUEST_DETAILS_EDITED_MULTIPLE_WORKSPACES',
    'DENIED_REQUEST',
    'DEVICE_ASSIGNATION_AGENT',
    'DEVICE_ASSIGNATION_AGENT_DELETED',
    'DEVICE_DELETED',
    'DEVICE_DOCUMENT_DELETED',
    'DEVICE_DOCUMENT_DOWNLOADED',
    'DEVICE_DOCUMENT_UPLOADED',
    'DEVICE_FAILED',
    'DEVICE_LINKED',
    'DEVICE_SWITCHED',
    'DEVICE_UNLINKED',
    'DRATA_SUPPORT_ACCESS_GRANTED',
    'DRATA_SUPPORT_ACCESS_REMOVED',
    'DRATA_TO_DRATA_DISABLED',
    'DRATA_TO_DRATA_ENABLED',
    'EDIT_CONTROL_INFO',
    'EMPLOYMENT_STATUS_UPDATED',
    'ENTITLEMENT_DISABLED',
    'ENTITLEMENT_ENABLED',
    'EVIDENCE_ASSOCIATED_TO_CONTROL',
    'EVIDENCE_BULK_DELETED',
    'EVIDENCE_BULK_OWNER_UPDATE',
    'EVIDENCE_CREATED',
    'EVIDENCE_DELETED',
    'EVIDENCE_DOWNLOAD_ALL',
    'EVIDENCE_DOWNLOADED',
    'EVIDENCE_UNASSOCIATED_TO_CONTROL',
    'EVIDENCE_UPDATED',
    'EXCEPTION_REQUEST_CREATED',
    'EXCEPTION_WORKFLOW_CREATED',
    'EXCEPTION_WORKFLOW_DELETED',
    'EXCEPTION_WORKFLOW_UPDATED',
    'EXTERNAL_DOCUMENT_LINKED_TO_POLICY',
    'EXTERNAL_EVIDENCE_DELETED',
    'EXTERNAL_EVIDENCE_EDITED',
    'EXTERNAL_EVIDENCE_URL_EDITED',
    'FILE_UPLOAD_REJECTED',
    'FILE_UPLOAD_UNSUPPORTED',
    'FINDING_EXCLUDED',
    'FINDING_INCLUDED',
    'FORCE_SYNC_ON_A_PERSON',
    'FORCE_SYNC_ON_ALL',
    'FORCE_SYNC_ON_SOME',
    'FRAMEWORK_AUDIT_DETAILS_EDITED',
    'FRAMEWORK_AUDIT_DETAILS_EDITED_MULTIPLE_WORKSPACES',
    'FRAMEWORK_BASELINE_UPDATED',
    'FRAMEWORK_CONTROL_MAPPING_RESET',
    'FRAMEWORK_DISABLED',
    'FRAMEWORK_ENABLED',
    'FRAMEWORK_LEVEL_IMPACT_UPDATED',
    'FRAMEWORK_SAQ_UPDATED',
    'GENERAL_TASK_COMPLETED',
    'GENERAL_TASK_CREATED',
    'GENERAL_TASK_DELETED',
    'GENERAL_TASK_RECURRING_DELETED',
    'GENERAL_TASK_UNCOMPLETED',
    'GENERAL_TASK_UPDATED',
    'GROUPS_DELETED',
    'GROUPS_IDENTITY_CREATED',
    'GROUPS_IDENTITY_DELETED',
    'GROUPS_IDENTITY_UPDATED',
    'GROUPS_PERSONNEL_ADDED',
    'GROUPS_PERSONNEL_DELETED',
    'GROUPS_PERSONNEL_IDENTITY_ADDED',
    'GROUPS_PERSONNEL_IDENTITY_REMOVED',
    'HIPAA_TRAINING_RESET',
    'HIPAA_TRAINING_UPDATED',
    'HUMAN_RESOURCES_AUDIT_PACKAGE_DOWNLOADED',
    'INFRASTRUCTURE_ACCESS_AUDIT_PACKAGE_DOWNLOADED',
    'MANUAL_ASSET_CREATED',
    'MANUAL_ASSET_DELETED',
    'MANUAL_ASSET_UPDATED',
    'MAPPED_CONTROLS_RESET_ON_REQUIREMENT',
    'MAPPED_POLICIES_RESET_ON_CONTROL',
    'MAPPED_REQUIREMENTS_RESET_ON_CONTROL',
    'MAPPED_TESTS_RESET_ON_CONTROL',
    'MDM_USER_DATA_UPDATED',
    'MONITOR_EXCLUSION_CREATED',
    'MONITOR_EXCLUSION_DELETED',
    'MONITOR_EXCLUSION_UPDATED',
    'NDA_DELETED',
    'NDA_UPLOADED',
    'NEW_REPORT',
    'NEW_REQUEST',
    'NIST_AI_TRAINING_RESET',
    'NIST_AI_TRAINING_UPDATED',
    'NON_DISCLOSURE',
    'NOTIFICATION_WORKFLOW_CREATED',
    'OBSERVABILITY_ACCESS_AUDIT_PACKAGE_DOWNLOADED',
    'PERSONNEL_BG_CHECKS_REVOKED',
    'PERSONNEL_EXCLUSION_ARCHIVED',
    'PERSONNEL_EXCLUSION_CREATED',
    'PERSONNEL_EXCLUSION_UPDATED',
    'PERSONNEL_OFFBOARDING_TICKET_LINKED',
    'PERSONNEL_OFFBOARDING_TICKET_UNLINKED',
    'PERSONNEL_REVIEW_STATUS_CHANGED',
    'POLICIES_DOWNLOAD_ALL',
    'POLICY_ADDED',
    'POLICY_ARCHIVED',
    'POLICY_ASSIGNED_ALL',
    'POLICY_ASSIGNED_GROUPS',
    'POLICY_ASSIGNED_NONE',
    'POLICY_ASSOCIATED_TO_CONTROL',
    'POLICY_BECAME_ORPHAN',
    'POLICY_DELETED',
    'POLICY_DETAILS_UPDATED',
    'POLICY_DOWNLOADED',
    'POLICY_OWNER_APPROVED',
    'POLICY_OWNER_UPDATED',
    'POLICY_RENEW_WITHOUT_UPDATE',
    'POLICY_REPLACED',
    'POLICY_RESTART_FROM_DRAFT',
    'POLICY_RESTART_FROM_LATEST_TEMPLATE',
    'POLICY_RESTORE_ARCHIVED',
    'POLICY_RESTORE_REPLACED',
    'POLICY_REVERT_TO_LATEST_TEMPLATE',
    'POLICY_REVERT_TO_LATEST_VERSION',
    'POLICY_TEMPLATE_DOWNLOADED',
    'POLICY_UNASSOCIATED_TO_CONTROL',
    'POLICY_VERSION_ADDED',
    'POLICY_VERSION_ALERT_CONFIGURED',
    'POLICY_VERSION_APPROVAL_OVERRIDE',
    'POLICY_VERSION_APPROVAL_SETTINGS_UPDATED',
    'POLICY_VERSION_APPROVED',
    'POLICY_VERSION_APPROVED_BY_APPROVER',
    'POLICY_VERSION_APPROVED_BY_APPROVER_TIER',
    'POLICY_VERSION_AUTO_ACKNOWLEDGMENT',
    'POLICY_VERSION_DELETED',
    'POLICY_VERSION_DRAFT_FINALIZED',
    'POLICY_VERSION_MODIFIED_NO_REQUIRED_APPROVAL',
    'POLICY_VERSION_MODIFIED_RESTARTED_APPROVAL',
    'POLICY_VERSION_PUBLISHED',
    'POLICY_VERSION_REQUEST_CHANGES',
    'POLICY_VERSION_STATUS_CANCEL_APPROVAL',
    'POLICY_VERSION_STATUS_DRAFT_CREATED',
    'POLICY_VERSION_STATUS_DRAFT_DELETED',
    'POLICY_VERSION_STATUS_DRAFT_FINALIZED',
    'POLICY_VERSION_UPDATED',
    'PRE_APPROVED_DOMAIN_ADDED',
    'PRE_APPROVED_DOMAIN_REMOVED',
    'PRE_APPROVED_EMAIL_ADDED',
    'PRE_APPROVED_EMAIL_REMOVED',
    'PROVIDER_CONNECTION_CREATED',
    'PROVIDER_CONNECTION_ERROR',
    'PROVIDER_CONNECTION_REMOVED',
    'PROVIDER_CONNECTION_UPDATED',
    'PROVIDER_WORKSPACE_CONNECTION_CREATED',
    'PROVIDER_WORKSPACE_CONNECTION_REMOVED',
    'PUBLIC_API_KEY_ALLOW_LIST_IP_ADDRESSES_EDITED',
    'PUBLIC_API_KEY_CREATED',
    'PUBLIC_API_KEY_REVOKED',
    'PUBLIC_API_KEY_UPDATED',
    'QUESTIONNAIRE_ARCHIVE_DOWNLOADED',
    'REMINDER_EMAIL_SENT',
    'REPORT_ASSOCIATED_TO_CONTROL',
    'REPORT_CREATED',
    'REPORT_DELETED',
    'REPORT_DOWNLOADED',
    'REPORT_UNASSOCIATED_TO_CONTROL',
    'REPORT_UPDATED',
    'REPORTS_DOWNLOAD_ALL',
    'REQUEST_MESSAGE_SENT',
    'REQUEST_STATUS_CHANGED',
    'REQUIREMENT_ASSOCIATED_TO_CONTROL',
    'REQUIREMENT_IN_SCOPE',
    'REQUIREMENT_INFO_EDITED',
    'REQUIREMENT_OUT_OF_SCOPE',
    'REQUIREMENT_UNASSOCIATED_TO_CONTROL',
    'RESENT_EVIDENCE_SAMPLE_DOWNLOAD_EMAIL',
    'RESENT_EVIDENCE_SAMPLE_DOWNLOAD_EMAIL_MULTIPLE_WORKSPACE',
    'RESYNC_DATA',
    'REVOKE_REQUEST',
    'RISK_ADDED',
    'RISK_ADDED_FROM_LIBRARY',
    'RISK_ALL_REGISTERED_SCORED',
    'RISK_ALL_REGISTERED_TREATED',
    'RISK_ARCHIVED',
    'RISK_ASSESSMENT_DUE_DATE_SET',
    'RISK_ASSESSMENT_OWNER_ASSIGNED',
    'RISK_ASSESSMENT_REPORT_GENERATED',
    'RISK_CATEGORY_CREATED',
    'RISK_CATEGORY_REMOVED',
    'RISK_CLOSED',
    'RISK_DASHBOARD_DOWNLOADED',
    'RISK_EDITED',
    'RISK_MAPPED_TO_CONTROL',
    'RISK_NOTE_ADDED',
    'RISK_NOTE_DELETED',
    'RISK_NOTE_UPDATED',
    'RISK_OWNERS_ASSIGNED',
    'RISK_SCORED',
    'RISK_SET_ACTIVE',
    'RISK_SET_APPLICABLE',
    'RISK_SET_NOT_APPLICABLE',
    'RISK_SETTINGS_UPDATED',
    'RISK_TASK_COMPLETED',
    'RISK_TASK_CREATED',
    'RISK_TASK_DELETED',
    'RISK_TASK_RECURRING_DELETED',
    'RISK_TASK_UNCOMPLETED',
    'RISK_TASK_UPDATED',
    'RISK_TREATMENT_REPORT_GENERATED',
    'RISK_TREATMENT_SET',
    'RISK_UNMAPPED_FROM_CONTROL',
    'RISKS_DELETED',
    'RISKS_DOWNLOADED',
    'RISKS_FILTERED_VIEW_DOWNLOADED',
    'SECURITY_AWARENESS_TRAINING_RESET',
    'SECURITY_AWARENESS_TRAINING_UPDATED',
    'SERVICE_PROVIDER_ADDED',
    'SERVICE_PROVIDER_DELETED',
    'SPECIAL_FORMER_PERSONNEL_ADDED',
    'TASK_COMPLETED',
    'TASK_CREATED',
    'TASK_DELETED',
    'TASK_RECURRING_DELETED',
    'TASK_UNCOMPLETED',
    'TASK_UPDATED',
    'TERMS_AGREED',
    'TICKET_CREATED_APPLICATION_USERS',
    'TICKET_CREATED_CONTROL',
    'TICKET_CREATED_MONITOR_INSTANCE',
    'TICKET_CREATED_RISK',
    'TICKET_DOWNLOAD',
    'TICKET_UNLINKED_CONTROL',
    'TICKET_UNLINKED_MONITOR_INSTANCE',
    'TICKET_UNLINKED_RISK',
    'TRUST_CONTENT_SAVED',
    'TRUST_PAGES_SAVED',
    'UNARCHIVE_CONTROL',
    'UPLOAD_EXTERNAL_EVIDENCE',
    'USER_DOCUMENT_DELETED',
    'USER_DOCUMENT_DOWNLOADED',
    'USER_DOCUMENT_UPLOADED',
    'USER_IDENTITY_ACCESS_REVIEW_LINK_UPDATED',
    'USER_IDENTITY_INFRASTRUCTURE_LINK_UPDATED',
    'USER_IDENTITY_INFRASTRUCTURE_SERVICE_ACCOUNT_UPDATED',
    'USER_IDENTITY_INFRASTRUCTURE_SYNCED',
    'USER_IDENTITY_INFRASTRUCTURE_TOGGLE_UPDATED',
    'USER_IDENTITY_INFRASTRUCTURE_USER_CREATED',
    'USER_IDENTITY_INFRASTRUCTURE_USER_DISCONNECTED',
    'USER_IDENTITY_INFRASTRUCTURE_USER_UPDATED',
    'USER_IDENTITY_OBSERVABILITY_LINK_UPDATED',
    'USER_IDENTITY_OBSERVABILITY_SERVICE_ACCOUNT_UPDATED',
    'USER_IDENTITY_OBSERVABILITY_SYNCED',
    'USER_IDENTITY_OBSERVABILITY_TOGGLE_UPDATED',
    'USER_IDENTITY_OBSERVABILITY_USER_CREATED',
    'USER_IDENTITY_OBSERVABILITY_USER_DISCONNECTED',
    'USER_IDENTITY_OBSERVABILITY_USER_UPDATED',
    'USER_IDENTITY_PERSONNEL_SYNCED',
    'USER_IDENTITY_VERSION_CONTROL_LINK_UPDATED',
    'USER_IDENTITY_VERSION_CONTROL_SERVICE_ACCOUNT_UPDATED',
    'USER_IDENTITY_VERSION_CONTROL_SYNCED',
    'USER_IDENTITY_VERSION_CONTROL_TOGGLE_UPDATED',
    'USER_IDENTITY_VERSION_CONTROL_USER_CREATED',
    'USER_IDENTITY_VERSION_CONTROL_USER_DISCONNECTED',
    'USER_IDENTITY_VERSION_CONTROL_USER_UPDATED',
    'USER_POLICY_ACCEPTED',
    'USER_ROLE_CREATED',
    'USER_ROLE_DELETED',
    'USER_ROLE_RESTRICTED',
    'USER_ROLE_UNRESTRICTED',
    'VENDOR_CREATED',
    'VENDOR_DELETED',
    'VENDOR_DOCUMENT_DELETED',
    'VENDOR_DOCUMENT_DOWNLOADED',
    'VENDOR_DOCUMENT_UPLOADED',
    'VENDOR_MANUAL_UPLOAD',
    'VENDOR_QUESTIONNAIRE_ANSWER_SAVED',
    'VENDOR_QUESTIONNAIRE_EMAIL_SENT',
    'VENDOR_QUESTIONNAIRE_RESPONSE_SAVED',
    'VENDOR_RISK_REPORT_GENERATED',
    'VENDOR_SECURITY_QUESTIONNAIRE_CREATED',
    'VENDOR_SECURITY_QUESTIONNAIRE_DELETED',
    'VENDOR_SECURITY_QUESTIONNAIRE_UPDATED',
    'VENDOR_SECURITY_REVIEW_APPROVAL_STATUS_CHANGED',
    'VENDOR_SECURITY_REVIEW_COMPLETED',
    'VENDOR_SECURITY_REVIEW_STARTED',
    'VENDOR_SOC_REPORT_REVIEW_APPROVAL_STATUS_CHANGED',
    'VENDOR_SOC_REPORT_REVIEW_COMPLETED',
    'VENDOR_SOC_REPORT_REVIEW_STARTED',
    'VENDOR_UPDATED',
    'VENDORS_DOWNLOAD_ALL',
    'VERSION_CONTROL_AUDIT_PACKAGE_DOWNLOADED',
    'VULNERABILITY_AUDIT_PACKAGE_DOWNLOAD',
    'VULNERABILITY_DOWNLOAD_ALL',
    'VULNERABILITY_FINDING_CREATED',
    'VULNERABILITY_FINDING_UPDATED',
    'VULNERABILITY_FINDINGS_SYNCED',
    'VULNERABILITY_SLA_SETTING_CREATED',
    'VULNERABILITY_SLA_SETTING_UPDATED',
    'WIZARD_STEP_SAVED',
    'WORKFLOW_ARCHIVED',
    'WORKFLOW_DELETED',
    'WORKFLOW_DRAFT_SAVED',
    'WORKFLOW_NOTIFICATION_SENT',
    'WORKFLOW_PUBLISHED',
    'WORKFLOW_RESTORED',
    'WORKFLOW_UNPUBLISHED',
    'WORKFLOW_WEBHOOK_SENT',
    'WORKSPACE_CREATED',
    'WORKSPACE_DELETED',
    'WORKSPACE_EDITED',
    'POLICY_VERSION_UPLOADED',
    'SAFEBASE_SYNC_PENDING',
    'SAFEBASE_SYNC_ERROR',
    'SAFEBASE_SYNC_COMPLETE',
    'SAFEBASE_MIGRATION_PENDING',
    'SAFEBASE_MIGRATION_MIGRATED',
    'SAFEBASE_MIGRATION_ERROR',
    'SAFEBASE_MIGRATION_COMPLETE',
    'TRUST_CENTER_INGRESS_DENIED',
    'CONNECTION_IDP_RANKING_UPDATED',
    'MONITOR_FINDINGS_ZIP_GENERATED',
    'MONITOR_FINDINGS_CSV_GENERATED',
] as const;
