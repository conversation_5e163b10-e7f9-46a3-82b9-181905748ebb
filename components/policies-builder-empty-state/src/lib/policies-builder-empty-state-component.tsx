import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { policyEmptyStateController } from './controllers/policy-empty-state.controller';

const handleStartBuilding = () => {
    policyEmptyStateController.handleStartBuilding();
};

const handleUploadExisting = () => {
    policyEmptyStateController.handleUploadExisting();
};

export const PoliciesBuilderEmptyStateComponent = observer(
    (): React.JSX.Element => {
        return (
            <Stack
                data-testid="PoliciesBuilderEmptyStateComponent"
                data-id="ydOG1e73"
                height="100%"
                justify="center"
                align="center"
            >
                <Box width={breakpointMd}>
                    <EmptyState
                        illustrationName="AddPage"
                        title={t`Policy builder`}
                        description={t`Start building your policy now using an auditor approved template. You can edit, update, and manage your policy anytime here in Drata.`}
                        leftAction={
                            <Button
                                label={t`Start building`}
                                level="primary"
                                a11yLoadingLabel={t`Creating policy version...`}
                                isLoading={toJS(
                                    policyEmptyStateController.isCreatingPolicy,
                                )}
                                onClick={handleStartBuilding}
                            />
                        }
                        rightAction={
                            <Button
                                label={t`Or upload existing policy`}
                                level="tertiary"
                                onClick={handleUploadExisting}
                            />
                        }
                    />
                </Box>
            </Stack>
        );
    },
);
