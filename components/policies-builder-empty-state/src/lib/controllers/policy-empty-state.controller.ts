import { isError } from 'lodash-es';
import { z } from 'zod';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { policyVersionControllerPostPolicyVersionMutation } from '@globals/api-sdk/queries';
import type {
    PolicyResponseDto,
    PolicyVersionControllerPostPolicyVersionData,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import {
    closePolicyEmptyStateModal,
    openPolicyEmptyStateModal,
} from '../helpers/open-policy-empty-state-modal.helper';
import { policyOwnerComboboxController } from '../policy-owners.controller';

class PolicyEmptyStateController {
    createPolicyVersionMutation = new ObservedMutation(
        policyVersionControllerPostPolicyVersionMutation,
        {
            onSuccess: () => {
                this.handleCreatePolicySuccess();
            },
            onError: (error) => {
                this.handleCreatePolicyError({ error });
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isCreatingPolicy(): boolean {
        return this.createPolicyVersionMutation.isPending;
    }

    getFormSchema(source: 'BUILDER' | 'UPLOADED'): FormSchema {
        return {
            ownerId: {
                type: 'combobox',
                label: t`Policy owner`,
                placeholder: t`Search for a policy owner...`,
                options: policyOwnerComboboxController.options,
                isLoading: policyOwnerComboboxController.isLoading,
                loaderLabel: t`Loading owners...`,
                validator: z.object(
                    {
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    },
                    { message: t`Please select a policy owner` },
                ),
                onFetchOptions: (params) => {
                    policyOwnerComboboxController.load(params);
                },
            },
            ...(source === 'UPLOADED' && {
                file: {
                    type: 'file',
                    label: t`Policy document`,
                    acceptedFormats: ['pdf', 'doc', 'docx', 'txt'],
                    isMulti: true,
                    oneFileOnly: true,
                    innerLabel: t`Or drop file here`,
                    selectButtonText: t`Upload file`,
                    removeButtonText: t`Remove file`,
                    errorCodeMessages: {
                        'file-invalid-type': t`Please upload a PDF, DOC, DOCX, or TXT file`,
                        'file-too-large': t`File size must be less than 25MB`,
                        'file-too-small': t`File is too small`,
                        'too-many-files': t`Please upload only one file`,
                    },
                },
            }),
        };
    }

    handleStartBuilding(): void {
        const { policy } = sharedPolicyBuilderController;

        if (!policy) {
            logger.error({
                message:
                    'Cannot start building policy: policy not found in policy builder controller',
            });

            return;
        }

        this.trackStartBuilding(policy);
        openPolicyEmptyStateModal({ source: 'BUILDER' });
    }

    handleUploadExisting(): void {
        const { policy } = sharedPolicyBuilderController;

        if (!policy) {
            logger.error({
                message:
                    'Cannot upload existing policy: policy not found in policy builder controller',
            });

            return;
        }

        openPolicyEmptyStateModal({ source: 'UPLOADED' });
    }

    closeModal(): void {
        closePolicyEmptyStateModal();
    }

    handleFormSubmit({
        source,
        formData,
    }: {
        source: 'BUILDER' | 'UPLOADED';
        formData: {
            ownerId: {
                id: string;
                label: string;
                value: string;
            };
            file?: File[];
        };
    }): void {
        const { policy } = sharedPolicyBuilderController;

        if (!policy) {
            logger.error({
                message:
                    'Cannot submit form: policy not found in policy builder controller',
            });

            return;
        }

        this.createDraftPolicyVersion({
            policy,
            sourceType: source,
            file: formData.file,
            ownerId: formData.ownerId.value,
        });
    }

    createDraftPolicyVersion({
        policy,
        sourceType,
        file,
        ownerId,
    }: {
        policy: PolicyResponseDto;
        sourceType: 'BUILDER' | 'UPLOADED';
        file?: File[];
        ownerId?: string;
    }): void {
        const formData = this.buildApiFormData({
            sourceType,
            file,
            ownerId,
        });

        this.createPolicyVersionMutation.mutate({
            path: { id: policy.id },
            body: formData,
        });
    }

    private buildApiFormData({
        sourceType,
        file,
        ownerId,
    }: {
        sourceType: 'BUILDER' | 'UPLOADED';
        file?: File[];
        ownerId?: string;
    }): PolicyVersionControllerPostPolicyVersionData['body'] {
        return {
            policyType: sourceType,
            policyVersionStatus: 'DRAFT',
            ...(ownerId && { ownerId: Number(ownerId) }),
            ...(file && { file }),
        };
    }

    private handleCreatePolicySuccess(): void {
        sharedPolicyBuilderController.invalidatePolicyQueries();
        this.closeModal();

        snackbarController.addSnackbar({
            id: 'policy-version-created-success',
            props: {
                title: t`Policy version created`,
                description: t`Your policy version has been created successfully.`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });

        const { policyId } = sharedPolicyBuilderController;

        if (policyId) {
            sharedProgrammaticNavigationController.navigateTo(
                `${routeController.userPartOfUrl}/governance/policies/builder/${policyId}/overview`,
            );
        }
    }

    private handleCreatePolicyError({ error }: { error: unknown }): void {
        logger.error({
            message: 'Failed to create policy version',
            errorObject: {
                message: isError(error) ? error.message : 'Unknown error',
                statusCode: 'unknown',
            },
        });

        snackbarController.addSnackbar({
            id: 'policy-version-created-error',
            props: {
                title: t`Error creating policy version`,
                description: t`Failed to create policy version. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }

    private trackStartBuilding(policy: PolicyResponseDto): void {
        console.info('Analytics: START_BUILDING_POLICY_TEMPLATE', {
            policyId: policy.id,
            policyName: policy.name,
            timestamp: new Date().toISOString(),
        });
    }
}

export const policyEmptyStateController = new PolicyEmptyStateController();
