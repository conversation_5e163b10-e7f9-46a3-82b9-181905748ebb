import { useCallback, useEffect } from 'react';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { type MessageDescriptor, useLingui } from '@globals/i18n';
import { msg, t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import { policyEmptyStateController } from './controllers/policy-empty-state.controller';
import { policyOwnerComboboxController } from './policy-owners.controller';

interface PolicyEmptyStateModalProps {
    onClose: () => void;
    source: 'BUILDER' | 'UPLOADED';
}

interface PolicyEmptyStateFormValues {
    ownerId: {
        id: string;
        label: string;
        value: string;
    };
    /**
     * Optional because only present in 'upload' mode.
     */
    file?: File[];
}

const modalTitles: Record<
    PolicyEmptyStateModalProps['source'],
    MessageDescriptor
> = {
    BUILDER: msg`Start building policy`,
    UPLOADED: msg`Upload existing policy`,
};

const modalDescriptions: Record<
    PolicyEmptyStateModalProps['source'],
    MessageDescriptor
> = {
    BUILDER: msg`Select an owner to start building your policy.`,
    UPLOADED: msg`Select an owner and upload your existing policy document.`,
};

export const PolicyEmptyStateModal = observer(
    ({ onClose, source }: PolicyEmptyStateModalProps): React.JSX.Element => {
        const { _ } = useLingui();

        const formSchema = toJS(
            policyEmptyStateController.getFormSchema(source),
        );

        useEffect(() => {
            policyOwnerComboboxController.load({ search: '' });
        }, []);

        const handleFormSubmit = useCallback(
            (data: FormValues) => {
                // The form schema ensures this data structure through Zod validation
                const typedData = data as unknown as PolicyEmptyStateFormValues;

                policyEmptyStateController.handleFormSubmit({
                    source,
                    formData: typedData,
                });
            },
            [source],
        );

        return (
            <>
                <Modal.Header
                    title={_(modalTitles[source])}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Text
                            type="body"
                            size="200"
                            colorScheme="neutral"
                            data-id="modal-description"
                        >
                            {_(modalDescriptions[source])}
                        </Text>

                        <Form
                            hasExternalSubmitButton
                            formId="policy-empty-state-form"
                            data-id="policy-empty-state-form"
                            schema={formSchema}
                            onSubmit={handleFormSubmit}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            level: 'tertiary',
                            label: t`Cancel`,
                            onClick: onClose,
                        },
                        {
                            type: 'submit',
                            form: 'policy-empty-state-form',
                            label:
                                source === 'BUILDER'
                                    ? t`Start building`
                                    : t`Upload policy`,
                            isLoading: toJS(
                                policyEmptyStateController.isCreatingPolicy,
                            ),
                            a11yLoadingLabel: t`Creating policy version...`,
                        },
                    ]}
                />
            </>
        );
    },
);
