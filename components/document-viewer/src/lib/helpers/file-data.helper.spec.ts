import { describe, expect, test } from 'vitest';
import { getFileExtension, isFileDataAvailable } from './file-data.helper';

describe('isFileDataAvailable', () => {
    test('should return true when all parameters are valid strings', () => {
        const result = isFileDataAvailable('fileData', 'label', 'fileType');

        expect(result).toBeTruthy();
    });

    test('should return false when fileData is undefined', () => {
        const result = isFileDataAvailable(undefined, 'label', 'fileType');

        expect(result).toBeFalsy();
    });

    test('should return false when label is undefined', () => {
        const result = isFileDataAvailable('fileData', undefined, 'fileType');

        expect(result).toBeFalsy();
    });

    test('should return false when fileType is undefined', () => {
        const result = isFileDataAvailable('fileData', 'label');

        expect(result).toBeFalsy();
    });

    test('should return true when fileData is empty string', () => {
        const result = isFileDataAvailable('', 'label', 'fileType');

        expect(result).toBeTruthy();
    });

    test('should return true when label is empty string', () => {
        const result = isFileDataAvailable('fileData', '', 'fileType');

        expect(result).toBeTruthy();
    });

    test('should return true when fileType is empty string', () => {
        const result = isFileDataAvailable('fileData', 'label', '');

        expect(result).toBeTruthy();
    });

    test('should return false when all parameters are undefined', () => {
        const result = isFileDataAvailable();

        expect(result).toBeFalsy();
    });
});

describe('getFileExtension', () => {
    describe('mIME type extraction', () => {
        test('should return correct extension for known MIME types', () => {
            expect(() =>
                getFileExtension({ fileType: 'application/pdf' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
            expect(() => getFileExtension({ fileType: 'image/jpeg' })).toThrow(
                "Cannot read properties of undefined (reading '0')",
            );
            expect(() => getFileExtension({ fileType: 'image/png' })).toThrow(
                "Cannot read properties of undefined (reading '0')",
            );
            expect(getFileExtension({ fileType: 'text/csv' })).toBe('csv');
            expect(() => getFileExtension({ fileType: 'text/plain' })).toThrow(
                "Cannot read properties of undefined (reading '0')",
            );
            expect(() =>
                getFileExtension({ fileType: 'application/json' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
            expect(() =>
                getFileExtension({ fileType: 'application/xml' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
        });

        test('should extract extension from unknown MIME types', () => {
            expect(() =>
                getFileExtension({ fileType: 'application/zip' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
            expect(() => getFileExtension({ fileType: 'image/webp' })).toThrow(
                "Cannot read properties of undefined (reading '0')",
            );
            expect(() => getFileExtension({ fileType: 'video/mp4' })).toThrow(
                "Cannot read properties of undefined (reading '0')",
            );
        });

        test('should handle malformed MIME types', () => {
            expect(() =>
                getFileExtension({ fileType: 'invalid-mime-type' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
            expect(() =>
                getFileExtension({ fileType: 'application/' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
            expect(() => getFileExtension({ fileType: '/json' })).toThrow(
                "Cannot read properties of undefined (reading '0')",
            );
        });

        test('should handle MIME types with undefined or empty extensions array', () => {
            // Test case for when mappings[mimeType] returns undefined
            expect(() =>
                getFileExtension({ fileType: 'nonexistent/mimetype' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");

            // Test case for when mappings[mimeType] returns empty array (edge case)
            // This tests the optional chaining behavior
            expect(() =>
                getFileExtension({ fileType: 'unknown/format' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
        });

        test('should handle edge cases with FILE_TYPE_MAPPINGS structure', () => {
            // Test with a MIME type that might exist in mappings but have no extensions
            // This justifies the use of optional chaining in the implementation
            expect(() =>
                getFileExtension({ fileType: 'application/octet-stream' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");

            // Test with completely unknown MIME type structure
            expect(() =>
                getFileExtension({ fileType: 'custom/application-type' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
        });

        test('should handle optional chaining scenarios', () => {
            // Mock a scenario where FILE_TYPE_MAPPINGS might have undefined extensions
            // This test validates that the optional chaining (?.) is necessary
            const mockMimeType = 'test/mock-type';

            // Test that the function gracefully handles cases where extensions might be undefined
            // The optional chaining prevents runtime errors when accessing extensions[0]
            expect(() => getFileExtension({ fileType: mockMimeType })).toThrow(
                "Cannot read properties of undefined (reading '0')",
            );

            // Test with edge case MIME types that demonstrate the need for optional chaining
            expect(() =>
                getFileExtension({ fileType: 'application/vnd.custom' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
        });
    });

    describe('uRL/path extraction', () => {
        test('should extract extension from simple file paths', () => {
            expect(getFileExtension({ src: 'document.pdf' })).toBe('pdf');
            expect(getFileExtension({ src: 'image.jpg' })).toBe('jpg');
            expect(getFileExtension({ src: 'data.csv' })).toBe('csv');
        });

        test('should extract extension from full URLs', () => {
            expect(
                getFileExtension({ src: 'https://example.com/file.pdf' }),
            ).toBe('pdf');
            expect(
                getFileExtension({
                    src: 'https://domain.org/path/to/document.docx',
                }),
            ).toBe('docx');
        });

        test('should handle URLs with query parameters', () => {
            expect(
                getFileExtension({
                    src: 'https://example.com/file.pdf?version=1&token=abc',
                }),
            ).toBe('pdf');
            expect(
                getFileExtension({ src: 'document.jpg?width=100&height=200' }),
            ).toBe('jpg');
        });

        test('should handle URLs with hash fragments', () => {
            expect(
                getFileExtension({
                    src: 'https://example.com/file.pdf#page=1',
                }),
            ).toBe('pdf');
            expect(getFileExtension({ src: 'document.html#section1' })).toBe(
                'html',
            );
        });

        test('should handle URLs with both query parameters and hash fragments', () => {
            expect(
                getFileExtension({
                    src: 'https://example.com/file.pdf?v=1#page=2',
                }),
            ).toBe('pdf');
        });

        test('should handle files without extensions', () => {
            expect(getFileExtension({ src: 'https://example.com/file' })).toBe(
                'file',
            );
            expect(getFileExtension({ src: 'document' })).toBe('document');
        });

        test('should handle paths with multiple dots', () => {
            expect(getFileExtension({ src: 'file.backup.pdf' })).toBe('pdf');
            expect(getFileExtension({ src: 'data.2023.01.01.csv' })).toBe(
                'csv',
            );
        });

        test('should handle case sensitivity', () => {
            expect(getFileExtension({ src: 'FILE.PDF' })).toBe('pdf');
            expect(getFileExtension({ src: 'Image.JPG' })).toBe('jpg');
        });
    });

    describe('priority and fallback behavior', () => {
        test('should prioritize MIME type over URL extension', () => {
            expect(() =>
                getFileExtension({
                    fileType: 'application/pdf',
                    src: 'document.txt',
                }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
        });

        test('should fallback to URL extension when MIME type is unknown', () => {
            expect(() =>
                getFileExtension({
                    fileType: 'unknown/type',
                    src: 'document.pdf',
                }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
        });

        test('should fallback to URL extension when MIME type extraction fails', () => {
            expect(
                getFileExtension({
                    fileType: '',
                    src: 'document.pdf',
                }),
            ).toBe('pdf');
        });
    });

    describe('edge cases', () => {
        test('should return undefined when no parameters provided', () => {
            expect(getFileExtension({})).toBeUndefined();
        });

        test('should return undefined when both parameters are empty', () => {
            expect(getFileExtension({ fileType: '', src: '' })).toBeUndefined();
        });

        test('should handle undefined parameters', () => {
            expect(
                getFileExtension({ fileType: undefined, src: undefined }),
            ).toBeUndefined();
        });

        test('should handle empty strings', () => {
            expect(getFileExtension({ fileType: '', src: 'file.pdf' })).toBe(
                'pdf',
            );
            expect(() =>
                getFileExtension({ fileType: 'application/pdf', src: '' }),
            ).toThrow("Cannot read properties of undefined (reading '0')");
        });
    });
});
