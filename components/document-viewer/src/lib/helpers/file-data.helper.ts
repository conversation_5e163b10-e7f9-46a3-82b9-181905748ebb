import { isEmpty, isString } from 'lodash-es';
import { FILE_TYPE_MAPPINGS } from '@cosmos/components/file-upload';

/**
 * Helper function to get file extension from MIME type using existing FILE_TYPE_MAPPINGS.
 */
const getExtensionFromMimeType = (mimeType: string): string | undefined => {
    for (const { mappings } of Object.values(FILE_TYPE_MAPPINGS)) {
        const extensions = mappings[mimeType];

        if (extensions[0]) {
            return extensions[0].replace('.', '');
        }
    }

    return undefined;
};

/**
 * Extracts file extension from either MIME type or file URL/path.
 *
 * @param params - Configuration object containing fileType and src properties.
 * @returns The file extension in lowercase, or undefined if not determinable.
 */
export const getFileExtension = ({
    fileType,
    src,
}: {
    fileType?: string;
    src?: string;
}): string | undefined => {
    if (fileType) {
        const mappedExtension = getExtensionFromMimeType(fileType);

        if (mappedExtension) {
            return mappedExtension;
        }

        const extensionFromMime = fileType.split('/').pop();

        if (extensionFromMime && !isEmpty(extensionFromMime)) {
            return extensionFromMime;
        }
    }

    if (src) {
        // Remove query parameters and hash fragments, then extract extension
        const cleanPath = src.split('?')[0].split('#')[0];

        // Handle URLs by extracting just the filename part
        const pathParts = cleanPath.split('/');
        const filename = pathParts[pathParts.length - 1];

        // Extract extension from filename
        const filenameParts = filename.split('.');

        if (filenameParts.length > 1) {
            return filenameParts[filenameParts.length - 1].toLowerCase();
        }

        // If no extension found, return the filename itself (for files without extensions)
        return filename.toLowerCase();
    }

    return undefined;
};

/**
 * Checks if file data is available for rendering.
 *
 * @param fileData - The file data string.
 * @param label - The file label/name.
 * @param fileType - The file type.
 * @returns True if all required file data parameters are valid strings.
 */
export const isFileDataAvailable = (
    fileData?: string,
    label?: string,
    fileType?: string,
): boolean => {
    return isString(fileData) && isString(label) && isString(fileType);
};
