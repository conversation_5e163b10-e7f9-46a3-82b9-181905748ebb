import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { CsvViewer } from './components/csv-viewer';
import { UnsupportedDocumentEmptyState } from './components/unsupported-document-empty-state.component';
import {
    IMAGE_EXTENSIONS,
    SUPPORTED_EXTENSIONS,
} from './constants/document-viewer.constants';
import { isCsvFile } from './helpers/csv-parser.helper';
import {
    getFileExtension,
    isFileDataAvailable,
} from './helpers/file-data.helper';

export interface DocumentViewerProps {
    src: string;
    label: string;
    fileType?: string;
    fileData?: string;
    'data-id'?: string;
}

export const DocumentViewer = ({
    src,
    label,
    fileType,
    fileData,
    'data-id': dataId,
}: DocumentViewerProps): React.JSX.Element => {
    const fileExtension = getFileExtension({ fileType, src });

    const isSupported = SUPPORTED_EXTENSIONS.has(fileExtension ?? '');
    const isImage = IMAGE_EXTENSIONS.has(fileExtension ?? '');

    if (isFileDataAvailable(fileData, label, fileType)) {
        if (isCsvFile(fileType, label)) {
            return (
                <CsvViewer
                    fileData={fileData || ''}
                    fileName={label}
                    data-id="evidence-file-csv-viewer"
                />
            );
        }

        if (
            fileExtension === 'json' ||
            fileExtension === 'xml' ||
            fileType?.includes('text')
        ) {
            const textContent = atob(fileData || '');

            return (
                <CodeViewer
                    language={fileExtension}
                    value={textContent}
                    data-id="evidence-file-code-viewer"
                />
            );
        }
    }

    if (!isSupported) {
        return (
            <UnsupportedDocumentEmptyState
                data-id={dataId}
                extension={fileExtension ?? ''}
                src={src}
            />
        );
    }

    if (isImage) {
        return (
            <img
                src={src}
                alt={label}
                data-testid={'ImageViewer'}
                data-id={dataId}
                style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    backgroundColor: '#f5f5f5',
                }}
            />
        );
    }

    return (
        <iframe
            id={dataId}
            src={src}
            title={label}
            data-testid={'DocumentViewer'}
            data-id={dataId}
            style={{ border: 'none', width: '100%', height: '100%' }}
        />
    );
};
