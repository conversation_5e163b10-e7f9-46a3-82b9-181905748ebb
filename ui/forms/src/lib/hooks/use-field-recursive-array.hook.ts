import { useFieldArray, type UseFieldArrayReturn } from 'react-hook-form';

export interface UseFieldRecursiveArrayReturn {
    fields: UseFieldArrayReturn['fields'];
    addItem: UseFieldArrayReturn['append'];
    removeItem: UseFieldArrayReturn['remove'];
    addNestedItem: (
        parentIndex: number,
        newNestedItem: Record<string, unknown>,
    ) => void;
    getNestedFieldName: (parentIndex: number) => string;
    getNestedItems: (parentIndex: number) => unknown[];
}

/**
 * Custom hook for managing recursive array fields in forms.
 * Provides utilities for adding, removing, and managing nested items in recursive structures.
 */
export const useFieldRecursiveArray = (
    name: string,
    recursiveFieldName?: string,
): UseFieldRecursiveArrayReturn => {
    // TODO: improve types for recursive fields.
    if (!recursiveFieldName) {
        throw new Error('recursiveFieldName is required');
    }

    const { fields, append, remove, update } = useFieldArray({ name });

    const addNestedItem = (
        parentIndex: number,
        newNestedItem: Record<string, unknown>,
    ) => {
        const currentField = fields[parentIndex] as Record<string, unknown>;
        const currentNestedItems = Array.isArray(
            currentField[recursiveFieldName],
        )
            ? (currentField[recursiveFieldName] as unknown[])
            : [];

        const nestedItemWithRecursiveField = {
            ...newNestedItem,
            [recursiveFieldName]: [],
        };

        const updatedField = {
            ...currentField,
            [recursiveFieldName]: [
                ...currentNestedItems,
                nestedItemWithRecursiveField,
            ],
        };

        update(parentIndex, updatedField);
    };

    const getNestedFieldName = (parentIndex: number): string => {
        return `${name}[${parentIndex}].${recursiveFieldName}`;
    };

    const getNestedItems = (parentIndex: number): unknown[] => {
        const currentField = fields[parentIndex] as Record<string, unknown>;

        return Array.isArray(currentField[recursiveFieldName])
            ? (currentField[recursiveFieldName] as unknown[])
            : [];
    };

    return {
        fields,
        addItem: append,
        removeItem: remove,
        addNestedItem,
        getNestedFieldName,
        getNestedItems,
    };
};
