import { useCallback, useMemo } from 'react';
import {
    type SetValueConfig,
    useController,
    useFormState,
} from 'react-hook-form';
import { t } from '@globals/i18n/macro';
import { getFieldSchemaByPath } from './helpers/get-field-schema-by-path.helper';
import { useFormContext } from './hooks/use-form-context.hook';
import { useIsShownWithRegistration } from './hooks/use-is-shown-with-registration.hook';
import type { FieldMetaByType } from './types/field-meta-by-type.type';
import type { FieldSchema } from './types/field-schema.type';
import type { FieldType } from './types/field-type.type';
import type { UniversalFieldControllerWithValue } from './types/universal-field-controller-with-value.type';

export function useUniversalFieldController<T extends FieldType>(
    name: string,
    shouldUnregister = false,
): [
    UniversalFieldControllerWithValue<T>,
    ReturnType<typeof useController>['formState'],
] {
    const { control, schema, setValue: setFormValue } = useFormContext();
    const { disabled } = useFormState();
    const fieldSchema = useMemo(() => {
        return getFieldSchemaByPath(schema, name);
    }, [schema, name]);

    if (!fieldSchema) {
        throw new Error(`No field schema found for: ${name}`);
    }

    const { type, isOptional, shownIf, readOnly } = fieldSchema;

    const fieldProps = useController({
        name,
        control,
        rules: { required: !isOptional },
        disabled,
        shouldUnregister,
    });

    const isShown = useIsShownWithRegistration(name, shownIf);

    const setValue = useCallback(
        (
            value: FieldMetaByType[T]['initialValue'],
            options: SetValueConfig = {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
            },
        ) => {
            setFormValue(name, value, {
                shouldValidate: options.shouldValidate,
                shouldDirty: options.shouldDirty,
                shouldTouch: options.shouldTouch,
            });
        },
        [name, setFormValue],
    );

    return [
        {
            ...fieldProps.field,
            ...fieldProps.fieldState,
            value: fieldProps.field.value as T,
            setValue,
            fieldSchemaProps: {
                ...fieldSchema,
                readOnly: readOnly || disabled,
            } as FieldSchema,
            type,
            isShown,
            isOptional,
            required: !isOptional,
            feedback: fieldProps.fieldState.error
                ? {
                      type: 'error',
                      message:
                          fieldProps.fieldState.error.message ||
                          t`Validation error`,
                  }
                : undefined,
            optionalText: isOptional ? t`optional` : undefined,
        },
        fieldProps.formState,
    ];
}
