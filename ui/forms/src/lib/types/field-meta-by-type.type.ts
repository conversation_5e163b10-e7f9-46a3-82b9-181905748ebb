import type { CheckboxFieldProps } from '@cosmos/components/checkbox-field';
import type { CheckboxFieldGroupProps } from '@cosmos/components/checkbox-field-group';
import type { ComboboxFieldProps } from '@cosmos/components/combobox-field';
import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import type { SelectFieldProps } from '@cosmos/components/select-field';
import type { SliderFieldProps } from '@cosmos/components/slider-field';
import type { TextFieldProps } from '@cosmos/components/text-field';
import type { TextareaFieldProps } from '@cosmos/components/textarea-field';
import type { ToggleFieldProps } from '@cosmos/components/toggle-field';
import type { ToggleGroupProps } from '@cosmos/components/toggle-group';
import type { ChoiceCardProps } from '@cosmos-lab/components/choice-card';
import type { ChoiceCardGroupProps } from '@cosmos-lab/components/choice-card-group';
import type { LimitedComboboxFieldProps } from './limited-combobox-field-props.type';
import type { LimitedDatePickerFieldProps } from './limited-date-picker-field-props.type';
import type { LimitedDateRangeFieldProps } from './limited-date-range-field-props.type';
import type { LimitedFileUploadFieldProps } from './limited-file-upload-field-props.type';
import type { LimitedImageUploadFieldProps } from './limited-image-upload-field-props.type';

/**
 * Represents the metadata for each field type.
 * This interface defines the props and initialValue for each field type.
 */
export interface FieldMetaByType {
    text: {
        props: TextFieldProps;
        initialValue: TextFieldProps['value'];
    };
    textarea: {
        props: TextareaFieldProps;
        initialValue: TextareaFieldProps['value'];
    };
    checkbox: {
        props: CheckboxFieldProps;
        initialValue: CheckboxFieldProps['checked'];
    };
    checkboxGroup: {
        props: CheckboxFieldGroupProps;
        initialValue: CheckboxFieldGroupProps['value'];
    };
    select: {
        props: SelectFieldProps;
        initialValue: SelectFieldProps['defaultValue'];
    };
    combobox: {
        props: LimitedComboboxFieldProps;
        initialValue:
            | ComboboxFieldProps['defaultValue']
            | ComboboxFieldProps['defaultSelectedOptions'];
    };
    radioGroup: {
        props: RadioFieldGroupProps;
        initialValue: RadioFieldGroupProps['value'];
    };
    slider: {
        props: SliderFieldProps;
        initialValue: SliderFieldProps['defaultValue'];
    };
    date: {
        props: LimitedDatePickerFieldProps;
        initialValue: LimitedDatePickerFieldProps['defaultValue'];
    };
    dateRange: {
        props: LimitedDateRangeFieldProps;
        initialValue: LimitedDateRangeFieldProps['value'];
    };
    file: {
        props: LimitedFileUploadFieldProps;
        initialValue: LimitedFileUploadFieldProps['initialFiles'];
    };
    image: {
        props: LimitedImageUploadFieldProps;
        initialValue: LimitedImageUploadFieldProps['initialFiles'];
    };
    choiceCard: {
        props: ChoiceCardProps;
        initialValue: ChoiceCardProps['checked'];
    };
    choiceCardGroup: {
        props: ChoiceCardGroupProps;
        initialValue: ChoiceCardGroupProps['value'];
    };
    toggle: {
        props: ToggleFieldProps;
        initialValue: ToggleFieldProps['checked'];
    };
    toggleGroup: {
        props: ToggleGroupProps;
        initialValue: ToggleGroupProps['selectedOption'];
    };
}
