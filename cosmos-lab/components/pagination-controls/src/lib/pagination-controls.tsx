import { useMemo, useState } from 'react';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { dimensionSm } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { StyledPaginationControlsDiv } from './styles/styled-pagination-controls-div.style';
import type { PageAction } from './types';
import type { PaginationControlsProps } from './types/pagination-controls.type';

/**
 * The PaginationControls component provides navigation controls for paginated content with page size selection, page navigation buttons, and current page indicators.
 *
 * [PaginationControls in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=46183-177879&t=oCQvDh35aqAC6r0M-4).
 */
export const PaginationControls = ({
    'data-id': dataId = 'pagination-controls',
    total,
    hidePageSizeOptions = false,
    hidePageCount = false,
    pageSizeOptions = DEFAULT_PAGE_SIZE_OPTIONS,
    initialPage = DEFAULT_PAGE,
    pageSize = DEFAULT_PAGE_SIZE,
    cantDetermineTotalCount = false,
    onPageSizeChange,
    onPageChange,
}: PaginationControlsProps): React.JSX.Element => {
    const [currentPage, setCurrentPage] = useState(initialPage);

    const pageSizeSelectOptions = pageSizeOptions.map(
        (value): ListBoxItemData => {
            return {
                id: `${value}-per-page`,
                label: t`${value} per page`,
                value: `${value}`,
            };
        },
    );

    const handleOnPageSizeChange = ({ value }: ListBoxItemData) => {
        if (onPageSizeChange) {
            onPageSizeChange(Number(value));
        }
    };

    const calculateTargetPage = (action: PageAction): number => {
        switch (action) {
            case 'FIRST_PAGE': {
                return 1;
            }
            case 'PREVIOUS_PAGE': {
                return currentPage - 1;
            }
            case 'NEXT_PAGE': {
                return currentPage + 1;
            }
            case 'LAST_PAGE': {
                return Math.ceil(total / pageSize);
            }
            default: {
                return currentPage;
            }
        }
    };

    const handleOnPageChange = (action: PageAction) => {
        if (!onPageChange) {
            return;
        }

        const targetPage = calculateTargetPage(action);

        setCurrentPage(targetPage);
        onPageChange(targetPage);
    };

    const pageCountString = useMemo(() => {
        const startRow = (currentPage - 1) * pageSize + 1;
        const endRow = Math.min(currentPage * pageSize, total);

        if (cantDetermineTotalCount) {
            return t`${startRow}-${endRow} of many`;
        }

        return t`${startRow}-${endRow} of ${total}`;
    }, [currentPage, pageSize, total, cantDetermineTotalCount]);

    const hasPreviousPage = currentPage > 1;
    const hasNextPage = currentPage < Math.ceil(total / pageSize);

    return (
        <StyledPaginationControlsDiv
            data-testid="PaginationControls"
            data-id={dataId}
        >
            <ActionStack
                gap={dimensionSm}
                stacks={[
                    {
                        id: 'footer-left-actions',
                        actions: [
                            !hidePageSizeOptions && {
                                id: 'footer-left-actions-set-page-size',
                                /**
                                 * TODO: update this to use select-field when available.
                                 * Ticket: https://drata.atlassian.net/browse/ENG-72892.
                                 */
                                actionType: 'select',
                                typeProps: {
                                    'aria-labelledby':
                                        'non-working-id-see-ENG-72892',
                                    id: 'pagination-controls-page-size',
                                    name: 'cosmos-select',
                                    options: pageSizeSelectOptions,
                                    placeholderText: t`${pageSize} per page`,
                                    required: false,
                                    onChange: handleOnPageSizeChange,
                                },
                            },
                        ].filter(Boolean) as Action[],
                    },
                    {
                        id: 'page-navigation-buttons-and-page-count',
                        actions: [
                            !hidePageCount && {
                                id: 'page-count',
                                actionType: 'text',
                                typeProps: {
                                    type: 'body',
                                    size: '100',
                                    children: pageCountString,
                                    showExtraPadding: true,
                                },
                            },
                            {
                                id: 'go-to-first-page-button',
                                actionType: 'button',
                                typeProps: {
                                    label: t`'Go to first page`,
                                    startIconName: 'DoubleArrowLeft',
                                    isIconOnly: true,
                                    level: 'tertiary',
                                    size: 'sm',
                                    colorScheme: 'neutral',
                                    cosmosUseWithCaution_isDisabled:
                                        !hasPreviousPage,
                                    onClick: () => {
                                        handleOnPageChange('FIRST_PAGE');
                                    },
                                },
                            },
                            {
                                id: 'go-to-previous-page-button',
                                actionType: 'button',
                                typeProps: {
                                    label: t`Go to previous page`,
                                    startIconName: 'ChevronLeft',
                                    isIconOnly: true,
                                    level: 'tertiary',
                                    size: 'sm',
                                    colorScheme: 'neutral',
                                    cosmosUseWithCaution_isDisabled:
                                        !hasPreviousPage,
                                    onClick: () => {
                                        handleOnPageChange('PREVIOUS_PAGE');
                                    },
                                },
                            },
                            {
                                id: 'go-to-next-page-button',
                                actionType: 'button',
                                typeProps: {
                                    label: t`Go to next page`,
                                    startIconName: 'ChevronRight',
                                    isIconOnly: true,
                                    level: 'tertiary',
                                    size: 'sm',
                                    colorScheme: 'neutral',
                                    cosmosUseWithCaution_isDisabled:
                                        !hasNextPage,
                                    onClick: () => {
                                        handleOnPageChange('NEXT_PAGE');
                                    },
                                },
                            },
                            {
                                id: 'go-to-last-page-button',
                                actionType: 'button',
                                typeProps: {
                                    label: t`Go to last page`,
                                    startIconName: 'DoubleArrowRight',
                                    isIconOnly: true,
                                    level: 'tertiary',
                                    size: 'sm',
                                    colorScheme: 'neutral',
                                    cosmosUseWithCaution_isDisabled:
                                        !hasNextPage,
                                    onClick: () => {
                                        handleOnPageChange('LAST_PAGE');
                                    },
                                },
                            },
                        ].filter(Boolean) as Action[],
                    },
                ]}
            />
        </StyledPaginationControlsDiv>
    );
};
