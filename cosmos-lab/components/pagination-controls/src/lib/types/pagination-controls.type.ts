export interface PaginationControlsProps {
    /**
     * Unique testing ID for this element.
     * Used for automated testing and debugging purposes.
     */
    'data-id'?: string;

    /**
     * The total number of items across all pages.
     * Used to calculate the total number of pages and display information.
     */
    total: number;

    /**
     * Available options for the number of items per page.
     * Typically an array like [10, 25, 50, 100].
     * If not provided, default options will be used.
     */
    pageSizeOptions?: number[];

    /**
     * Hide the page size options.
     */
    hidePageSizeOptions?: boolean;

    /**
     * The current page number (one-based).
     * For example, the first page is 1, the second page is 2, etc.
     * If not provided, defaults to the first page.
     */
    initialPage?: number;

    /**
     * The current number of items displayed per page.
     * If not provided, defaults to the first option in pageSizeOptions.
     */
    pageSize?: number;

    /**
     * Callback function triggered when the user changes the page.
     *
     * @param page - The new page number (one-based).
     */
    onPageChange?: (page: number) => void;

    /**
     * Callback function triggered when the user changes the page size.
     *
     * @param size - The new page size.
     */
    onPageSizeChange?: (size: number) => void;

    /**
     * Hide the page numbers.
     */
    hidePageCount?: boolean;

    /**
     * If `true`, the total count of rows will not be displayed in the pagination controls.
     */
    cantDetermineTotalCount?: boolean;
}
