import { useMemo } from 'react';
import { styled } from 'styled-components';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import {
    dataConicCelebrate,
    dimension2x,
    dimension3x,
    dimension4x,
    dimension6x,
    dimension36x,
    neutralBackgroundModerate,
} from '@cosmos/constants/tokens';
import { getGaugeColor, getStrokeDashArray } from './helpers';
import type { DataGaugeProps } from './types';

const DEFAULT_DIMENSIONS = 144;
const STROKE_WIDTH = 16;
const ROTATION_OFFSET = 135;

const StyledPercentageText = styled.div`
    position: absolute;
    top: calc(50% - ${dimension2x});
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
`;

const StyledTextContainer = styled.div`
    position: absolute;
    top: calc(50% + ${dimension3x});
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
    padding: 0 ${dimension6x};
    box-sizing: border-box;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: normal;
    text-align: center;
    width: 100%;
`;

const StyledTextBox = styled(Box)`
    text-align: center;
`;

const StyledGaugeSvg = styled.svg`
    width: ${dimension36x};
    min-width: ${dimension36x};
    height: ${dimension36x};
    align-self: center;
    position: relative;
`;

const StyledGaugeMask = styled.div`
    width: ${dimension36x};
    height: ${dimension36x};
    background-image: ${dataConicCelebrate};
`;

const StyledGaugeCircle = styled.circle<{
    $strokeColor: string;
    $strokeDasharray: string;
}>`
    fill: transparent;
    stroke: ${({ $strokeColor }) => $strokeColor};
    stroke-width: ${dimension4x};
    stroke-dasharray: ${({ $strokeDasharray }) => $strokeDasharray};
`;

/**
 * The DataGauge component displays progress toward a goal or performance metrics as a circular gauge chart with percentage values, color-coded indicators, and optional rainbow celebration effects.
 *
 * [DataGauge in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=43525-256261&t=GFojvxS2b2W7WsNi-11).
 */
const DataGauge = ({
    value,
    unit,
    hasRainbow = false,
    isDisabled = false,
}: DataGaugeProps): React.JSX.Element => {
    const {
        normalizedValue,
        progress,
        radius,
        circumference,
        arcLength,
        gaugeColor,
        filled,
        empty,
    } = useMemo(() => {
        const clampedValue = Math.min(100, Math.max(0, value));
        const calculatedProgress = clampedValue / 100;
        const calculatedRadius = DEFAULT_DIMENSIONS / 2 - STROKE_WIDTH / 2;
        const calculatedCircumference = 2 * Math.PI * calculatedRadius;
        const calculatedArcLength = calculatedCircumference * 0.75;
        const color = getGaugeColor(clampedValue);
        const { calculatedFilledLength, calculatedEmptyLength } =
            getStrokeDashArray(
                calculatedProgress,
                calculatedCircumference,
                calculatedArcLength,
            );

        return {
            normalizedValue: clampedValue,
            progress: calculatedProgress,
            radius: calculatedRadius,
            circumference: calculatedCircumference,
            arcLength: calculatedArcLength,
            gaugeColor: color,
            filled: calculatedFilledLength,
            empty: calculatedEmptyLength,
        };
    }, [value]);

    if (isDisabled) {
        return (
            <StyledGaugeSvg
                aria-hidden="true"
                role="meter"
                aria-valuenow={0}
                aria-valuemin={0}
                aria-valuemax={100}
                aria-valuetext="Disabled"
            >
                <StyledGaugeCircle
                    cx={DEFAULT_DIMENSIONS / 2}
                    cy={DEFAULT_DIMENSIONS / 2}
                    r={radius}
                    $strokeColor={neutralBackgroundModerate}
                    $strokeDasharray={arcLength.toString()}
                    transform={`rotate(${ROTATION_OFFSET} ${DEFAULT_DIMENSIONS / 2} ${DEFAULT_DIMENSIONS / 2})`}
                />
                <foreignObject
                    x="0"
                    y="0"
                    width={DEFAULT_DIMENSIONS}
                    height={DEFAULT_DIMENSIONS}
                >
                    <Stack
                        pt="12x"
                        direction="column"
                        align="center"
                        justify="center"
                    >
                        <Text type="headline" size="600" colorScheme="neutral">
                            —
                        </Text>
                    </Stack>
                </foreignObject>
            </StyledGaugeSvg>
        );
    }

    return (
        <StyledGaugeSvg
            aria-hidden="true"
            role="meter"
            aria-valuenow={normalizedValue}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-valuetext={`${normalizedValue}%`}
            data-testid="DataGauge"
            data-id="GLJVxI86"
        >
            {hasRainbow && normalizedValue === 100 ? (
                <>
                    <mask id="rainbowMask">
                        <rect
                            width={DEFAULT_DIMENSIONS}
                            height={DEFAULT_DIMENSIONS}
                            fill="black"
                        />
                        <StyledGaugeCircle
                            cx={DEFAULT_DIMENSIONS / 2}
                            cy={DEFAULT_DIMENSIONS / 2}
                            r={radius}
                            $strokeColor="white"
                            $strokeDasharray={`${arcLength} ${circumference - arcLength}`}
                            transform={`rotate(${ROTATION_OFFSET} ${DEFAULT_DIMENSIONS / 2} ${DEFAULT_DIMENSIONS / 2})`}
                        />
                    </mask>
                    <foreignObject
                        x="0"
                        y="0"
                        width={DEFAULT_DIMENSIONS}
                        height={DEFAULT_DIMENSIONS}
                        mask="url(#rainbowMask)"
                    >
                        <StyledGaugeMask />
                    </foreignObject>
                </>
            ) : (
                <>
                    <StyledGaugeCircle
                        cx={DEFAULT_DIMENSIONS / 2}
                        cy={DEFAULT_DIMENSIONS / 2}
                        r={radius}
                        $strokeColor={neutralBackgroundModerate}
                        $strokeDasharray={empty}
                        transform={`rotate(${ROTATION_OFFSET + 360 * progress * 0.75} ${DEFAULT_DIMENSIONS / 2} ${DEFAULT_DIMENSIONS / 2})`}
                    />
                    <StyledGaugeCircle
                        cx={DEFAULT_DIMENSIONS / 2}
                        cy={DEFAULT_DIMENSIONS / 2}
                        r={radius}
                        $strokeColor={gaugeColor}
                        $strokeDasharray={filled}
                        transform={`rotate(${ROTATION_OFFSET} ${DEFAULT_DIMENSIONS / 2} ${DEFAULT_DIMENSIONS / 2})`}
                    />
                </>
            )}
            <foreignObject
                x="0"
                y="0"
                width={DEFAULT_DIMENSIONS}
                height={DEFAULT_DIMENSIONS}
            >
                <StyledPercentageText>
                    <Text
                        type="headline"
                        size="600"
                        colorScheme={value === 0 ? 'critical' : 'neutral'}
                    >
                        {normalizedValue}%
                    </Text>
                </StyledPercentageText>

                <StyledTextContainer>
                    <StyledTextBox px="2x">
                        <Text size="100" colorScheme="neutral" align="center">
                            {unit}
                        </Text>
                    </StyledTextBox>
                </StyledTextContainer>
            </foreignObject>
        </StyledGaugeSvg>
    );
};

export { DataGauge };
