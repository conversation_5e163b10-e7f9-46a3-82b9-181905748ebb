import type * as React from 'react';
import type { IconName } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { DataDonutProps } from '../data-donut.type';
import { DataDonutEmptyStateSvg } from './data-donut-empty-state-svg.component';

export interface DataDonutEmptyStateProps {
    'data-id': string;
    diameter: number;
    radius: number;
    strokeWidth: number;
    totalValueString: string;
    unit: string;
    iconName?: IconName;
    hideTotal?: boolean;
    hideUnit?: boolean;
    donutAriaLabel: string;
    text: string;
    position: DataDonutProps['legendPosition'];
    align: DataDonutProps['align'];
    size: DataDonutProps['size'];
}

export const DataDonutEmptyState = ({
    'data-id': dataId,
    diameter,
    radius,
    strokeWidth,
    totalValueString,
    unit,
    iconName,
    hideTotal,
    hideUnit,
    donutAriaLabel,
    text,
    position,
    align,
    size,
}: DataDonutEmptyStateProps): React.JSX.Element => {
    const containerDirection = position === 'bottom' ? 'column' : 'row';

    return (
        <Stack
            width="100%"
            align={align}
            data-testid="DataDonutEmptyState"
            data-id={dataId}
        >
            <Stack gap="4x" align="center" direction={containerDirection}>
                <Stack justify="center">
                    <DataDonutEmptyStateSvg
                        data-id={dataId}
                        diameter={diameter}
                        radius={radius}
                        strokeWidth={strokeWidth}
                        size={size}
                        totalValueString={totalValueString}
                        unit={unit}
                        iconName={iconName}
                        hideTotal={hideTotal}
                        hideUnit={hideUnit}
                        donutAriaLabel={donutAriaLabel}
                    />
                </Stack>
                {size !== 'sm' && (
                    <Stack width="100%" justify="center">
                        <Text size="200" shouldWrap={false}>
                            {text}
                        </Text>
                    </Stack>
                )}
            </Stack>
        </Stack>
    );
};
