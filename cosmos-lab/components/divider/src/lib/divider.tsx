import { styled } from 'styled-components';
import {
    borderWidthMd,
    borderWidthSm,
    neutralBorderFaded,
} from '@cosmos/constants/tokens';
// eslint-disable-next-line no-restricted-imports -- This is the only official use case for @radix-ui/react-separator
import { Separator } from '@radix-ui/react-separator';

export type DividerSize = 'sm' | 'md';
export type DividerOrientation = 'horizontal' | 'vertical';

const SIZES = {
    sm: borderWidthSm,
    md: borderWidthMd,
} as const satisfies Record<DividerSize, string>;

export interface DividerProps {
    size?: DividerSize;
    orientation?: DividerOrientation;
}

const StyledSeparator = styled(Separator)<{ $size: DividerSize }>`
    background-color: ${neutralBorderFaded};
    ${({ $size }) => `height: ${SIZES[$size]}; width: 100%;`}

    &[data-orientation='vertical'] {
        width: ${({ $size }) => SIZES[$size]};
        height: auto;
        align-self: stretch;
    }
`;

/**
 * The Divider component provides visual separation between content sections with customizable orientation and sizing for improved content organization and hierarchy.
 *
 * [Divider in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=45945-89467&t=KLe6gHCZFs2veyOB-4).
 */
export const Divider = ({
    size = 'sm',
    orientation = 'horizontal',
}: DividerProps): React.JSX.Element => {
    return (
        <StyledSeparator
            $size={size}
            orientation={orientation}
            data-testid="Divider"
            data-id="DjHUHY0w"
        />
    );
};
