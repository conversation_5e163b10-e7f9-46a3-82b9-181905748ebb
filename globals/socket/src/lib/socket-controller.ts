import type Pusher from 'pusher-js';
import { IterableWeakSet } from 'weakref';
import { sharedAuthController } from '@controllers/auth';
import type { SocketEvent } from '@drata/enums';
import { sharedConfigController } from '@globals/config';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedCurrentUserController } from '@globals/current-user';
import {
    action,
    makeAutoObservable,
    reaction,
    runInAction,
} from '@globals/mobx';

export type ChannelTypes = 'user' | 'company' | 'auditHub';

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- need for generics
type Callback = (...args: any[]) => any;

type CallbackCollection = Partial<
    Record<ChannelTypes, Partial<Record<string, IterableWeakSet<Callback>>>>
>;

let pusher: Pusher | null = null;

class SocketController {
    callbackCollection: CallbackCollection = {};
    isInitialized = false;
    isAuditHubInitialized = false;

    constructor() {
        makeAutoObservable(this, undefined, { autoBind: true });
    }

    private validateCoreConfig() {
        const { key, cluster, channelPrefix } =
            sharedConfigController.configs.pusher ?? {};
        const { api } = sharedConfigController.configs.url ?? {};
        const { accountId } = sharedCurrentCompanyController.company ?? {};
        const { id: userId } = sharedCurrentUserController.user ?? {};

        if (!key) {
            throw new Error('missing pusher api key');
        }
        if (!cluster) {
            throw new Error('missing pusher cluster key');
        }
        if (!channelPrefix) {
            throw new Error('missing pusher channelPrefix key');
        }
        if (!api) {
            throw new Error('missing api url');
        }
        if (!accountId) {
            throw new Error('missing company accountId');
        }
        if (!userId) {
            throw new Error('missing user id');
        }

        return { key, cluster, channelPrefix, api, accountId, userId };
    }

    private async getPusherInstance(key: string, cluster: string, api: string) {
        if (pusher) {
            return pusher;
        }

        try {
            const module = await import('pusher-js/with-encryption');

            pusher ??= new module.default(key, {
                cluster,
                channelAuthorization: {
                    transport: 'ajax',
                    endpoint: `${api}/socket/channel`,
                    headersProvider: action(() => ({
                        Authorization: `Bearer ${sharedAuthController.accessToken}`,
                    })),
                },
            });

            return pusher;
        } catch {
            throw new Error('Failed to load Pusher module');
        }
    }

    async initialize() {
        const { key, cluster, channelPrefix, api, accountId, userId } =
            this.validateCoreConfig();

        pusher = await this.getPusherInstance(key, cluster, api);

        const accountChannel = pusher.subscribe(`${channelPrefix}${accountId}`);
        const userChannel = pusher.subscribe(
            `${channelPrefix}${accountId}.${userId}`,
        );

        accountChannel.bind_global(this.accountHandler.bind(this));
        userChannel.bind_global(this.userHandler.bind(this));

        runInAction(() => {
            this.isInitialized = true;
        });
    }

    async initializeAuditHubChannel(requestId: number) {
        const { key, cluster, channelPrefix, api, accountId } =
            this.validateCoreConfig();

        pusher = await this.getPusherInstance(key, cluster, api);

        const auditHubChannel = pusher.subscribe(
            `${channelPrefix}${accountId}.${requestId}`,
        );

        auditHubChannel.bind_global(this.auditHubHandler.bind(this));

        runInAction(() => {
            this.isAuditHubInitialized = true;
        });
    }

    async unsubscribeFromAuditHubChannel(requestId: number) {
        const { key, cluster, channelPrefix, api, accountId } =
            this.validateCoreConfig();

        pusher = await this.getPusherInstance(key, cluster, api);

        pusher.unsubscribe(`${channelPrefix}${accountId}.${requestId}`);

        runInAction(() => {
            this.isAuditHubInitialized = false;
        });
    }

    subscribe({
        eventName,
        channelType,
        callback,
    }: {
        eventName: SocketEvent;
        channelType: ChannelTypes;
        callback: Callback;
    }) {
        this.callbackCollection[channelType] ??= {};
        this.callbackCollection[channelType][eventName] ??=
            new IterableWeakSet();
        this.callbackCollection[channelType][eventName].add(callback);
    }

    private handleEvent(
        channelType: ChannelTypes,
        event: string,
        data: unknown,
    ) {
        const callbacks = this.callbackCollection[channelType]?.[event];

        if (!callbacks) {
            return;
        }

        for (const cb of callbacks) {
            try {
                cb(data);
            } catch (error) {
                console.error(
                    `Error in ${channelType} socket callback:`,
                    error,
                );
            }
        }
    }

    private accountHandler(event: string, data: unknown) {
        this.handleEvent('company', event, data);
    }

    private userHandler(event: string, data: unknown) {
        this.handleEvent('user', event, data);
    }

    private auditHubHandler(event: string, data: unknown) {
        this.handleEvent('auditHub', event, data);
    }
}

export const sharedSocketController = new SocketController();

reaction(
    () => {
        const { accessToken } = sharedAuthController;
        const userId = sharedCurrentUserController.user?.id;
        const accountId = sharedCurrentCompanyController.company?.accountId;
        const apiUrl = sharedConfigController.configs.url?.api;
        const pusherCluster = sharedConfigController.configs.pusher?.cluster;
        const pusherKey = sharedConfigController.configs.pusher?.key;
        const pusherChannelPrefix =
            sharedConfigController.configs.pusher?.channelPrefix;

        return Boolean(
            accessToken &&
                userId &&
                accountId &&
                apiUrl &&
                pusherCluster &&
                pusherKey &&
                pusherChannelPrefix,
        );
    },
    action((canInitialize: boolean) => {
        if (canInitialize) {
            sharedSocketController.initialize().catch(() => {
                console.error('Failed to initialize socket');
            });
        }
    }),
);
