import { sharedCurrentCompanyController } from '@globals/current-company';
import { makeAutoObservable } from '@globals/mobx';

class EntitlementFlagController {
    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return sharedCurrentCompanyController.isLoading;
    }

    get flags(): {
        [key: string]: unknown;
    }[] {
        return sharedCurrentCompanyController.company?.entitlements ?? [];
    }

    get isTrustCenterEnabled(): boolean {
        return this.#hasEntitlementFlag('TRUST_CENTER');
    }

    get isCustomWorkflowsEnabled(): boolean {
        return this.#hasEntitlementFlag('CUSTOM_WORKFLOWS');
    }

    get isWelcomeExperienceEnabled(): boolean {
        return this.#hasEntitlementFlag('WELCOME_EXPERIENCE');
    }

    get isMultipleWorkspacesEnabled(): boolean {
        return this.#hasEntitlementFlag('MULTIPLE_PRODUCTS');
    }

    get isUserAccessReviewEnabled(): boolean {
        return this.#hasEntitlementFlag('ACCESS_REVIEW');
    }

    get isCustomFrameworksEnabled(): boolean {
        return this.#hasEntitlementFlag('CUSTOM_FRAMEWORKS');
    }

    get isCustomTestEnabled(): boolean {
        return this.#hasEntitlementFlag('CUSTOM_CONNECTIONS_AND_TESTS');
    }

    get isCustomFieldsEnabled(): boolean {
        return this.#hasEntitlementFlag('CUSTOM_FIELDS_AND_FORMULAS');
    }

    get isVendorRiskManagementProEnabled(): boolean {
        return this.#hasEntitlementFlag('TPRM_PRO');
    }

    get isRiskManagementEnabled(): boolean {
        return this.#hasEntitlementFlag('RISK_MANAGEMENT');
    }

    get isCustomControlsEnabled(): boolean {
        return this.#hasEntitlementFlag('CUSTOM_CONTROLS');
    }

    get companyEntitlementsWithQuotas(): string[] {
        const companyEntitlements = this.flags as {
            type: string;
        }[];

        const companyEntitlementTypes = companyEntitlements.map(
            ({ type }: { type: string }) => type,
        );

        /**
         * This was taken from the old UI and was named DEFAULT_ENTITLEMENTS_QUOTA_USAGE
         * See: `src/views/pages/AccountSettings/PlanAndUsage/Usage/usage.constants.ts`
         * I'm assuming that these are the entitlements that have fixed quotas.
         */
        const ENTITLEMENTS_WITH_QUOTA = [
            'MULTIPLE_PRODUCTS',
            'CUSTOM_FRAMEWORKS',
            'ADAPTIVE_AUTOMATION',
        ];

        return companyEntitlementTypes.filter((entitlement) =>
            ENTITLEMENTS_WITH_QUOTA.includes(entitlement),
        );
    }

    get isMapControlsTestsEnabled(): boolean {
        return this.#hasEntitlementFlag('MAP_CONTROLS_TESTS');
    }

    get isDownloadControlEnabled(): boolean {
        return this.#hasEntitlementFlag('DOWNLOAD_CONTROL');
    }

    get isAIDataShareEnabled(): boolean {
        return this.#hasEntitlementFlag('AI_DATA_SHARE');
    }

    get hasCustomConnectionsAndTests(): boolean {
        return this.#hasEntitlementFlag('CUSTOM_CONNECTIONS_AND_TESTS');
    }

    /**
     * DO NOT USE DIRECTLY. Use the specific getters instead.
     *
     * Check if the company has a specific entitlement flag.
     */
    #hasEntitlementFlag = (flag: string): boolean => {
        return this.flags.some(({ type = '' }) => type === flag);
    };
}
export const sharedEntitlementFlagController = new EntitlementFlagController();
