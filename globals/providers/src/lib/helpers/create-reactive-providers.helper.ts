import { computed, runInAction } from '@globals/mobx';
import type { BaseProvider } from '../types/base-provider.type';
import type { Provider } from '../types/provider.type';
import { getEnabledProviders } from './get-enabled-providers.helper';

/**
 * Creates a reactive providers object that automatically updates when feature flags change.
 * Uses MobX computed values and JavaScript Proxy to provide seamless reactivity.
 */
export const createReactiveProviders = (
    allProviders: Record<Provider, BaseProvider>,
): Record<Provider, BaseProvider> => {
    // Create a computed value that reacts to feature flag changes
    const computedProviders = computed(() => {
        return getEnabledProviders(allProviders);
    });

    // Return a Proxy that provides reactive access to the filtered providers
    return new Proxy({} as Record<Provider, BaseProvider>, {
        get(_target, prop) {
            const currentProviders = runInAction(() => computedProviders.get());

            return currentProviders[prop as Provider];
        },
        ownKeys() {
            const currentProviders = runInAction(() => computedProviders.get());

            return Object.keys(currentProviders);
        },
        has(_target, prop) {
            const currentProviders = runInAction(() => computedProviders.get());

            return prop in currentProviders;
        },
        getOwnPropertyDescriptor(_target, prop) {
            const currentProviders = runInAction(() => computedProviders.get());

            if (prop in currentProviders) {
                return {
                    enumerable: true,
                    configurable: true,
                    writable: false,
                    value: currentProviders[prop as Provider],
                };
            }

            return undefined;
        },
    });
};
