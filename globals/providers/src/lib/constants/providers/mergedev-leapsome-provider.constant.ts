import leapsomeLogo from '@assets/img/company-logos/leapsome/Leapsome.svg';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { action } from '@globals/mobx';
import type { BaseProvider } from '../../types/base-provider.type';

export const MERGEDEV_LEAPSOME: BaseProvider = {
    id: 'MERGEDEV_LEAPSOME',
    name: 'Leapsome',
    companyName: 'Leapsome',
    companyUrl: 'https://www.leapsome.com/',
    logo: leapsomeLogo,
    providerTypes: ['HRIS'],
    getIsEnabled: action(
        () => sharedFeatureAccessModel.isMergedevLeapsomeEnabled,
    ),
} as const;
