import { isError } from 'lodash-es';
import type { CreateTicketPayload } from '@controllers/create-ticket';
import { snackbarController } from '@controllers/snackbar';
import { ticketsControllerCreateTicketMutation } from '@globals/api-sdk/queries';
import type { TicketsCreateRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedAccessReviewActivePeriodUserTicketsController } from './access-review-tickets-controller';

//TODO: review this controller on https://drata.atlassian.net/browse/ENG-70832
class AccessReviewTicketCreationController {
    constructor() {
        makeAutoObservable(this);
    }
    /**
     * Mutation for creating tickets shared between access review and controls.
     */
    createAccessReviewTicketMutation = new ObservedMutation(
        ticketsControllerCreateTicketMutation,
    );

    get isCreating(): boolean {
        return this.createAccessReviewTicketMutation.isPending;
    }

    get hasError(): boolean {
        return this.createAccessReviewTicketMutation.hasError;
    }

    get error(): Error | null {
        return this.createAccessReviewTicketMutation.error;
    }

    /**
     * Creates a ticket for a specific access review user.
     */
    createAccessReviewTicket = (
        payload: CreateTicketPayload,
        params: {
            applicationId: number;
            periodId: number;
            userId: number;
        },
    ): Promise<void> => {
        const { currentWorkspaceId } = sharedWorkspacesController;

        const requestData: TicketsCreateRequestDto = {
            ...payload,
            relatedEntityIds: [params.userId],
            relatedEntityEnum: 'APPLICATION_USER',
            workspaceId: currentWorkspaceId || undefined,
        };

        return this.createAccessReviewTicketMutation
            .mutateAsync({
                body: requestData,
            })
            .then(() => {
                sharedAccessReviewActivePeriodUserTicketsController.ticketsInProgressForAccessReviewUsersQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'access-review-ticket-create-success',
                    props: {
                        title: t`Access review ticket created successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch((error) => {
                snackbarController.addSnackbar({
                    id: 'access-review-ticket-create-error',
                    props: {
                        title: t`Failed to create access review ticket`,
                        description: isError(error)
                            ? error.message
                            : t`Please try again`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedAccessReviewActivePeriodUserTicketCreationController =
    new AccessReviewTicketCreationController();
