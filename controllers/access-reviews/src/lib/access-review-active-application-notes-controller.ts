import { isNil } from 'lodash-es';
import type {
    AttachmentConfig,
    INotesController,
    NoteCreateInput,
    NotesLabels,
    NoteUpdateInput,
    StandardNote,
} from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerGetApplicationPeriodOptions,
    accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    accessReviewPeriodControllerPostReviewPeriodNoteMutation,
    accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    accessReviewUserControllerDownloadPeriodApplicationNoteFileOptions,
} from '@globals/api-sdk/queries';
import type { AccessReviewNoteResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class AccessReviewActiveApplicationNotesController implements INotesController {
    activeApplicationQuery = new ObservedQuery(
        accessReviewApplicationControllerGetApplicationPeriodOptions,
    );
    activeNoteCreateMutation = new ObservedMutation(
        accessReviewPeriodControllerPostReviewPeriodNoteMutation,
    );
    activeNoteUpdateMutation = new ObservedMutation(
        accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    );
    activeNoteDeleteMutation = new ObservedMutation(
        accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    );
    activeDownloadAttachmentQuery = new ObservedQuery(
        accessReviewUserControllerDownloadPeriodApplicationNoteFileOptions,
    );

    periodId: string | null = null;
    reviewAppId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Standardized notes list per INotesController.
     */
    get notes(): StandardNote[] {
        return (this.activeApplicationQuery.data?.application.notes ?? []).map(
            (note: AccessReviewNoteResponseDto) => {
                return {
                    ...note,
                    id: String(note.id),
                    comment: note.message || '',
                    createdAt: note.sentAt || '',
                    updatedAt: note.sentAt || '',
                    owner: {
                        id: String(note.ownerId),
                        firstName: note.ownerName,
                        lastName: '',
                        avatarUrl: note.ownerAvatar,
                    },
                    files: note.files,
                };
            },
        );
    }

    /**
     * ========== UI Configuration ==========.
     */
    get labels(): NotesLabels {
        return {
            title: t`Internal notes`,
            subtitle: t`Add any feedback or questions you want to track for this application. Reviewers will receive an email notification. These messages are not shared with auditors.`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details for this application.`,
            readOnlyEmptyStateDescription: t`Follow any feedback or questions about this application. These messages are not shared with auditors.`,
        };
    }

    get attachments(): AttachmentConfig {
        return {
            showAddAttachment: 'modal',
            acceptedFormats: [
                'pdf',
                'docx',
                'odt',
                'xlsx',
                'ods',
                'pptx',
                'odp',
                'gif',
                'jpeg',
                'png',
                'jpg',
            ],
            allowMultipleFiles: true,
        };
    }

    get maxNoteCharacters(): number {
        return 191;
    }
    get hasSourceInput(): boolean {
        return false;
    }

    get enableAuditorOnlyEditing(): boolean {
        return false;
    }
    get showUnreadIndicators(): boolean {
        return false;
    }
    get enableReadStatusTracking(): boolean {
        return false;
    }
    get documentActive(): null {
        return null;
    }

    get total(): number {
        return this.activeApplicationQuery.data?.application.notes.length ?? 0;
    }

    get isLoading(): boolean {
        return this.activeApplicationQuery.isLoading;
    }

    get applicationReviewStatus(): string | null {
        return this.activeApplicationQuery.data?.application.status ?? null;
    }

    get isReadOnly(): boolean {
        return (
            this.applicationReviewStatus === 'COMPLETE' ||
            !sharedFeatureAccessModel.isAccessReviewManageEnabled
        );
    }

    get isUploading(): boolean {
        return (
            this.activeNoteCreateMutation.isPending ||
            this.activeNoteUpdateMutation.isPending
        );
    }

    get 'data-id'(): string {
        return 'access-review-active-app-notes';
    }

    loadNotes = ({
        periodId,
        reviewAppId,
    }: {
        periodId?: string;
        reviewAppId?: string;
    }): void => {
        if (isNil(periodId) || isNil(reviewAppId)) {
            throw new Error(t`Period ID and App ID params are required`);
        }
        this.periodId = periodId;
        this.reviewAppId = reviewAppId;

        this.activeApplicationQuery.load({
            path: {
                periodId: Number(periodId),
                reviewAppId: Number(reviewAppId),
            },
        });
    };

    /**
     * ========== Infinite scrolling properties ==========.
     */
    get hasMore(): boolean {
        // No pagination support yet
        return false;
    }

    get isLoadingMore(): boolean {
        // No pagination support yet
        return false;
    }

    loadMore = (): void => {
        // No pagination support yet - does nothing
    };

    /**
     * ========== CRUD Operations ==========.
     */
    createNote(values: NoteCreateInput, onSuccess: () => void): void {
        if (!this.periodId || !this.reviewAppId) {
            throw new Error(t`Period ID and App ID are required`);
        }
        const timestamp = new Date().toISOString();

        this.activeNoteCreateMutation
            .mutateAsync({
                path: {
                    periodId: Number(this.periodId),
                },
                body: {
                    comment: values.comment || '',
                    'files[]': (values.files ?? []) as (File | Blob)[],
                    reviewPeriodApplicationId: this.reviewAppId,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeApplicationQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-application-note-success`,
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess();
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-application-note-error`,
                    props: {
                        title: t`Unable to create note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    }

    updateNote(
        noteId: string,
        values: NoteUpdateInput,
        onSuccess: () => void,
    ): void {
        const timestamp = new Date().toISOString();

        this.activeNoteUpdateMutation
            .mutateAsync({
                path: {
                    noteId,
                    periodId: Number(this.periodId),
                    reviewAppId: Number(this.reviewAppId),
                },
                body: {
                    comment: values.comment || '',
                    'files[]': (values.files ?? []) as (File | Blob)[],
                    filesToDelete: values.filesToDelete,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeApplicationQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-application-note-success`,
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess();
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-application-note-error`,
                    props: {
                        title: t`Unable to update note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    }

    deleteNote(noteId: string): void {
        const timestamp = new Date().toISOString();

        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed.`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.activeNoteDeleteMutation
                    .mutateAsync({
                        path: {
                            noteId,
                            periodId: Number(this.periodId),
                            reviewAppId: Number(this.reviewAppId),
                        },
                    })
                    .then(() => {
                        runInAction(() => {
                            this.activeApplicationQuery.invalidate();
                        });
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-application-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch((error: Error) => {
                        const title =
                            (error as { statusCode?: number }).statusCode ===
                            403
                                ? t`Only the same owner can delete a note`
                                : t`Unable to delete note at the moment`;

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-application-note-error`,
                            props: {
                                title,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    }

    downloadNoteAttachment = (noteFileId: string, noteId: string): void => {
        this.activeDownloadAttachmentQuery.load({
            path: {
                periodId: Number(this.periodId),
                reviewAppId: Number(this.reviewAppId),
            },
            query: {
                noteId,
                fileId: noteFileId,
            },
        });
        when(() => !this.activeDownloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.activeDownloadAttachmentQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedAccessReviewActiveApplicationNotesController =
    new AccessReviewActiveApplicationNotesController();
