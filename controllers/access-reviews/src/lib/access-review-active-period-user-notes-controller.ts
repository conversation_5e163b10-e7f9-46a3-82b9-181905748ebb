import { isNil } from 'lodash-es';
import type {
    AttachmentConfig,
    INotesController,
    NoteCreateInput,
    NotesLabels,
    NoteUpdateInput,
    StandardNote,
} from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerPostReviewPeriodNoteMutation,
    accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    accessReviewPeriodControllerGetReviewApplicationUserDetailsOptions,
    accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    accessReviewUserControllerDownloadUserNoteFileOptions,
} from '@globals/api-sdk/queries';
import type { NoteResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class AccessReviewActiveOverviewNotesController implements INotesController {
    activeNotesQuery = new ObservedQuery(
        accessReviewPeriodControllerGetReviewApplicationUserDetailsOptions,
    );
    activeNoteCreateMutation = new ObservedMutation(
        accessReviewApplicationControllerPostReviewPeriodNoteMutation,
    );
    activeNoteUpdateMutation = new ObservedMutation(
        accessReviewPeriodControllerPutReviewPeriodNoteMutation,
    );
    activeNoteDeleteMutation = new ObservedMutation(
        accessReviewPeriodControllerDeleteReviewPeriodNoteMutation,
    );
    activeDownloadAttachmentQuery = new ObservedQuery(
        accessReviewUserControllerDownloadUserNoteFileOptions,
    );

    periodId: string | null = null;
    reviewAppId: string | null = null;
    userId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Backward compatible getter for old wrappers.
     */
    get list(): NoteResponseDto[] {
        const mappedNotes = (this.activeNotesQuery.data?.notes ?? []).map(
            (note) => {
                return {
                    ...note,
                    comment: note.message || '',
                    createdAt: note.sentAt || '',
                    updatedAt: note.sentAt || '',
                    owner: {
                        id: note.ownerId,
                        firstName: note.ownerName,
                        lastName: '',
                        avatarUrl: note.ownerAvatar,
                    },
                    files: note.files,
                };
            },
        );

        return mappedNotes.map((note) => note) as unknown as NoteResponseDto[];
    }

    /**
     * Standardized notes getter per INotesController.
     */
    get notes(): StandardNote[] {
        return (this.activeNotesQuery.data?.notes ?? []).map((note) => ({
            ...note,
            id: String(note.id),
            comment: note.message || '',
            createdAt: note.sentAt || '',
            updatedAt: note.sentAt || '',
            owner: {
                id: String(note.ownerId),
                firstName: note.ownerName,
                lastName: '',
                avatarUrl: note.ownerAvatar,
            },
            files: note.files,
        }));
    }

    // Backward compatible getter ended above

    get total(): number {
        return this.activeNotesQuery.data?.notes.length ?? 0;
    }

    get isLoading(): boolean {
        return this.activeNotesQuery.isLoading;
    }

    get isUploading(): boolean {
        return (
            this.activeNoteCreateMutation.isPending ||
            this.activeNoteUpdateMutation.isPending
        );
    }

    get 'data-id'(): string {
        return 'access-review-period-user-notes';
    }

    /**
     * INotesController UI configuration.
     */
    public get labels(): NotesLabels {
        return {
            title: t`Internal notes`,
            subtitle: t`Add any feedback or questions you want to track for this personnel. The reviewers will receive an email notification.`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details for this period.`,
            readOnlyEmptyStateDescription: t`Follow any feedback or questions about this period. These messages are not shared with auditors.`,
        };
    }

    public get attachments(): AttachmentConfig {
        return {
            showAddAttachment: 'modal',
            acceptedFormats: [
                'pdf',
                'docx',
                'odt',
                'xlsx',
                'ods',
                'pptx',
                'odp',
                'gif',
                'jpeg',
                'png',
                'jpg',
            ],
            allowMultipleFiles: true,
        };
    }

    public get maxNoteCharacters(): number {
        return 191;
    }

    public get showUnreadIndicators(): boolean {
        return false;
    }

    public get enableAuditorOnlyEditing(): boolean {
        return false;
    }

    public get hasSourceInput(): boolean {
        return false;
    }

    public get documentActive(): null {
        return null;
    }

    /**
     * Infinite scrolling properties (required by INotesController).
     * Note: This controller doesn't use infinite query yet, so these are stub implementations.
     */
    get hasMore(): boolean {
        // No pagination support yet
        return false;
    }

    get isLoadingMore(): boolean {
        // No pagination support yet
        return false;
    }

    loadMore = (): void => {
        // No pagination support yet - does nothing
    };

    /**
     * Generate dropdown items for a note.
     * Since this controller doesn't use read status tracking,
     * no dropdown items are needed.
     */
    getDropdownItems = (): undefined => {
        return undefined;
    };

    loadNotes = ({
        periodId,
        reviewAppId,
        userId,
    }: {
        periodId?: string;
        reviewAppId?: string;
        userId?: string;
    }): void => {
        if (isNil(periodId) || isNil(reviewAppId) || isNil(userId)) {
            throw new Error(t`Period ID and App ID params are required`);
        }
        this.periodId = periodId;
        this.reviewAppId = reviewAppId;
        this.userId = userId;

        this.activeNotesQuery.load({
            path: {
                periodId: Number(periodId),
                reviewAppId: Number(reviewAppId),
                userId: Number(userId),
            },
        });
    };

    createNote(values: NoteCreateInput, onSuccess: () => void): void {
        if (!this.periodId || !this.reviewAppId || !this.userId) {
            throw new Error(t`Event ID is not set`);
        }
        const timestamp = new Date().toISOString();

        this.activeNoteCreateMutation
            .mutateAsync({
                path: {
                    periodId: Number(this.periodId),
                    reviewAppId: Number(this.reviewAppId),
                    userId: Number(this.userId),
                },
                body: {
                    comment: values.comment || '',
                    'files[]': (values.files ?? []) as (File | Blob)[],
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeNotesQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-event-note-success`,
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess();
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-event-note-error`,
                    props: {
                        title: t`Unable to create note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    }

    updateNote(
        noteId: string,
        values: NoteUpdateInput,
        onSuccess: () => void,
    ): void {
        const timestamp = new Date().toISOString();

        this.activeNoteUpdateMutation
            .mutateAsync({
                path: {
                    noteId,
                    periodId: Number(this.periodId),
                    reviewAppId: Number(this.reviewAppId),
                },
                body: {
                    comment: values.comment || '',
                    'files[]': (values.files ?? []) as (File | Blob)[],
                    filesToDelete: values.filesToDelete,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.activeNotesQuery.invalidate();
                });
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-event-note-success`,
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess();
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-event-note-error`,
                    props: {
                        title: t`Unable to update note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    }

    deleteNote(noteId: string): void {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed.`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                const timestamp = new Date().toISOString();

                this.activeNoteDeleteMutation
                    .mutateAsync({
                        path: {
                            noteId,
                            periodId: Number(this.periodId),
                            reviewAppId: Number(this.reviewAppId),
                        },
                    })
                    .then(() => {
                        runInAction(() => {
                            this.activeNotesQuery.invalidate();
                        });
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch((error: Error) => {
                        const title =
                            (error as { statusCode?: number }).statusCode ===
                            403
                                ? t`Only the same owner can delete a note`
                                : t`Unable to delete note at the moment`;

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-error`,
                            props: {
                                title,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    }

    downloadNoteAttachment = (noteFileId: string, noteId: string): void => {
        this.activeDownloadAttachmentQuery.load({
            path: {
                periodId: Number(this.periodId),
                reviewAppId: Number(this.reviewAppId),
                userId: Number(this.userId),
            },
            query: {
                noteId,
                fileId: noteFileId,
            },
        });
        when(() => !this.activeDownloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.activeDownloadAttachmentQuery;
                const { signedUrl } = data ?? {};
                // UI config getters are defined on the class; nothing to do here.

                if (!signedUrl) {
                    return;
                }

                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedAccessReviewActivePeriodUserNotesController =
    new AccessReviewActiveOverviewNotesController();
