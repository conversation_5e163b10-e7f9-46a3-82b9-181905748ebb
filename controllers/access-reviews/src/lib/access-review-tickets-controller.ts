import type { ClosedTicketsControllerBase } from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    ticketsControllerDeleteTicketMutation,
    ticketsControllerGetTicketsForAccessReviewInfiniteOptions,
    ticketsControllerGetTicketsForAccessReviewOptions,
} from '@globals/api-sdk/queries';
import type { TicketResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

const getObjectNamePlural = () => t`access review users`;
const getObjectName = () => t`access review user`;

//TODO: review this controller on https://drata.atlassian.net/browse/ENG-70832
class AccessReviewTicketsController implements ClosedTicketsControllerBase {
    ticketsInProgressForAccessReviewUsersQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketsForAccessReviewInfiniteOptions,
    );

    ticketsCompletedForAccessReviewUsersQuery = new ObservedQuery(
        ticketsControllerGetTicketsForAccessReviewOptions,
    );

    ticketsCompletedInfiniteQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketsForAccessReviewInfiniteOptions,
    );

    unlinkTicketMutation = new ObservedMutation(
        ticketsControllerDeleteTicketMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get objectName(): string {
        return getObjectName();
    }

    get objectGetObjectNamePluralName(): string {
        return getObjectNamePlural();
    }

    get ticketsInProgress(): TicketResponseDto[] {
        return (
            this.ticketsInProgressForAccessReviewUsersQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasNextPage(): boolean {
        return this.ticketsInProgressForAccessReviewUsersQuery.hasNextPage;
    }

    loadNextPage = (): void => {
        this.ticketsInProgressForAccessReviewUsersQuery.nextPage();
    };

    get isLoadingTicketsInProgress(): boolean {
        return this.ticketsInProgressForAccessReviewUsersQuery.isLoading;
    }

    get totalTicketsInProgress(): number {
        return (
            this.ticketsInProgressForAccessReviewUsersQuery.data?.pages[0]
                ?.total ?? 0
        );
    }

    get ticketsCompleted(): TicketResponseDto[] {
        return this.ticketsCompletedForAccessReviewUsersQuery.data?.data ?? [];
    }

    get ticketsCompletedInfinite(): TicketResponseDto[] {
        return (
            this.ticketsCompletedInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasNextPageCompleted(): boolean {
        return this.ticketsCompletedInfiniteQuery.hasNextPage;
    }

    loadNextPageCompleted = (): void => {
        this.ticketsCompletedInfiniteQuery.nextPage();
    };

    get isLoadingTicketsCompleted(): boolean {
        return this.ticketsCompletedForAccessReviewUsersQuery.isLoading;
    }

    get isLoadingTicketsCompletedInfinite(): boolean {
        return this.ticketsCompletedInfiniteQuery.isLoading;
    }

    get totalTicketsCompleted(): number {
        return this.ticketsCompletedForAccessReviewUsersQuery.data?.total ?? 0;
    }

    get totalTicketsCompletedInfinite(): number {
        return this.ticketsCompletedInfiniteQuery.data?.pages[0]?.total ?? 0;
    }

    get userHasPermissionToCreateTicket(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'CreateTicket',
            'MANAGE',
        );
    }

    get isUnlinkingTicket(): boolean {
        return this.unlinkTicketMutation.isPending;
    }

    unlinkTicket = (ticketId: number, objectId: number) => {
        if (this.isUnlinkingTicket) {
            return;
        }

        openConfirmationModal({
            title: t`Unlink ticket`,
            body: t`Are you sure you want to unlink this ticket? This action cannot be undone.`,
            confirmText: t`Unlink`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.unlinkTicketMutation
                    .mutateAsync({
                        path: { id: ticketId },
                        query: {
                            relatedEntityId: objectId,
                            relatedEntityEnum: 'APPLICATION_USER',
                        },
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'ticket-unlinked-success',
                            hasTimeout: true,
                            props: {
                                title: t`Ticket unlinked successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: 'Close',
                            },
                        });
                        closeConfirmationModal();
                        // Invalidate queries to refresh the ticket list
                        this.ticketsInProgressForAccessReviewUsersQuery.invalidate();
                        this.ticketsCompletedForAccessReviewUsersQuery.invalidate();
                        this.ticketsCompletedInfiniteQuery.invalidate();
                    })
                    .catch((error: Error) => {
                        snackbarController.addSnackbar({
                            id: 'ticket-unlinked-error',
                            props: {
                                title: error.message,
                                severity: 'critical',
                                closeButtonAriaLabel: 'Close',
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    load = (userId: number) => {
        this.ticketsInProgressForAccessReviewUsersQuery.load({
            query: { id: userId, type: 'APPLICATION_USER', isCompleted: false },
        });
        this.ticketsCompletedForAccessReviewUsersQuery.load({
            query: { id: userId, type: 'APPLICATION_USER', isCompleted: true },
        });
    };

    loadClosedTicketsInfinite = (userId: number) => {
        this.ticketsCompletedInfiniteQuery.load({
            query: {
                id: userId,
                type: 'APPLICATION_USER',
                isCompleted: true,
                limit: 5,
            },
        });
    };
}

export const sharedAccessReviewActivePeriodUserTicketsController =
    new AccessReviewTicketsController();
