import { isEmpty } from 'lodash-es';
import {
    providerCatalogControllerGetProviderResourceAttributesOptions,
    providerCatalogControllerGetProviderResourcesOptions,
} from '@globals/api-sdk/queries';
import type {
    ProviderCatalogProviderResourcesResponseDto,
    ProviderResourceAttributesResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { providers } from '@globals/providers';

/**
 * Controller for managing provider service information.
 * Fetches and caches service names for provider resources.
 */
class ProviderServicesController {
    /**
     * Cache of ObservedQuery instances for each provider.
     */
    providerQueries = new Map<
        string,
        ObservedQuery<
            typeof providerCatalogControllerGetProviderResourcesOptions
        >
    >();

    providerAttributeQueries = new Map<
        string,
        ObservedQuery<
            typeof providerCatalogControllerGetProviderResourceAttributesOptions
        >
    >();

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Get or create an ObservedQuery for a specific provider.
     */
    private getProviderAttributeQuery(
        providerId: string,
        resourceName: string,
        workspaceId?: string,
        customClientAlias?: string,
    ): ObservedQuery<
        typeof providerCatalogControllerGetProviderResourceAttributesOptions
    > {
        // Create a unique key that includes workspace and alias for custom providers
        const key = customClientAlias
            ? `${providerId}-${resourceName}-${workspaceId}-${customClientAlias}`
            : `${providerId}-${resourceName}`;

        if (!this.providerAttributeQueries.has(key)) {
            const newQuery = new ObservedQuery(
                providerCatalogControllerGetProviderResourceAttributesOptions,
            );

            this.providerAttributeQueries.set(key, newQuery);

            // Build query parameters
            const queryParams: Record<string, string> = {};

            if (workspaceId) {
                queryParams.workspaceId = workspaceId;
            }
            if (customClientAlias) {
                queryParams.customClientAlias = customClientAlias;
            }

            const loadParams: Parameters<typeof newQuery.load>[0] = {
                path: { clientType: providerId, resourceName },
            };

            if (!isEmpty(Object.keys(queryParams))) {
                loadParams.query = queryParams;
            }

            newQuery.load(loadParams);
        }

        const query = this.providerAttributeQueries.get(key);

        if (!query) {
            throw new Error(`Provider attribute query not found for ${key}`);
        }

        return query;
    }

    /**
     * Get or create an ObservedQuery for a specific provider.
     */
    private getProviderQuery(
        providerId: string,
        workspaceId?: string,
        customClientAlias?: string,
    ): ObservedQuery<
        typeof providerCatalogControllerGetProviderResourcesOptions
    > {
        // Create a unique key that includes workspace and alias for custom providers
        const key = customClientAlias
            ? `${providerId}-${workspaceId}-${customClientAlias}`
            : providerId;

        if (!this.providerQueries.has(key)) {
            const newQuery = new ObservedQuery(
                providerCatalogControllerGetProviderResourcesOptions,
            );

            this.providerQueries.set(key, newQuery);

            // Build query parameters
            const queryParams: Record<string, string> = {};

            if (workspaceId) {
                queryParams.workspaceId = workspaceId;
            }
            if (customClientAlias) {
                queryParams.customClientAlias = customClientAlias;
            }

            // Load data immediately
            const loadParams: Parameters<typeof newQuery.load>[0] = {
                path: { clientType: providerId },
            };

            if (!isEmpty(Object.keys(queryParams))) {
                loadParams.query = queryParams;
            }

            newQuery.load(loadParams);
        }

        const query = this.providerQueries.get(key);

        if (!query) {
            throw new Error(`Provider query not found for ${key}`);
        }

        return query;
    }

    /**
     * Get the service name for a specific provider/resource combination.
     * Falls back to provider display name if service is not found.
     */
    getServiceName(
        providerId: string,
        resourceName: string,
        workspaceId?: string,
        customClientAlias?: string,
    ): string {
        const query = this.getProviderQuery(
            providerId,
            workspaceId,
            customClientAlias,
        );

        // Try to find the service name from the API data
        if (query.data) {
            const resourceInfo = query.data.resources.find(
                (r) => r.name === resourceName,
            );

            if (resourceInfo?.service) {
                return resourceInfo.service;
            }
        }

        // Fallback to provider display name (used when loading or service not found)
        const provider = providers[providerId as keyof typeof providers];

        return provider.name || providerId;
    }

    /**
     * Check if any provider queries are currently loading.
     */
    get isLoading(): boolean {
        return [...this.providerQueries.values()].some(
            (query) => query.isLoading,
        );
    }

    /**
     * Check if a specific provider's data is loading.
     */
    isProviderLoading(providerId: string): boolean {
        const query = this.providerQueries.get(providerId);

        return query?.isLoading ?? false;
    }

    /**
     * Get provider resources data for a specific provider.
     */
    getProviderResources(
        providerId: string,
        workspaceId?: string,
        customClientAlias?: string,
    ): ProviderCatalogProviderResourcesResponseDto | null {
        const query = this.getProviderQuery(
            providerId,
            workspaceId,
            customClientAlias,
        );

        return query.data ?? null;
    }

    /**
     * Get attributes data for a specific provider/resource combination.
     */
    getProviderAttributes(
        providerId: string,
        resourceName: string,
        workspaceId?: string,
        customClientAlias?: string,
    ): ProviderResourceAttributesResponseDto | null {
        const query = this.getProviderAttributeQuery(
            providerId,
            resourceName,
            workspaceId,
            customClientAlias,
        );

        return query.data ?? null;
    }

    getProviderAttributeQueryIsLoading(
        providerId: string,
        resourceName: string,
        workspaceId?: string,
        customClientAlias?: string,
    ): boolean {
        // Create the same key format as used in getProviderAttributeQuery
        const key = customClientAlias
            ? `${providerId}-${resourceName}-${workspaceId}-${customClientAlias}`
            : `${providerId}-${resourceName}`;

        const query = this.providerAttributeQueries.get(key);

        return query?.isLoading ?? false;
    }

    /**
     * Preload service data for multiple providers.
     * Useful for batch loading when you know which providers will be needed.
     */
    preloadProviders(providerIds: string[]): void {
        providerIds.forEach((providerId) => {
            this.getProviderQuery(providerId);
        });
    }
}

export const sharedProviderServicesController =
    new ProviderServicesController();
