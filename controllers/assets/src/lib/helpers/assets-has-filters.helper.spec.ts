import { describe, expect, test } from 'vitest';
import {
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { assetsHasFilter } from './assets-has-filters.helper';

describe('assetsHasFilter', () => {
    describe('when no filters are applied', () => {
        test('should return false for default empty state', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
                },
                sorting: [],
                globalFilter: {
                    search: '',
                    filters: {
                        assetClassType: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetType: { filterType: 'radio', value: undefined },
                        employmentStatus: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetProvider: { filterType: 'combobox', value: [] },
                        userId: { filterType: 'combobox', value: [] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeFalsy();
        });

        test('should return false when all filters are set to ALL', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 20, 50],
                },
                sorting: [],
                globalFilter: {
                    search: '',
                    filters: {
                        assetClassType: {
                            filterType: 'select',
                            value: {
                                label: 'All classes',
                                value: 'ALL',
                                id: 'ALL',
                            },
                        },
                        assetType: { filterType: 'radio', value: 'ALL' },
                        employmentStatus: {
                            filterType: 'select',
                            value: {
                                label: 'All statuses',
                                value: 'ALL',
                                id: 'ALL',
                            },
                        },
                        assetProvider: { filterType: 'combobox', value: [] },
                        userId: { filterType: 'combobox', value: [] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeFalsy();
        });
    });

    describe('when filters are applied', () => {
        test('should return true when assetClassType filter is applied', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 20, 50],
                },
                sorting: [],
                globalFilter: {
                    search: '',
                    filters: {
                        assetClassType: {
                            filterType: 'combobox',
                            value: { label: 'Code', value: 'CODE', id: 'CODE' },
                        },
                        assetType: { filterType: 'radio', value: undefined },
                        employmentStatus: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetProvider: { filterType: 'combobox', value: [] },
                        userId: { filterType: 'combobox', value: [] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeTruthy();
        });

        test('should return true when assetType filter is applied', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 20, 50],
                },
                sorting: [],
                globalFilter: {
                    search: '',
                    filters: {
                        assetClassType: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetType: { filterType: 'radio', value: 'PHYSICAL' },
                        employmentStatus: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetProvider: { filterType: 'combobox', value: [] },
                        userId: { filterType: 'combobox', value: [] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeTruthy();
        });
        test('should return true when employmentStatus filter is applied', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 20, 50],
                },
                sorting: [],
                globalFilter: {
                    search: '',
                    filters: {
                        assetClassType: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetType: { filterType: 'radio', value: undefined },
                        employmentStatus: {
                            filterType: 'select',
                            value: {
                                label: 'Current contractor',
                                value: 'CURRENT_CONTRACTOR',
                                id: 'CURRENT_CONTRACTOR',
                            },
                        },
                        assetProvider: { filterType: 'combobox', value: [] },
                        userId: { filterType: 'combobox', value: [] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeTruthy();
        });

        test('should return true when assetProvider array filter is applied', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 20, 50],
                },
                sorting: [],
                globalFilter: {
                    search: '',
                    filters: {
                        assetClassType: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetType: { filterType: 'radio', value: undefined },
                        employmentStatus: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetProvider: {
                            filterType: 'combobox',
                            value: ['PROVIDER_1'],
                        },
                        userId: { filterType: 'combobox', value: [] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeTruthy();
        });
        test('should return true when userId array filter is applied', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 20, 50],
                },
                sorting: [],
                globalFilter: {
                    search: '',
                    filters: {
                        assetClassType: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetType: { filterType: 'radio', value: undefined },
                        employmentStatus: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetProvider: { filterType: 'combobox', value: [] },
                        userId: { filterType: 'combobox', value: ['123'] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeTruthy();
        });
    });
    describe('when search is applied', () => {
        test('should return true when search is applied', () => {
            const params: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 20, 50],
                },
                sorting: [],
                globalFilter: {
                    search: 'search',
                    filters: {
                        assetClassType: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetType: { filterType: 'radio', value: undefined },
                        employmentStatus: {
                            filterType: 'select',
                            value: undefined,
                        },
                        assetProvider: { filterType: 'combobox', value: [] },
                        userId: { filterType: 'combobox', value: [] },
                    },
                },
            };

            expect(assetsHasFilter(params)).toBeTruthy();
        });
    });
});
