import { vendorAssessmentCriteriaControllerGetAllCriteriaOptions } from '@globals/api-sdk/queries';
import type {
    VendorAssessmentCriteriaControllerGetAllCriteriaData,
    VendorAssessmentCriteriaItemResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class VendorsCriteriaController {
    constructor() {
        makeAutoObservable(this);
    }

    criteriaQuery = new ObservedQuery(
        vendorAssessmentCriteriaControllerGetAllCriteriaOptions,
    );

    /**
     * Get loading state for the criteria table.
     */
    get isLoading(): boolean {
        return this.criteriaQuery.isLoading;
    }

    /**
     * Get error state for debugging.
     */
    get error(): unknown {
        return this.criteriaQuery.error;
    }

    /**
     * Get criteria data from API response.
     */
    get data(): VendorAssessmentCriteriaItemResponseDto[] {
        const apiResponse = this.criteriaQuery.data;

        if (!apiResponse || !('data' in apiResponse)) {
            return [];
        }

        return apiResponse.data;
    }

    /**
     * Get total count for pagination.
     */
    get total(): number {
        const apiResponse = this.criteriaQuery.data;

        if (apiResponse && 'total' in apiResponse) {
            return apiResponse.total;
        }

        return 0;
    }

    /**
     * Load criteria data with query parameters.
     */
    load = (
        query: VendorAssessmentCriteriaControllerGetAllCriteriaData['query'],
    ): void => {
        this.criteriaQuery.load({ query });
    };
}

export const sharedVendorsCriteriaController = new VendorsCriteriaController();
