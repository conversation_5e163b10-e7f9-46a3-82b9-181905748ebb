import { isEmpty, isError, isNil } from 'lodash-es';
import type {
    UtilitiesVrmAgentMessageAction,
    UtilitiesVrmAgentMessageData,
} from '@components/utilities';
import { routeController } from '@controllers/route';
import type { IconName } from '@cosmos/components/icon';
import { agentWorkflowVendorAssessmentControllerGetWorkflowsOptions } from '@globals/api-sdk/queries';
import type { VendorAssessmentAgentWorkflowResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { VRM_AGENT_ICON_MAPPING } from './constants/vrm-agent-icons-mapping.constants';
import { VRM_AGENT_REFERENCE_MAPPING } from './constants/vrm-agent-reference-mapping.constants';
import type {
    VrmAgentAuditTrailStep,
    VrmAgentReferenceId,
} from './types/vendor-vrm-agent.types';
import { sharedVendorVrmAgentActionsController } from './vendor-vrm-agent-actions.controller';
import { sharedVendorsSecurityReviewDocumentsController } from './vendors-security-review-documents-controller';

class VendorsVrmAgentController {
    currentVendorId: number | null = null;

    #actionsController = sharedVendorVrmAgentActionsController;

    vendorAssessmentAgentWorkflowQuery = new ObservedQuery(
        agentWorkflowVendorAssessmentControllerGetWorkflowsOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    isLoadingMock = false;
    isExecutingActionState = false;
    error: string | null = null;

    auditTrailSteps: VrmAgentAuditTrailStep[] = [];

    get currentAssessment(): VendorAssessmentAgentWorkflowResponseDto | null {
        return this.vendorAssessmentAgentWorkflowQuery.data?.data[0] ?? null;
    }

    get currentAssessmentSecurityReviewId(): string | null {
        if (!this.currentAssessment?.workflowTypeId) {
            return null;
        }

        return this.currentAssessment.workflowTypeId;
    }

    get assessmentMessages(): UtilitiesVrmAgentMessageData[] {
        // If there are no assessment messages (empty state), return the empty state message
        if (
            !this.currentAssessment?.messages ||
            isEmpty(this.currentAssessment.messages)
        ) {
            return [this.emptyStateMessage];
        }

        return this.currentAssessment.messages.map((message) => ({
            id: `${message.id}`,
            createdAt: message.createdAt,
            caller: message.data.caller as 'AGENT' | 'USER',
            title: message.data.title
                ? this.processItemsWithReferences(
                      message.data.title.map((item, index) => ({
                          ...item,
                          id: `${message.id}-title-${index}`,
                          icon: item.icon
                              ? this.transformIconValue(item.icon)
                              : undefined,
                      })),
                  )
                : message.data.title,
            body: message.data.body
                ? this.processItemsWithReferences(
                      message.data.body.map((item, index) => ({
                          ...item,
                          id: `${message.id}-body-${index}`,
                          icon: item.icon
                              ? this.transformIconValue(item.icon)
                              : undefined,
                          text: item.text || '', // Ensure text is always a string
                          style:
                              item.style === 'bold' || item.style === 'normal'
                                  ? item.style
                                  : undefined,
                      })),
                  )
                : message.data.body,
            actions: this.processItemsWithReferences(
                message.data.actions?.map((item, index) => ({
                    id: `${message.id}-action-${index}`,
                    type: item.type as
                        | 'button'
                        | 'link'
                        | 'pressedButton'
                        | 'action',
                    text: item.text || '',
                    action: item.action || '',
                })) ?? [],
            ),
        }));
    }

    get emptyStateMessage(): UtilitiesVrmAgentMessageData {
        return {
            id: 'vrm-empty-state-001',
            createdAt: new Date().toISOString(),
            caller: 'AGENT',
            title: null,
            body: [
                {
                    id: 'vrm-empty-body-001',
                    text: `I can help you conduct a detailed assessment and provide a report of their security posture.`,
                },
            ],
            actions: [
                {
                    id: 'vrm-empty-action-001',
                    type: 'action',
                    text: 'Start review',
                    action: 'VENDOR_ASSESSMENT_START',
                },
            ],
        };
    }

    get isLoading(): boolean {
        return this.vendorAssessmentAgentWorkflowQuery.isLoading;
    }

    get hasVendorAssessmentAgentWorkflow(): boolean {
        return !isNil(this.currentAssessment);
    }

    get hasAssessmentMessages(): boolean {
        return isEmpty(this.currentAssessment?.messages);
    }

    get isExecutingAction(): boolean {
        return this.isExecutingActionState;
    }

    /**
     * Retrieves the current step progress from the assessment data.
     */
    get currentStepProgress(): number {
        // Remove mock when assessment.currentStep.progress is available
        return Math.floor(Math.random() * 100);
    }

    /**
     * Checks if there are security review documents that were created or updated after the assessment creation date.
     * This determines if the re-run assessment button should be available.
     */
    hasNewerSecurityReviewDocuments = (): boolean => {
        if (!this.currentAssessment?.createdAt) {
            return false;
        }

        const { allSecurityReviewDocuments } =
            sharedVendorsSecurityReviewDocumentsController;

        if (isEmpty(allSecurityReviewDocuments)) {
            return false;
        }

        const reviewCreationTime = new Date(
            this.currentAssessment.createdAt,
        ).getTime();

        return allSecurityReviewDocuments.some((securityReviewDocument) => {
            if (securityReviewDocument.vendorDocument) {
                const document = securityReviewDocument.vendorDocument;
                const documentCreatedTime = new Date(
                    document.createdAt,
                ).getTime();
                const documentUpdatedTime = new Date(
                    document.updatedAt,
                ).getTime();

                return (
                    documentCreatedTime > reviewCreationTime ||
                    documentUpdatedTime > reviewCreationTime
                );
            }

            return false;
        });
    };

    handleActionClick = (action: UtilitiesVrmAgentMessageAction): void => {
        switch (action.type) {
            case 'button':
            case 'action': {
                this.executeAction(action.action);
                break;
            }
            case 'link': {
                break;
            }
            default: {
                console.warn(`Unknown action type: ${action.type}`);
                break;
            }
        }
    };

    executeAction = (actionId: string): void => {
        if (!this.currentVendorId) {
            console.warn('Cannot execute action: no vendor ID set');

            return;
        }

        this.isExecutingActionState = true;

        try {
            this.#actionsController.executeAction(
                actionId,
                this.currentVendorId,
            );
        } catch (error) {
            console.error('Failed to execute action:', error);
        } finally {
            this.isExecutingActionState = false;
        }
    };

    resolveReferenceToUrl = (refId: string): string | null => {
        if (!this.currentVendorId) {
            return null;
        }

        if (!(refId in VRM_AGENT_REFERENCE_MAPPING)) {
            return null;
        }

        if (!this.currentAssessmentSecurityReviewId) {
            return null;
        }

        const routePattern =
            VRM_AGENT_REFERENCE_MAPPING[refId as VrmAgentReferenceId];

        const route = routePattern
            .replace(':vendorId', this.currentVendorId.toString())
            .replace(
                ':securityReviewId',
                this.currentAssessmentSecurityReviewId,
            );

        return `${routeController.userPartOfUrl}/${route}`;
    };

    transformIconValue = (iconValue: string): IconName => {
        return VRM_AGENT_ICON_MAPPING[iconValue] ?? (iconValue as IconName);
    };

    processItemsWithReferences = <
        T extends { ref?: string } | { action: string; type: string },
    >(
        items: T[],
    ): T[] => {
        return items.map((item) => {
            // Handle actions with type 'link'
            if ('action' in item && 'type' in item && item.type === 'link') {
                const resolvedUrl = this.resolveReferenceToUrl(item.action);

                return {
                    ...item,
                    action: resolvedUrl || item.action,
                };
            }

            // Handle items with ref property
            if ('ref' in item && item.ref) {
                const resolvedUrl = this.resolveReferenceToUrl(item.ref);

                return {
                    ...item,
                    ref: resolvedUrl || item.ref,
                };
            }

            return item;
        });
    };

    loadAssessment = (vendorId: number): void => {
        if (!vendorId) {
            this.error = 'Vendor ID is required';

            return;
        }

        this.currentVendorId = vendorId;
        try {
            // For now, use mock data
            this.loadMockData();
            this.vendorAssessmentAgentWorkflowQuery.load({
                query: {
                    statuses: ['PENDING', 'IN_PROGRESS', 'FAILED'],
                    vendorIds: [vendorId],
                },
            });
        } catch (error) {
            this.error = isError(error) ? error.message : 'Unknown error';
        } finally {
            this.isLoadingMock = false;
        }
    };

    loadMockData = (): void => {
        this.isLoadingMock = true;
        this.loadMockAuditTrail();
        this.isLoadingMock = false;
    };

    loadMockAuditTrail = (): void => {
        this.auditTrailSteps = [
            {
                id: 'security-review-questionnaire',
                displayName: 'Security review questionnaire',
                status: 'COMPLETED',
                createdAt: 'August 10, 2024 at 10:23',
                href:
                    this.resolveReferenceToUrl('VendorsDocuments') || undefined,
            },
            {
                id: 'documents-collected',
                displayName: 'Documents collected from SB Trust Center',
                status: 'COMPLETED',
                createdAt: 'August 10, 2024 at 10:23',
                href:
                    this.resolveReferenceToUrl('VendorsDocuments') || undefined,
            },
            {
                id: 'criteria-assessment-results',
                displayName: 'Criteria assessment results',
                status: 'COMPLETED',
                createdAt: 'August 10, 2024 at 10:23',
                href: undefined,
            },
            {
                id: 'follow-up-questionnaire',
                displayName: 'Follow-up questionnaire',
                status: 'IN_PROGRESS',
                createdAt: undefined,
            },
        ];
    };
}

export const sharedVendorsVrmAgentController = new VendorsVrmAgentController();
