import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { makeAutoObservable, when } from '@globals/mobx';
import { VRM_AGENT_ACTION_MAPPING } from './constants/vrm-agent-actions-mapping.constants';
import {
    getVrmAssessmentMissingImpactLevelError,
    getVrmAssessmentStartedSuccess,
} from './constants/vrm-agent-snackbars.constants';
import type {
    VrmAgentActionId,
    VrmAgentHandlerName,
} from './types/vendor-vrm-agent.types';
import { sharedVendorsDetailsController } from './vendors-details-controller';
import { sharedVendorsSecurityReviewDetailsController } from './vendors-security-review-details-controller';

export class VendorVrmAgentActionsController {
    isStoppingAnalysis = false;
    isStartingAssessment = false;

    constructor() {
        makeAutoObservable(this);
    }

    executeAction = (actionId: string, vendorId: number): void => {
        if (!(actionId in VRM_AGENT_ACTION_MAPPING)) {
            console.warn(`Unknown action ID: ${actionId}`);

            return;
        }

        const handlerName =
            VRM_AGENT_ACTION_MAPPING[actionId as VrmAgentActionId];
        const handler = this.getActionHandler(handlerName);

        if (handler) {
            handler(vendorId);
        } else {
            console.warn(`No handler found for action: ${actionId}`);
        }
    };

    getActionHandler = (
        handlerName: VrmAgentHandlerName,
    ): ((vendorId: number) => void) | null => {
        switch (handlerName) {
            case VRM_AGENT_ACTION_MAPPING.VENDOR_ASSESSMENT_START: {
                return this.handleAssessmentStart;
            }
            case VRM_AGENT_ACTION_MAPPING.CANCEL: {
                return this.handleStopAnalysis;
            }
            case VRM_AGENT_ACTION_MAPPING.SEND_VENDOR_ASSESSMENT_QUESTIONNAIRE: {
                return this.handleSendQuestionnaire;
            }
            case VRM_AGENT_ACTION_MAPPING.ENABLE_AUTO_SEND_QUESTIONNAIRE: {
                return this.handleAutoSendQuestionnaire;
            }
            case VRM_AGENT_ACTION_MAPPING.FINALIZE: {
                return this.handleFinalizeAssessment;
            }
            default: {
                return null;
            }
        }
    };
    handleAssessmentStart = (vendorId: number): void => {
        console.info('🚀 Starting VRM assessment for vendor:', vendorId);

        // First, validate that the vendor has an impact level assigned
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.impactLevel) {
            snackbarController.addSnackbar(
                getVrmAssessmentMissingImpactLevelError(),
            );

            return;
        }

        // Check if we're on a specific security review route
        const { currentParams } = routeController;
        const routeSecurityReviewId =
            currentParams.securityReviewId || currentParams.reviewId;

        // Only use controller data if we're on a specific security review route
        const securityReviewId = routeSecurityReviewId
            ? sharedVendorsSecurityReviewDetailsController.securityReviewDetails
                  ?.id
            : undefined;

        console.info('📍 Security review detection:', {
            routeSecurityReviewId,
            securityReviewId,
            hasSecurityReviewId: Boolean(securityReviewId),
        });

        // Always call the same endpoint - backend will handle security review creation
        this.startVrmAssessment(vendorId, securityReviewId);
    };

    handleStopAnalysis = (vendorId: number): void => {
        console.info('Stopping VRM analysis for vendor:', vendorId);
        this.isStoppingAnalysis = true;
        this.mockStopAnalysis(vendorId);
    };
    handleSendQuestionnaire = (vendorId: number): void => {
        console.info('Sending questionnaire for vendor:', vendorId);
    };
    handleAutoSendQuestionnaire = (vendorId: number): void => {
        console.info('Enabling auto-send questionnaires for vendor:', vendorId);
    };

    handleFinalizeAssessment = (vendorId: number): void => {
        console.info('Finalizing assessment for vendor:', vendorId);
    };

    handleReRun = (vendorId: number): void => {
        if (this.isStoppingAnalysis || this.isStartingAssessment) {
            console.warn('Re-run already in progress for vendor:', vendorId);

            return;
        }

        console.info('🔄 Starting re-run assessment for vendor:', vendorId);

        // First, stop the current analysis
        this.handleStopAnalysis(vendorId);

        // Wait for stop operation to complete, then start new assessment
        when(
            () => !this.isStoppingAnalysis,
            () => {
                console.info(
                    '✅ Stop analysis completed, starting new assessment',
                );
                this.handleAssessmentStart(vendorId);
            },
        );
    };

    startVrmAssessment = (
        vendorId: number,
        securityReviewId?: number,
    ): void => {
        console.info('🎯 Starting VRM assessment:', {
            vendorId,
            securityReviewId,
        });

        // Mock the unified endpoint call
        this.mockStartVrmAssessment(vendorId, securityReviewId);
    };

    mockStartVrmAssessment = (
        vendorId: number,
        securityReviewId: number | undefined,
    ): void => {
        console.info('📤 MOCK: Calling unified VRM assessment endpoint:', {
            vendorId,
            securityReviewId,
        });

        this.isStartingAssessment = true;

        // Simulate API response
        setTimeout(() => {
            console.info('✅ MOCK: VRM assessment started successfully:', {
                vendorId,
                securityReviewId,
                created: securityReviewId
                    ? 'Using existing security review'
                    : 'Security review created by backend',
            });

            this.isStartingAssessment = false;
            snackbarController.addSnackbar(getVrmAssessmentStartedSuccess());
        }, 1000);
    };

    mockStopAnalysis = (vendorId: number): void => {
        console.info('📤 MOCK: Stopping VRM analysis for vendor:', vendorId);
        this.isStoppingAnalysis = false;
    };

    hasHandler = (actionId: string): boolean => {
        return actionId in VRM_AGENT_ACTION_MAPPING;
    };
}

export const sharedVendorVrmAgentActionsController =
    new VendorVrmAgentActionsController();
