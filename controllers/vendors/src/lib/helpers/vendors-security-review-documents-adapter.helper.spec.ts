import { isBoolean, isNumber, isString } from 'lodash-es';
import { beforeEach, describe, expect, test, vi } from 'vitest';
import type { VendorSecurityReviewDocumentResponseDto } from '@globals/api-sdk/types';
// Import the mocked module to access it in tests
import { sharedFeatureAccessModel } from '@globals/feature-access';
import {
    findRelevantQuestionnaire,
    transformDocumentsToFile,
    transformDocumentsToFiles,
    transformDocumentsToQuestionnaire,
    transformDocumentsToQuestionnaires,
    transformToDocuments,
} from './vendors-security-review-documents-adapter.helper';

// Mock the global controllers
vi.mock('@globals/feature-access', () => ({
    sharedFeatureAccessModel: {
        isVendorAISummaryEnabled: true,
    },
}));

// Mock the i18n macro
vi.mock('@globals/i18n/macro', () => ({
    t: (strings: TemplateStringsArray, ...values: unknown[]) => {
        let result = '';

        for (const [index, string] of strings.entries()) {
            result = result + string;
            const value = values[index];

            if (value !== undefined) {
                if (isString(value) || isNumber(value) || isBoolean(value)) {
                    result = result + String(value);
                } else if (value === null) {
                    result = `${result}null`;
                } else {
                    // For objects, arrays, etc., provide a meaningful representation
                    result = `${result}[object]`;
                }
            }
        }

        return result;
    },
}));

const createQuestionnaireMock = (
    overrides: Partial<
        VendorSecurityReviewDocumentResponseDto['questionnaire']
    > = {},
): NonNullable<VendorSecurityReviewDocumentResponseDto['questionnaire']> => ({
    id: 1,
    completedBy: null,
    recipientEmail: '<EMAIL>',
    isCompleted: true,
    dateSent: '2023-01-01',
    isManualUpload: false,
    responseId: null,
    title: 'Test Questionnaire',
    completedAt: '2023-01-02',
    totalQuestions: null,
    totalQuestionsAnswered: null,
    ...overrides,
});

const createQuestionnaireReminderMock = (
    overrides: Partial<
        VendorSecurityReviewDocumentResponseDto['questionnaireReminder']
    > = {},
): NonNullable<
    VendorSecurityReviewDocumentResponseDto['questionnaireReminder']
> => ({
    id: 1,
    result: 'Reminder sent successfully',
    scheduleConfigurationId: 1,
    createdAt: '2023-01-03',
    updatedAt: '2023-01-03',
    ...overrides,
});

const createSummaryStatusMock = (
    overrides: Partial<
        VendorSecurityReviewDocumentResponseDto['summaryStatus']
    > = {},
): NonNullable<VendorSecurityReviewDocumentResponseDto['summaryStatus']> => ({
    data: 5,
    status: 'SUCCESS',
    ...overrides,
});

const createDocumentMock = (
    overrides: Partial<VendorSecurityReviewDocumentResponseDto> = {},
): VendorSecurityReviewDocumentResponseDto => ({
    id: 123,
    documentId: 456,
    type: 'QUESTIONNAIRE',
    name: 'Test Document',
    vendorDocument: null,
    questionnaire: null,
    vendorReview: null,
    questionnaireReminder: null,
    summaryStatus: null,
    ...overrides,
});

describe('vendor Security Review Documents Adapter Helper', () => {
    beforeEach(() => {
        // Reset mocks
        vi.clearAllMocks();
        // Reset feature flag to default
        vi.mocked(sharedFeatureAccessModel).isVendorAISummaryEnabled = true;
    });

    describe('extractSummaryStatus (internal helper)', () => {
        test('should return empty string when AI summary feature is disabled', () => {
            // Mock feature flag as disabled
            vi.mocked(sharedFeatureAccessModel).isVendorAISummaryEnabled =
                false;

            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 5,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('');
        });

        test('should handle GENERATING status for QUESTIONNAIRE', () => {
            const mockData = createDocumentMock({
                type: 'QUESTIONNAIRE',
                summaryStatus: createSummaryStatusMock({
                    status: 'GENERATING',
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.summaryMessage).toBe(
                'Analyzing questionnaire...',
            );
        });

        test('should handle GENERATING status for DOCUMENT', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'GENERATING',
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('Analyzing document...');
        });

        test('should handle SUCCESS status for QUESTIONNAIRE with exceptions', () => {
            const mockData = createDocumentMock({
                type: 'QUESTIONNAIRE',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 3,
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.summaryMessage).toBe(
                'Review 3 callouts found',
            );
        });

        test('should handle SUCCESS status for QUESTIONNAIRE with no exceptions', () => {
            const mockData = createDocumentMock({
                type: 'QUESTIONNAIRE',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 0,
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.summaryMessage).toBe('No callout found');
        });

        test('should handle SUCCESS status for DOCUMENT with exceptions', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 5,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('Review 5 exceptions found');
        });

        test('should handle SUCCESS status for DOCUMENT with no exceptions', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 0,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('No exceptions found');
        });

        test('should handle ERROR status for QUESTIONNAIRE', () => {
            const mockData = createDocumentMock({
                type: 'QUESTIONNAIRE',
                summaryStatus: createSummaryStatusMock({
                    status: 'ERROR',
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.summaryMessage).toBe('Analysis failed');
        });

        test('should handle ERROR status for DOCUMENT', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'ERROR',
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('Ready for review');
        });

        test('should handle null summaryStatus (defaults to GENERATING)', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: null,
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('Analyzing document...');
        });

        test('should handle undefined summaryStatus (defaults to GENERATING)', () => {
            const mockData = createDocumentMock({
                type: 'QUESTIONNAIRE',
                summaryStatus: undefined,
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.summaryMessage).toBe(
                'Analyzing questionnaire...',
            );
        });

        test('should handle unknown status (defaults to empty string)', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'UNKNOWN_STATUS' as 'SUCCESS',
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('');
        });

        test('should handle null data in summaryStatus', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: null,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('No exceptions found');
        });

        test('should handle undefined data in summaryStatus', () => {
            const mockData = createDocumentMock({
                type: 'QUESTIONNAIRE',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: undefined,
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.summaryMessage).toBe('No callout found');
        });
    });

    describe('transformDocumentsToQuestionnaire', () => {
        test('should transform single questionnaire document with complete data', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Questionnaire',
                type: 'QUESTIONNAIRE',
                documentId: 456,
                questionnaire: createQuestionnaireMock({
                    id: 789,
                    isCompleted: true,
                    isManualUpload: false,
                    dateSent: '2023-01-01',
                    completedAt: '2023-01-02',
                    responseId: 999,
                }),
                questionnaireReminder: createQuestionnaireReminderMock({
                    createdAt: '2023-01-03',
                }),
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 3,
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result).toStrictEqual({
                id: 'row-123',
                name: 'Test Questionnaire',
                customData: {
                    summaryMessage: 'Review 3 callouts found',
                    isCompleted: true,
                    isManualUpload: false,
                    dateSent: '2023-01-01',
                    completedAt: '2023-01-02',
                    reminderDate: '2023-01-03',
                    responseId: 999,
                    questionnaireId: 789,
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            });
        });

        test('should handle questionnaire with null/undefined values', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Questionnaire',
                type: 'QUESTIONNAIRE',
                documentId: 456,
                questionnaire: createQuestionnaireMock({
                    id: 789,
                    completedAt: null,
                    responseId: null,
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.completedAt).toBeUndefined();
            expect(result.customData.responseId).toBeUndefined();
            expect(result.customData.questionnaireId).toBe(789);
        });

        test('should handle questionnaire with no questionnaire data', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Questionnaire',
                type: 'QUESTIONNAIRE',
                documentId: 456,
                questionnaire: null,
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.isCompleted).toBeUndefined();
            expect(result.customData.questionnaireId).toBeUndefined();
            expect(result.customData.responseId).toBeUndefined();
        });
    });

    describe('transformDocumentsToQuestionnaires', () => {
        test('should transform document response to questionnaire files', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test Questionnaire',
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        isCompleted: true,
                        isManualUpload: false,
                        dateSent: '2023-01-01',
                        completedAt: '2023-01-02',
                        responseId: null,
                    }),
                    questionnaireReminder: createQuestionnaireReminderMock({
                        createdAt: '2023-01-03',
                    }),
                    summaryStatus: null,
                }),
            ];

            const result = transformDocumentsToQuestionnaires(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test Questionnaire',
                    customData: {
                        summaryMessage: 'Analyzing questionnaire...',
                        isCompleted: true,
                        isManualUpload: false,
                        dateSent: '2023-01-01',
                        completedAt: '2023-01-02',
                        reminderDate: '2023-01-03',
                        responseId: undefined,
                        questionnaireId: 1,
                        documentId: 456,
                        securityReviewDocumentId: 123,
                    },
                },
            ]);
        });

        test('should handle empty questionnaire data', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test Questionnaire',
                    type: 'QUESTIONNAIRE',
                }),
            ];

            const result = transformDocumentsToQuestionnaires(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test Questionnaire',
                    customData: {
                        summaryMessage: 'Analyzing questionnaire...',
                        isCompleted: undefined,
                        isManualUpload: undefined,
                        dateSent: undefined,
                        completedAt: undefined,
                        reminderDate: undefined,
                        responseId: undefined,
                        questionnaireId: undefined,
                        documentId: 456,
                        securityReviewDocumentId: 123,
                    },
                },
            ]);
        });

        test('should include responseId when available', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test Questionnaire',
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        isCompleted: true,
                        isManualUpload: false,
                        responseId: 456,
                    }),
                }),
            ];

            const result = transformDocumentsToQuestionnaires(mockData);

            expect(result[0].customData.responseId).toBe(456);
        });
    });

    describe('transformDocumentsToFile', () => {
        test('should transform single document with SUCCESS status and exceptions', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Document',
                type: 'DOCUMENT',
                documentId: 456,
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 5,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result).toStrictEqual({
                id: 'row-123',
                name: 'Test Document',
                value: 'Review 5 exceptions found',
                documentId: 456,
                securityReviewDocumentId: 123,
            });
        });

        test('should transform single document with SUCCESS status and no exceptions', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Document',
                type: 'DOCUMENT',
                documentId: 456,
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 0,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result).toStrictEqual({
                id: 'row-123',
                name: 'Test Document',
                value: 'No exceptions found',
                documentId: 456,
                securityReviewDocumentId: 123,
            });
        });

        test('should transform single document with ERROR status', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Document',
                type: 'DOCUMENT',
                documentId: 456,
                summaryStatus: createSummaryStatusMock({
                    status: 'ERROR',
                    data: 5,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result).toStrictEqual({
                id: 'row-123',
                name: 'Test Document',
                value: 'Ready for review',
                documentId: 456,
                securityReviewDocumentId: 123,
            });
        });

        test('should transform single document with GENERATING status', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Document',
                type: 'DOCUMENT',
                documentId: 456,
                summaryStatus: createSummaryStatusMock({
                    status: 'GENERATING',
                    data: null,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result).toStrictEqual({
                id: 'row-123',
                name: 'Test Document',
                value: 'Analyzing document...',
                documentId: 456,
                securityReviewDocumentId: 123,
            });
        });

        test('should transform single document with no summary status', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Document',
                type: 'DOCUMENT',
                documentId: 456,
                summaryStatus: null,
            });

            const result = transformDocumentsToFile(mockData);

            expect(result).toStrictEqual({
                id: 'row-123',
                name: 'Test Document',
                value: 'Analyzing document...',
                documentId: 456,
                securityReviewDocumentId: 123,
            });
        });
    });

    describe('transformDocumentsToFiles', () => {
        test('should transform document response with exceptions to files', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    summaryStatus: createSummaryStatusMock({
                        status: 'SUCCESS',
                        data: 5,
                    }),
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: 'Review 5 exceptions found',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should transform document response with no exceptions to files', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    summaryStatus: createSummaryStatusMock({
                        status: 'SUCCESS',
                        data: 0,
                    }),
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: 'No exceptions found',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should handle document response without summary status', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: 'Analyzing document...',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should handle document response with non-SUCCESS status', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    summaryStatus: createSummaryStatusMock({
                        status: 'ERROR',
                        data: 5,
                    }),
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: 'Ready for review',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should handle document response with GENERATING status', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    summaryStatus: createSummaryStatusMock({
                        status: 'GENERATING',
                        data: null,
                    }),
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: 'Analyzing document...',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should include documentId when available', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    documentId: 789,
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result[0].documentId).toBe(789);
            expect(result[0].securityReviewDocumentId).toBe(123);
        });
    });

    describe('transformToDocuments', () => {
        test('should transform mixed document types correctly', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test Document',
                    type: 'DOCUMENT',
                    documentId: 456,
                    summaryStatus: createSummaryStatusMock({
                        status: 'SUCCESS',
                        data: 3,
                    }),
                }),
                createDocumentMock({
                    id: 124,
                    name: 'Test Questionnaire',
                    type: 'QUESTIONNAIRE',
                    documentId: 457,
                    questionnaire: createQuestionnaireMock({
                        id: 789,
                        isCompleted: true,
                    }),
                }),
            ];

            const result = transformToDocuments(mockData);

            expect(result).toHaveLength(2);
            expect(result[0]).toStrictEqual({
                type: 'DOCUMENT',
                data: {
                    id: 'row-123',
                    name: 'Test Document',
                    value: 'Review 3 exceptions found',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            });
            expect(result[1]).toStrictEqual({
                type: 'QUESTIONNAIRE',
                data: {
                    id: 'row-124',
                    name: 'Test Questionnaire',
                    customData: {
                        summaryMessage: 'Analyzing questionnaire...',
                        isCompleted: true,
                        isManualUpload: false,
                        dateSent: '2023-01-01',
                        completedAt: '2023-01-02',
                        reminderDate: undefined,
                        responseId: undefined,
                        questionnaireId: 789,
                        documentId: 457,
                        securityReviewDocumentId: 124,
                    },
                },
            });
        });

        test('should handle unknown document type', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Unknown Type',
                    type: 'UNKNOWN_TYPE' as 'DOCUMENT',
                    documentId: 456,
                }),
            ];

            const result = transformToDocuments(mockData);

            expect(result).toStrictEqual([
                {
                    type: 'UNKNOWN_TYPE',
                    data: null,
                },
            ]);
        });

        test('should handle empty array', () => {
            const result = transformToDocuments([]);

            expect(result).toStrictEqual([]);
        });
    });

    describe('findRelevantQuestionnaire', () => {
        test('should return questionnaire with dateSent when available', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    type: 'DOCUMENT',
                }),
                createDocumentMock({
                    id: 124,
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        dateSent: undefined,
                    }),
                }),
                createDocumentMock({
                    id: 125,
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        dateSent: '2023-01-01',
                    }),
                }),
            ];

            const result = findRelevantQuestionnaire(mockData);

            expect(result?.id).toBe(125);
            expect(result?.questionnaire?.dateSent).toBe('2023-01-01');
        });

        test('should return first questionnaire when none have dateSent', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    type: 'DOCUMENT',
                }),
                createDocumentMock({
                    id: 124,
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        dateSent: undefined,
                    }),
                }),
                createDocumentMock({
                    id: 125,
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        dateSent: undefined,
                    }),
                }),
            ];

            const result = findRelevantQuestionnaire(mockData);

            expect(result?.id).toBe(124);
        });

        test('should return undefined when no questionnaires exist', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    type: 'DOCUMENT',
                }),
                createDocumentMock({
                    id: 124,
                    type: 'DOCUMENT',
                }),
            ];

            const result = findRelevantQuestionnaire(mockData);

            expect(result).toBeUndefined();
        });

        test('should return undefined for empty array', () => {
            const result = findRelevantQuestionnaire([]);

            expect(result).toBeUndefined();
        });

        test('should handle questionnaires without questionnaire data', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 124,
                    type: 'QUESTIONNAIRE',
                    questionnaire: null,
                }),
            ];

            const result = findRelevantQuestionnaire(mockData);

            expect(result?.id).toBe(124);
        });
    });

    describe('edge cases and error handling', () => {
        test('should handle empty arrays in transformDocumentsToQuestionnaires', () => {
            const result = transformDocumentsToQuestionnaires([]);

            expect(result).toStrictEqual([]);
        });

        test('should handle empty arrays in transformDocumentsToFiles', () => {
            const result = transformDocumentsToFiles([]);

            expect(result).toStrictEqual([]);
        });

        test('should handle questionnaire with all null values', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Questionnaire',
                type: 'QUESTIONNAIRE',
                documentId: 456,
                questionnaire: createQuestionnaireMock({
                    id: null as unknown as number,
                    isCompleted: null as unknown as boolean,
                    isManualUpload: null as unknown as boolean,
                    dateSent: null as unknown as string,
                    completedAt: null,
                    responseId: null,
                }),
                questionnaireReminder: null,
                summaryStatus: null,
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            expect(result.customData.isCompleted).toBeNull();
            expect(result.customData.isManualUpload).toBeNull();
            expect(result.customData.dateSent).toBeNull();
            expect(result.customData.completedAt).toBeUndefined();
            expect(result.customData.responseId).toBeUndefined();
            expect(result.customData.questionnaireId).toBeUndefined();
        });

        test('should handle document with missing documentId', () => {
            const mockData = createDocumentMock({
                id: 123,
                name: 'Test Document',
                type: 'DOCUMENT',
                documentId: undefined as unknown as number,
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.documentId).toBeUndefined();
            expect(result.securityReviewDocumentId).toBe(123);
        });

        test('should handle very large exception counts', () => {
            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 999999,
                }),
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('Review 999999 exceptions found');
        });

        test('should handle negative exception counts (treated as no exceptions)', () => {
            const mockData = createDocumentMock({
                type: 'QUESTIONNAIRE',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: -5,
                }),
            });

            const result = transformDocumentsToQuestionnaire(mockData);

            // Negative values are treated as "no exceptions" due to the logic: numberOfExceptions > 0
            expect(result.customData.summaryMessage).toBe('No callout found');
        });

        test('should handle feature flag toggling during execution', () => {
            // Start with feature enabled
            vi.mocked(sharedFeatureAccessModel).isVendorAISummaryEnabled = true;

            const mockData = createDocumentMock({
                type: 'DOCUMENT',
                summaryStatus: createSummaryStatusMock({
                    status: 'SUCCESS',
                    data: 3,
                }),
            });

            let result = transformDocumentsToFile(mockData);

            expect(result.value).toBe('Review 3 exceptions found');

            // Disable feature flag
            vi.mocked(sharedFeatureAccessModel).isVendorAISummaryEnabled =
                false;

            result = transformDocumentsToFile(mockData);
            expect(result.value).toBe('');

            // Re-enable feature flag
            vi.mocked(sharedFeatureAccessModel).isVendorAISummaryEnabled = true;

            result = transformDocumentsToFile(mockData);
            expect(result.value).toBe('Review 3 exceptions found');
        });

        test('should handle mixed questionnaire types in findRelevantQuestionnaire', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        dateSent: '2023-01-01',
                        isCompleted: false,
                    }),
                }),
                createDocumentMock({
                    id: 124,
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        dateSent: '2023-01-02',
                        isCompleted: true,
                    }),
                }),
            ];

            const result = findRelevantQuestionnaire(mockData);

            // Should return the first one with dateSent (123), not necessarily the completed one
            expect(result?.id).toBe(123);
        });

        test('should handle string IDs in document transformation', () => {
            const mockData = createDocumentMock({
                id: '123' as unknown as number,
                documentId: '456' as unknown as number,
                name: 'Test Document',
                type: 'DOCUMENT',
            });

            const result = transformDocumentsToFile(mockData);

            expect(result.id).toBe('row-123');
            expect(result.documentId).toBe('456');
            expect(result.securityReviewDocumentId).toBe('123');
        });
    });
});
