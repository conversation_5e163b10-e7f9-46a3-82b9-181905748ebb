import type { Snac<PERSON>barController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';

type SnackbarConfig = Parameters<SnackbarController['addSnackbar']>[0];

const createVendorErrorTitle = () => t`Error while creating the vendor.`;
const createVendorErrorDescription = () => t`Please try again later.`;
const uploadFileErrorTitle = () => t`Error while uploading the files.`;
const uploadFileSuccessTitle = () => t`All files uploaded successfully.`;
const sendQuestionnaireEmailErrorTitle = () =>
    t`Error while sending the questionnaire mail.`;
const sendQuestionnaireEmailSuccessTitle = () =>
    t`Questionnaires sent successfully`;
const closeButtonAriaLabel = () => t`Close`;

export const getCreatingVendorError = (): SnackbarConfig =>
    ({
        id: 'create-vendor-error',
        props: {
            title: createVendorErrorTitle(),
            description: createVendorErrorDescription(),
            severity: 'critical' as const,
            closeButtonAriaLabel: closeButtonAriaLabel(),
        },
    }) satisfies SnackbarConfig;

export const getUploadFileError = (): SnackbarConfig =>
    ({
        id: 'upload-file-error',
        props: {
            title: uploadFileErrorTitle(),
            severity: 'critical' as const,
            closeButtonAriaLabel: closeButtonAriaLabel(),
        },
    }) satisfies SnackbarConfig;

export const getUploadFileSuccess = (): SnackbarConfig =>
    ({
        id: 'upload-file-success',
        props: {
            title: uploadFileSuccessTitle(),
            severity: 'success' as const,
            closeButtonAriaLabel: closeButtonAriaLabel(),
        },
    }) satisfies SnackbarConfig;

export const getSendQuestionnaireEmailError = (): SnackbarConfig =>
    ({
        id: 'send-questionnaire-email-error',
        props: {
            title: sendQuestionnaireEmailErrorTitle(),
            severity: 'critical' as const,
            closeButtonAriaLabel: closeButtonAriaLabel(),
        },
    }) satisfies SnackbarConfig;

export const getSendQuestionnaireEmailSuccess = (): SnackbarConfig =>
    ({
        id: 'send-questionnaire-email-success',
        props: {
            title: sendQuestionnaireEmailSuccessTitle(),
            severity: 'success' as const,
            closeButtonAriaLabel: closeButtonAriaLabel(),
        },
    }) satisfies SnackbarConfig;
