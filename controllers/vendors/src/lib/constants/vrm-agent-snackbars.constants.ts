import type { SnackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';

const vrmAssessmentMissingImpactLevelTitle = () => t`Impact Level Required`;
const vrmAssessmentMissingImpactLevelDescription = () =>
    t`You need to assign an impact level to this vendor`;
const vrmAssessmentStartedTitle = () => t`Assessment Started`;
const vrmAssessmentStartedDescription = () =>
    t`Security review assessment has been initiated.`;
const closeButtonAriaLabel = () => t`Close`;

export const getVrmAssessmentMissingImpactLevelError = (): Parameters<
    SnackbarController['addSnackbar']
>[0] =>
    ({
        id: 'vrm-assessment-missing-impact-level',
        props: {
            title: vrmAssessmentMissingImpactLevelTitle(),
            description: vrmAssessmentMissingImpactLevelDescription(),
            severity: 'critical' as const,
            closeButtonAriaLabel: closeButtonAriaLabel(),
        },
    }) satisfies Parameters<SnackbarController['addSnackbar']>[0];

export const getVrmAssessmentStartedSuccess = (): Parameters<
    SnackbarController['addSnackbar']
>[0] =>
    ({
        id: 'vrm-assessment-started',
        props: {
            title: vrmAssessmentStartedTitle(),
            description: vrmAssessmentStartedDescription(),
            severity: 'success' as const,
            closeButtonAriaLabel: closeButtonAriaLabel(),
        },
    }) satisfies Parameters<SnackbarController['addSnackbar']>[0];
