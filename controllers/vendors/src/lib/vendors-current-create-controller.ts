import { isEmpty, isNaN } from 'lodash-es';
import {
    integrationFormToRequestAdapter,
    type VendorInternalDetailsFormValuesType,
} from '@components/vendors-current-add-vendor';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { vendorsControllerCreateVendorMutation } from '@globals/api-sdk/queries';
import type { VendorRequestDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormValues } from '@ui/forms';
import { getCreatingVendorError } from './constants/vendors-create-snackbars.constants';
import { mergeCustomFieldsIntoValues } from './helpers/form-custom-fields.helper';
import { sharedVendorCustomFieldsController } from './vendor-custom-fields.controller';
import { sharedVendorsDetailsController } from './vendors-details-controller';

class VendorsCreateCurrentVendorController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorDetails: FormValues | null = null;
    vendorInternalDetails: VendorInternalDetailsFormValuesType | null = null;
    vendorImpactAssessmentDetails: FormValues | null = null;
    vendorId: number | null = null;
    vendorName = '';

    createVendorMutation = new ObservedMutation(
        vendorsControllerCreateVendorMutation,
    );

    get isLoading(): boolean {
        return this.createVendorMutation.isPending;
    }

    get isVendorRiskManagementProEnabled(): boolean {
        return sharedFeatureAccessModel.isVendorRiskManagementProEnabled;
    }

    saveVendorDetails = (formValues: FormValues) => {
        const passwordPolicyGroup = formValues.passwordPolicyGroup as
            | FormValues
            | undefined;
        const passwordPolicy = passwordPolicyGroup?.passwordPolicy as
            | FormValues
            | undefined;

        const passwordMinLength = passwordPolicyGroup?.passwordMinLength as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            name: formValues.name ?? undefined,
            url: isEmpty(formValues.url) ? undefined : formValues.url,
            servicesProvided: isEmpty(formValues.servicesProvided)
                ? undefined
                : formValues.servicesProvided,
            passwordPolicy: passwordPolicy?.value ?? undefined,
            passwordRequiresMinLength:
                passwordPolicyGroup?.passwordRequiresMinLength ?? false,
            passwordMinLength: isNaN(passwordMinLength?.value)
                ? undefined
                : Number(passwordMinLength?.value),
            passwordRequiresNumber:
                passwordPolicyGroup?.passwordRequiresNumber ?? false,
            passwordRequiresSymbol:
                passwordPolicyGroup?.passwordRequiresSymbol ?? false,
            passwordMfaEnabled:
                passwordPolicyGroup?.passwordMfaEnabled ?? false,
            privacyUrl: isEmpty(formValues.privacyUrl)
                ? undefined
                : formValues.privacyUrl,
            termsUrl: isEmpty(formValues.termsUrl)
                ? undefined
                : formValues.termsUrl,
            contactAtVendor: isEmpty(formValues.contactAtVendor)
                ? undefined
                : formValues.contactAtVendor,
            contactsEmail: isEmpty(formValues.contactsEmail)
                ? undefined
                : (formValues.contactsEmail as string).trim(),
        };

        mergeCustomFieldsIntoValues(formValues, newValues);

        this.vendorDetails = newValues;
    };

    saveVendorInternalDetails = (formValues: FormValues) => {
        const integrations = formValues.integrations as
            | ListBoxItemData[]
            | undefined;

        const integrationsIds = integrationFormToRequestAdapter(integrations);

        const status = formValues.status as ListBoxItemData | undefined;
        const type = formValues.type as ListBoxItemData | undefined;
        const category = formValues.category as ListBoxItemData | undefined;
        const risk = formValues.risk as ListBoxItemData | undefined;
        const user = formValues.user as ListBoxItemData | undefined;
        const impactLevel = formValues.impactLevel as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            status: status?.value ?? undefined,
            type: type?.value ?? undefined,
            category: category?.value ?? undefined,
            risk: risk?.value ?? undefined,
            impactLevel: impactLevel?.value ?? undefined,
            dataStored: formValues.dataStored ?? undefined,
            hasPii: formValues.hasPii ?? undefined,
            isSubProcessor: formValues.isSubProcessor ?? undefined,
            location: formValues.location ?? undefined,
            integrations: integrationsIds,
            userId: isNaN(user?.id) ? undefined : Number(user?.id),
            user: user ?? undefined,
            contact: formValues.contact ?? undefined,
            cost: isNaN(formValues.cost) ? undefined : formValues.cost,
            notes: formValues.notes ?? undefined,
        };

        mergeCustomFieldsIntoValues(formValues, newValues);

        this.vendorInternalDetails = newValues;
        !this.isVendorRiskManagementProEnabled &&
            this.createCurrentVendorDetails(newValues);
    };

    saveImpactAssessmentDetails = (formValues: FormValues) => {
        const impactLevel = formValues.impactLevel as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            dataAccessedOrProcessedList: isEmpty(
                formValues.dataAccessedOrProcessedList,
            )
                ? undefined
                : formValues.dataAccessedOrProcessedList,
            operationalImpact: isEmpty(formValues.operationalImpact)
                ? undefined
                : formValues.operationalImpact,
            environmentAccess: isEmpty(formValues.environmentAccess)
                ? undefined
                : formValues.environmentAccess,
            impactLevel: impactLevel?.value ?? undefined,
        };

        this.createCurrentVendorDetails(newValues);
    };

    createCurrentVendorDetails = (newValues: FormValues) => {
        const mutatedVendorDetails = {
            ...this.vendorDetails,
            ...this.vendorInternalDetails,
            ...newValues,
            isSubProcessorActive: false,
            confirmed: true,
        } as VendorRequestDto;

        this.createVendorMutation.mutate({
            body: mutatedVendorDetails,
        });

        when(
            () => !this.createVendorMutation.isPending,
            () => {
                const { response, hasError } = this.createVendorMutation;

                if (hasError) {
                    snackbarController.addSnackbar(getCreatingVendorError());
                }
                if (response?.id) {
                    this.vendorId = response.id;
                    this.vendorName = response.name;
                    this.submitCustomFields(response.id);
                }
            },
        );
    };

    submitCustomFields = (vendorId: number) => {
        if (!vendorId) {
            return;
        }

        if (sharedFeatureAccessModel.isCustomFieldsEnabled) {
            const vendorDetailsCustomFields = this.vendorDetails
                ? sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                      this.vendorDetails,
                      sharedVendorCustomFieldsController.vendorDetailsCustomFieldsList,
                  )
                : null;

            const internalDetailsCustomFields = this.vendorInternalDetails
                ? sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                      this.vendorInternalDetails as FormValues,
                      sharedVendorCustomFieldsController.vendorInternalDetailsCustomFieldsList,
                  )
                : null;

            const customFieldsToSubmit = {
                customFieldSubmissions: [
                    ...(Array.isArray(
                        vendorDetailsCustomFields?.customFieldSubmissions,
                    )
                        ? vendorDetailsCustomFields.customFieldSubmissions
                        : []),
                    ...(Array.isArray(
                        internalDetailsCustomFields?.customFieldSubmissions,
                    )
                        ? internalDetailsCustomFields.customFieldSubmissions
                        : []),
                ],
            } as Record<string, unknown>;

            sharedVendorCustomFieldsController.vendorCustomFieldsSubmission(
                vendorId,
                customFieldsToSubmit,
            );
        }
    };

    cleanVendorValues = () => {
        this.vendorDetails = null;
        this.vendorInternalDetails = null;
        this.vendorImpactAssessmentDetails = null;
        this.vendorId = null;
        this.vendorName = '';
        sharedVendorsDetailsController.vendorDetailsTemporalValues = null;
    };
}

export const sharedVendorsCreateCurrentVendorController =
    new VendorsCreateCurrentVendorController();
