import { isEmpty, isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    providerCatalogControllerGetProviderResourceAttributesOptions,
    providerCatalogControllerGetProviderResourceSchemaOptions,
    providerCatalogControllerGetProviderResourcesOptions,
    providerCatalogControllerGetProviderServicesOptions,
} from '@globals/api-sdk/queries';
import type {
    ProviderCatalogProviderResourcesResponseDto,
    ProviderCatalogProviderServicesResponseDto,
    ProviderResourceAttributesResponseDto,
    ProviderResourceSchemaResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, reaction } from '@globals/mobx';
import { type Provider, providers } from '@globals/providers';
import { sharedWorkspacesController } from '@globals/workspaces';

class ResourceGuideController {
    selectedProvider: ListBoxItemData | undefined;
    selectedService: ListBoxItemData | undefined;
    selectedResource: ListBoxItemData | undefined;

    constructor() {
        makeAutoObservable(this);

        // Auto-select "All services" when services are loaded
        reaction(
            () => this.availableServices,
            (services) => {
                // Only auto-select if we have a provider but no service selected
                if (
                    !this.selectedProvider ||
                    this.selectedService ||
                    isEmpty(services)
                ) {
                    return;
                }

                const allServicesOption = services[0];

                if (allServicesOption.value === 'all-services') {
                    this.selectedService = allServicesOption;
                }
            },
        );
    }

    providerServicesQuery = new ObservedQuery(
        providerCatalogControllerGetProviderServicesOptions,
    );

    providerResourcesQuery = new ObservedQuery(
        providerCatalogControllerGetProviderResourcesOptions,
    );

    resourceSchemaQuery = new ObservedQuery(
        providerCatalogControllerGetProviderResourceSchemaOptions,
    );

    providerCatalogResourceAttributesQuery = new ObservedQuery(
        providerCatalogControllerGetProviderResourceAttributesOptions,
    );

    get isLoadingServices(): boolean {
        return this.providerServicesQuery.isLoading;
    }

    get isLoadingResources(): boolean {
        return this.providerResourcesQuery.isLoading;
    }

    get isLoadingAttributes(): boolean {
        return this.providerCatalogResourceAttributesQuery.isLoading;
    }

    get isLoadingSchema(): boolean {
        return this.resourceSchemaQuery.isLoading;
    }

    get isLoading(): boolean {
        return (
            this.isLoadingServices ||
            this.isLoadingResources ||
            this.isLoadingSchema
        );
    }

    get providerServicesData(): ProviderCatalogProviderServicesResponseDto | null {
        return this.providerServicesQuery.data ?? null;
    }

    get providerResourcesData(): ProviderCatalogProviderResourcesResponseDto | null {
        return this.providerResourcesQuery.data ?? null;
    }

    get resourceSchemaData(): ProviderResourceSchemaResponseDto | null {
        return this.resourceSchemaQuery.data ?? null;
    }

    get attributeDictionaryData(): ProviderResourceAttributesResponseDto | null {
        return this.providerCatalogResourceAttributesQuery.data ?? null;
    }

    get availableProviders(): ListBoxItemData[] {
        // Only show AWS, GCP, and Azure providers for resource guide
        const allowedProviders: Provider[] = ['AWS', 'GCP', 'AZURE'];

        return Object.values(providers)
            .filter((provider) => allowedProviders.includes(provider.id))
            .map((provider) => ({
                id: provider.id,
                label: provider.name,
                value: provider.id,
            }));
    }

    get availableServices(): ListBoxItemData[] {
        if (!this.providerServicesData?.services) {
            return [];
        }

        // Add "All services" option as first item
        const allServicesOption: ListBoxItemData = {
            id: 'all-services',
            label: t`All services`,
            value: 'all-services',
        };

        const serviceOptions = this.providerServicesData.services.map(
            (service) => ({
                id: service.name,
                label: service.name,
                value: service.name,
            }),
        );

        return [
            allServicesOption,
            ...serviceOptions.sort((a, b) => a.label.localeCompare(b.label)),
        ];
    }

    get availableResources(): ListBoxItemData[] {
        if (!this.providerResourcesData?.resources) {
            return [];
        }

        return this.providerResourcesData.resources
            .map((resource) => ({
                id: resource.name,
                label: resource.name,
                value: resource.name,
            }))
            .sort((a, b) => a.label.localeCompare(b.label));
    }

    get currentSchema(): string | null {
        return this.resourceSchemaData?.schema ?? null;
    }

    get hasValidSelection(): boolean {
        return !isNil(this.selectedProvider) && !isNil(this.selectedResource);
    }

    get isEmpty(): boolean {
        return isNil(this.selectedProvider);
    }

    get canShowSchema(): boolean {
        return this.hasValidSelection && !isNil(this.currentSchema);
    }

    selectProvider = (provider: ListBoxItemData): void => {
        try {
            // Reset dependent selections
            this.selectedProvider = provider;
            this.selectedService = undefined;
            this.selectedResource = undefined;

            // Clear previous data
            this.providerResourcesQuery.unload();
            this.resourceSchemaQuery.unload();

            const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

            if (!workspaceId) {
                throw new Error('No workspace selected');
            }

            // Load services for the selected provider
            this.providerServicesQuery.load({
                path: { clientType: provider.value ?? provider.id },
                query: { workspaceId: String(workspaceId) },
            });

            // Load all resources for the provider (without service filter initially)
            this.providerResourcesQuery.load({
                path: { clientType: provider.value ?? provider.id },
                query: { workspaceId: String(workspaceId) },
            });
        } catch (error) {
            this.handleError(error, t`Failed to load provider data`);
        }
    };

    selectService = (service: ListBoxItemData): void => {
        try {
            this.selectedService = service;
            this.selectedResource = undefined;
            this.resourceSchemaQuery.unload();

            if (!this.selectedProvider) {
                return;
            }

            const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

            if (!workspaceId) {
                throw new Error('No workspace selected');
            }

            // If "All services" is selected, we already have all resources
            if (service.value === 'all-services') {
                return;
            }

            // Load resources filtered by service
            this.providerResourcesQuery.load({
                path: {
                    clientType:
                        this.selectedProvider.value ?? this.selectedProvider.id,
                },
                query: {
                    workspaceId: String(workspaceId),
                    service: service.value ?? service.id,
                },
            });
        } catch (error) {
            this.handleError(error, t`Failed to load service resources`);
        }
    };

    selectResource = (resource: ListBoxItemData): void => {
        try {
            this.selectedResource = resource;

            if (!this.selectedProvider) {
                return;
            }

            const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

            if (!workspaceId) {
                throw new Error('No workspace selected');
            }

            // Load schema for the selected resource
            this.resourceSchemaQuery.load({
                path: {
                    clientType:
                        this.selectedProvider.value ?? this.selectedProvider.id,
                    resourceName: resource.value ?? resource.id,
                },
                query: { workspaceId: String(workspaceId) },
            });
        } catch (error) {
            this.handleError(error, t`Failed to load resource schema`);
        }
    };

    loadAttributeDictionary = (): void => {
        try {
            if (
                !this.selectedProvider?.value ||
                !this.selectedResource?.value
            ) {
                return;
            }

            this.providerCatalogResourceAttributesQuery.load({
                path: {
                    resourceName: this.selectedResource.value,
                    clientType: this.selectedProvider.value,
                },
                query: {
                    mode: 'RECURSIVE_CHILDREN_WITH_ARRAYS',
                },
            });
        } catch (error) {
            this.handleError(error, t`Failed to load attribute dictionary`);
        }
    };

    handleError = (error: unknown, message: string): void => {
        console.error('ResourceGuideController error:', error);

        snackbarController.addSnackbar({
            id: `resource-guide-error-${Date.now()}`,
            props: {
                title: message,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };
}

export const sharedResourceGuideController = new ResourceGuideController();
