import { sharedConnectionsController } from '@controllers/connections';
import { UtilitiesBase, type UtilitiesBaseConfig } from './utilities-base';

export interface UtilitiesTicketsConfig extends UtilitiesBaseConfig {
    overrides?: {
        enabled?: boolean;
    };
}

export class UtilitiesTicketsController extends UtilitiesBase<UtilitiesTicketsConfig> {
    override get isEnabled(): boolean {
        return sharedConnectionsController.hasTicketingConnectionWithWriteAccess;
    }
}

export const sharedUtilitiesTicketsController =
    new UtilitiesTicketsController();
