import { ticketsControllerGetTicketMonitoringOptions } from '@globals/api-sdk/queries';
import type { TicketMonitoringResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationDetailsQuery = new ObservedQuery(
        ticketsControllerGetTicketMonitoringOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationDetailsQuery.isLoading;
    }

    get ticketAutomationDetails(): TicketMonitoringResponseDto | null {
        return this.ticketAutomationDetailsQuery.data;
    }

    loadTicketAutomationDetails = (ticketId: number) => {
        this.ticketAutomationDetailsQuery.load({
            path: { id: ticketId },
        });
    };
}

export const sharedTicketAutomationDetailsController =
    new TicketAutomationDetailsController();
