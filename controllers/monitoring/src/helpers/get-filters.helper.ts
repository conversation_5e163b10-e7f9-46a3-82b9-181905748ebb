import { isEmpty, isNil } from 'lodash-es';
import type { GlobalFilterState } from '@cosmos/components/datatable';
import {
    MONITORING_ALLOWED_CONNECTION_ID,
    MONITORING_ALLOWED_CONTROLS_ID,
    MONITORING_ALLOWED_FRAMEWORKS_ID,
} from '../lib/constants/monitoring-filters-id.constant';
import type { ProductionFilterType } from '../types/monitoring-controller.types';

interface Option {
    id: string;
    label: string;
    value: string;
}
const formatFilterArrayById = (
    filters: GlobalFilterState['filters'],
    id: string,
) => {
    if (isEmpty(filters[id])) {
        return undefined;
    }
    const filterValues = filters[id].value as Option[] | undefined;

    return filterValues?.map((conn) => conn.value);
};

export const getFilters = (
    filters: GlobalFilterState['filters'],
): ProductionFilterType => {
    const globalFilters = Object.fromEntries(
        Object.entries(filters)
            .filter(
                ([, filter]) => !isEmpty(filter.value) && !isNil(filter.value),
            )
            .map(([key, filter]) => [key, filter.value]),
    );

    return {
        ...globalFilters,
        [MONITORING_ALLOWED_CONNECTION_ID]: formatFilterArrayById(
            filters,
            MONITORING_ALLOWED_CONNECTION_ID,
        ),
        [MONITORING_ALLOWED_CONTROLS_ID]: formatFilterArrayById(
            filters,
            MONITORING_ALLOWED_CONTROLS_ID,
        ),
        [MONITORING_ALLOWED_FRAMEWORKS_ID]: formatFilterArrayById(
            filters,
            MONITORING_ALLOWED_FRAMEWORKS_ID,
        ),
    };
};
