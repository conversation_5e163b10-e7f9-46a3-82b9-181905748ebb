import { ticketsControllerGetTicketFieldsByIssueTypeOptions } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import type { TicketFields } from './types/ticket-fields.type';

class TicketAutomationTicketFieldsController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationTicketFields = new ObservedQuery(
        ticketsControllerGetTicketFieldsByIssueTypeOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationTicketFields.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationTicketFields.hasError;
    }

    get ticketAutomationIssueTypeList(): TicketFields {
        return (this.ticketAutomationTicketFields.data?.fields ??
            {}) as unknown as TicketFields;
    }

    loadTicketAutomationFields = ({
        connectionId,
        projectId,
        issueTypeId,
    }: {
        connectionId: number;
        projectId: string;
        issueTypeId: string;
    }) => {
        this.ticketAutomationTicketFields.load({
            query: { connectionId, projectId, issueTypeId },
        });
    };
}

export const sharedTicketAutomationTicketFieldsController =
    new TicketAutomationTicketFieldsController();
