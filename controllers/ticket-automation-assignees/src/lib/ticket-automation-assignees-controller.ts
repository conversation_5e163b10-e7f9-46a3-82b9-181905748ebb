import { ticketsControllerGetTicketAssignableInfiniteOptions } from '@globals/api-sdk/queries';
import type { TicketUserResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

class TicketAutomationAssigneesController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationAssigneesQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketAssignableInfiniteOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationAssigneesQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationAssigneesQuery.hasError;
    }

    get hasNextPage(): boolean {
        return this.ticketAutomationAssigneesQuery.hasNextPage;
    }

    get ticketAutomationAssigneeList(): TicketUserResponseDto[] {
        return (
            this.ticketAutomationAssigneesQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get isFetching(): boolean {
        return this.ticketAutomationAssigneesQuery.isFetching;
    }

    loadNextPage = (): void => {
        this.ticketAutomationAssigneesQuery.nextPage();
    };

    loadTicketAutomationAssignees = ({
        connectionId,
        projectId,
        search,
    }: {
        connectionId: number;
        projectId: number;
        search?: string;
    }) => {
        this.ticketAutomationAssigneesQuery.load({
            path: { projectId },
            query: { connectionId, q: search?.trim() },
        });
    };

    onFetchTicketAutomationAssignees = ({
        connectionId,
        projectId,
        search,
        increasePage,
    }: {
        connectionId: number;
        projectId: number;
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }
        this.loadTicketAutomationAssignees({
            connectionId,
            projectId,
            search,
        });
    };
}

export const sharedTicketAutomationAssigneesController =
    new TicketAutomationAssigneesController();
