import { isEmpty, isObject } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { policiesControllerGetPoliciesSummarizedDataWithOwnerInfiniteOptions } from '@globals/api-sdk/queries';
import type { PoliciesSummarizedDataWithOwnerDto } from '@globals/api-sdk/types';
import {
    action,
    makeAutoObservable,
    ObservedInfiniteQuery,
} from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getUserInitials } from '@helpers/user';
import { sharedMapPoliciesModel } from '@models/controls';
import { DEFAULT_POLICY_STATUSES } from './policies.controller.constants';

export interface PolicyItem extends ListBoxItemData {
    creationDate: string;
    policyData: PoliciesSummarizedDataWithOwnerDto;
}

export const MAP_POLICIES_MODAL_ID = 'map-policies-modal';

class PoliciesLibraryInfiniteListController {
    constructor() {
        makeAutoObservable(this);
    }

    searchTerm = '';

    policiesInfiniteQuery = new ObservedInfiniteQuery(
        policiesControllerGetPoliciesSummarizedDataWithOwnerInfiniteOptions,
    );

    get hasNextPage(): boolean {
        return this.policiesInfiniteQuery.hasNextPage;
    }

    get policiesList(): PoliciesSummarizedDataWithOwnerDto[] {
        return (
            this.policiesInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get policiesListAsItems(): PolicyItem[] {
        return this.policiesList.map(this.mapPolicyToItem);
    }

    mapPolicyToItem = (
        policy: PoliciesSummarizedDataWithOwnerDto,
    ): PolicyItem => {
        const publishedDateStr = this.getPublishedDateString(policy);

        const fallbackText = getUserInitials({
            firstName: policy.currentOwner.firstName,
            lastName: policy.currentOwner.lastName,
        });

        return {
            id: String(policy.id),
            value: String(policy.id),
            label: policy.name,
            description: publishedDateStr,
            creationDate: publishedDateStr,
            policyData: policy,
            avatar: {
                fallbackText,
                imgSrc: policy.currentOwner.avatarUrl,
                imgAlt: 'Policy',
            },
        };
    };

    getPublishedDateString = (
        policy: PoliciesSummarizedDataWithOwnerDto,
    ): string => {
        const publishedVersion = policy.versions.find(
            (v) => v.isCurrentPublished,
        );

        if (!publishedVersion?.publishedAt) {
            return 'Pending publish';
        }

        try {
            return `Published on: ${formatDate('field', publishedVersion.publishedAt)}`;
        } catch (error) {
            console.error('Error formatting date:', error);

            return 'Pending publish';
        }
    };

    get hasError(): boolean {
        return this.policiesInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.policiesInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.policiesInfiniteQuery.isFetching;
    }

    search(term: string, excludeControlId?: number): void {
        this.searchTerm = term;
        this.load(excludeControlId);
    }

    load(excludeControlId?: number): void {
        this.policiesInfiniteQuery.load({
            query: {
                page: 1,
                limit: DEFAULT_PAGE_SIZE,
                q: this.searchTerm,
                policyStatuses: DEFAULT_POLICY_STATUSES,
                ...(excludeControlId && { excludeControlId }),
            },
        });
    }

    loadNextPage(): void {
        if (this.hasNextPage && !this.isFetching) {
            this.policiesInfiniteQuery.nextPage();
        }
    }

    addSelectedPolicies(policies: PoliciesSummarizedDataWithOwnerDto[]) {
        if (!Array.isArray(policies)) {
            snackbarController.addSnackbar({
                id: 'map-policies-error',
                props: {
                    title: 'Invalid policies format',
                    description: 'Expected an array of policies',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return;
        }

        const validPolicies = policies.filter((policy) => {
            if (!isObject(policy) || !('id' in policy)) {
                snackbarController.addSnackbar({
                    id: 'invalid-policy-error',
                    props: {
                        title: 'Invalid policy object',
                        description: 'Policy is missing required properties',
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                return false;
            }

            return true;
        });

        const newPolicies = validPolicies.filter(
            (item) =>
                !sharedMapPoliciesModel.selectedPolicies.some(
                    (existing) => existing.id === item.id,
                ),
        );

        sharedMapPoliciesModel.selectedPolicies = [
            ...sharedMapPoliciesModel.selectedPolicies,
            ...newPolicies,
        ];
    }

    removePolicy(id: number) {
        sharedMapPoliciesModel.selectedPolicies =
            sharedMapPoliciesModel.selectedPolicies.filter(
                (item) => item.id !== id,
            );
    }

    clearAllPolicies() {
        sharedMapPoliciesModel.selectedPolicies = [];
    }

    getPolicyUrl(policyId: number) {
        return `${routeController.userPartOfUrl}/governance/policies/builder/${policyId}/overview`;
    }

    saveSelectedPolicies(
        selectedPolicies: ListBoxItemData[] | ListBoxItemData,
    ) {
        action(() => {
            this.clearAllPolicies();

            if (
                !(Array.isArray(selectedPolicies) && isEmpty(selectedPolicies))
            ) {
                const policiesToAdd = Array.isArray(selectedPolicies)
                    ? selectedPolicies.map(
                          (item) => (item as PolicyItem).policyData,
                      )
                    : [(selectedPolicies as PolicyItem).policyData];

                this.addSelectedPolicies(policiesToAdd);
            }
        })();

        modalController.closeModal(MAP_POLICIES_MODAL_ID);
    }

    fetchOptions({
        search,
        increasePage,
        excludeControlId,
    }: {
        search?: string;
        increasePage?: boolean;
        excludeControlId?: number;
    }) {
        action(() => {
            if (increasePage) {
                this.loadNextPage();
            } else {
                this.search(search || '', excludeControlId);
            }
        })();
    }

    unload() {
        this.policiesInfiniteQuery.unload();
    }
}

export const sharedPoliciesLibraryInfiniteListController =
    new PoliciesLibraryInfiniteListController();
