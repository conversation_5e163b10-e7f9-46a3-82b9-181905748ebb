import { isEmpty, isError, isNil, isObject, omit } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { modalController } from '@controllers/modal';
import {
    sharedTicketAutomationAdditionalFieldsController,
    sharedTicketAutomationCyclesController,
    sharedTicketAutomationFoldersController,
    sharedTicketAutomationListsController,
    sharedTicketAutomationParentsController,
    sharedTicketAutomationStatesController,
    sharedTicketAutomationStructureController,
    sharedTicketAutomationTeamsController,
    sharedTicketAutomationTicketTypesController,
    sharedTicketAutomationUsersController,
    sharedTicketAutomationWorkspacesController,
} from '@controllers/ticket-automation';
import { sharedTicketAutomationAssigneesController } from '@controllers/ticket-automation-assignees';
import { sharedTicketAutomationIssueTypesController } from '@controllers/ticket-automation-issue-types';
import { sharedTicketAutomationLabelsController } from '@controllers/ticket-automation-labels';
import { sharedTicketAutomationProjectsController } from '@controllers/ticket-automation-projects';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    ExternalTicketsControllerGetTicketStructureData,
    TicketsControllerGetTicketStructureData,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, makeAutoObservable, runInAction, toJS } from '@globals/mobx';
import type { BaseProvider, Provider } from '@globals/providers';
import {
    CREATE_TICKET_REQUIRED_BY_PROVIDER,
    PROVIDERS_WITH_WORKSPACE,
} from './constants/create-ticket.constants';
import { mapConnectionToBoxItem } from './helpers/mapConnectionToBoxItem.helper';
import type { CreateTicketClientType } from './types/create-ticket-client-type.type';
import type { CreateTicketDynamicField } from './types/create-ticket-dynamic-field.type';
import type { CreateTicketFormData } from './types/create-ticket-form-data.type';
import type { CreateTicketFormField } from './types/create-ticket-form-field.type';
import type {
    CreateTicketPayload,
    CreateTicketPayloadField,
} from './types/create-ticket-payload.type';

class CreateTicketController {
    /**
     * Form data.
     */
    isCreating = false;
    formData: CreateTicketFormData;
    constructor() {
        makeAutoObservable(this);
        this.formData = {
            description: t`Created in Drata`,
            summary: t`Created in Drata`,
            fields: {},
        };
    }

    /**
     * Callback function to handle ticket creation.
     */
    onCreateTicket: ((payload: CreateTicketPayload) => Promise<void>) | null =
        null;

    initData: Partial<CreateTicketFormData> = {};

    /**
     * Initialize with callback and optional default description.
     */
    initialize = (
        onCreateTicket: (payload: CreateTicketPayload) => Promise<void>,
        initData: Partial<CreateTicketFormData> = {},
    ) => {
        this.onCreateTicket = onCreateTicket;
        this.initData = initData;
        this.initData.fields = this.initFields(initData);
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
    };

    initFields = (initData: Partial<CreateTicketFormData>) => {
        const fields = initData.fields ?? {};

        if (initData.description) {
            fields.description = {
                name: 'description',
                type: 'string',
                value: initData.description,
            };
        }

        if (initData.summary) {
            fields.subject = {
                name: 'subject',
                type: 'string',
                value: initData.summary,
            };
            fields.summary = {
                name: 'summary',
                type: 'string',
                value: initData.summary,
            };
            fields.short_summary = {
                name: 'summary',
                type: 'string',
                value: initData.summary,
            };
        }

        if (initData.assigneeId) {
            fields.assigneeId = {
                name: 'assigneeId',
                type: 'user',
                item: {
                    id: initData.assigneeId,
                    label: initData.assigneeId,
                },
            };
        }

        return fields;
    };

    resetForm = (data?: Partial<CreateTicketFormData>) => {
        this.invalidProps = [];
        this.invalidDynamicFields = [];

        this.formData = {
            description: t`Created in Drata`,
            summary: t`Created in Drata`,
            fields: {},
            ...data,
        };
    };

    get isLoading() {
        return (
            this.isConnectionsLoading ||
            this.isWorkspacesLoading ||
            this.isTeamsLoading ||
            this.isProjectsLoading ||
            this.isFoldersLoading ||
            this.isListsLoading ||
            this.isIssueTypesLoading ||
            this.isTicketTypesLoading ||
            this.isAdditionalFieldsLoading
        );
    }

    // Connection field

    get isConnectionsLoading() {
        return sharedConnectionsController.isLoading;
    }

    get availableConnections() {
        return sharedConnectionsController.allTicketingConnectionWithWriteAccess.map(
            mapConnectionToBoxItem,
        );
    }

    get showConnectionField(): boolean {
        return this.availableConnections.length > 1;
    }

    get connectionId(): number | undefined {
        if (!this.showConnectionField) {
            const connectionId = Number(this.availableConnections[0]?.id);

            return Number.isNaN(connectionId) ? undefined : connectionId;
        }

        return this.formData.connectionId;
    }

    get selectedConnection():
        | (ListBoxItemData & { provider?: BaseProvider })
        | undefined {
        if (!this.connectionId) {
            return undefined;
        }

        return this.availableConnections.find(
            (conn) => conn.id === String(this.connectionId),
        );
    }

    get providerId(): Provider | undefined {
        return this.selectedConnection?.provider?.id;
    }

    setConnection = (connection: ListBoxItemData) => {
        const connectionId = Number(connection.id);

        this.formData.connectionId = connectionId;
        // Reset dependent fields
        this.resetForm({ ...this.initData, connectionId });

        if (this.providerId === 'LINEAR') {
            this.loadTeams(connectionId);
        } else if (
            this.providerId &&
            PROVIDERS_WITH_WORKSPACE.includes(this.providerId)
        ) {
            this.loadWorkspaces(connectionId);
        } else {
            this.loadProjects(connectionId);
        }
    };

    // Workspace field

    loadWorkspaces = (connectionId: number) => {
        sharedTicketAutomationWorkspacesController.loadTicketAutomationWorkspaces(
            connectionId,
        );
    };

    get isWorkspacesLoading() {
        return sharedTicketAutomationWorkspacesController.isLoading;
    }

    get workspaces(): ListBoxItemData[] {
        return sharedTicketAutomationWorkspacesController.ticketAutomationWorkspacesList.map(
            (workspace) => ({
                id: String(workspace.value),
                label: workspace.label,
                value: String(workspace.value),
            }),
        );
    }

    get selectedWorkspace(): ListBoxItemData | undefined {
        if (!this.formData.workspaceId) {
            return undefined;
        }

        return this.workspaces.find(
            (workspace) => workspace.id === String(this.formData.workspaceId),
        );
    }

    // Team field

    loadTeams = (connectionId: number) => {
        sharedTicketAutomationTeamsController.loadTicketAutomationTeams(
            connectionId,
        );
    };

    get isTeamsLoading() {
        return sharedTicketAutomationTeamsController.isLoading;
    }

    get teams(): ListBoxItemData[] {
        return sharedTicketAutomationTeamsController.ticketAutomationTeamsList.map(
            (team) => ({
                id: String(team.value),
                label: team.label,
                value: String(team.value),
            }),
        );
    }

    get selectedTeam(): ListBoxItemData | undefined {
        if (!this.formData.teamId) {
            return undefined;
        }

        return this.teams.find(
            (team) => team.id === String(this.formData.teamId),
        );
    }

    setTeam = (team: ListBoxItemData) => {
        this.formData.teamId = team.id;
    };

    loadProjects = (connectionId: number, workspaceId?: string | null) => {
        sharedTicketAutomationProjectsController.loadTicketAutomationProjects(
            connectionId,
            workspaceId,
        );
    };

    get isProjectsLoading() {
        return sharedTicketAutomationProjectsController.isLoading;
    }

    get projects(): ListBoxItemData[] {
        return sharedTicketAutomationProjectsController.ticketAutomationProjectList.map(
            (project) => {
                let projectId = project.value;

                // this endpoint sometimes returns project.value as a string and sometimes as an object like { projectId: string }
                try {
                    const parsed = JSON.parse(project.value) as {
                        projectId: string;
                    };

                    if (parsed.projectId) {
                        projectId = parsed.projectId;
                    }
                } catch {
                    // not JSON, keep as is
                }

                return {
                    id: projectId,
                    label: project.label,
                    value: project.value,
                };
            },
        );
    }

    get selectedProject(): ListBoxItemData | undefined {
        if (!this.formData.projectId) {
            return undefined;
        }

        return this.projects.find(
            (project) => project.id === this.formData.projectId,
        );
    }

    // Folder field

    loadFolders = (connectionId: number, projectId: string) => {
        sharedTicketAutomationFoldersController.loadTicketAutomationFolders(
            connectionId,
            projectId,
        );
    };

    get isFoldersLoading() {
        return sharedTicketAutomationFoldersController.isLoading;
    }

    get folders(): ListBoxItemData[] {
        return sharedTicketAutomationFoldersController.ticketAutomationFoldersList.map(
            (folder) => ({
                id: folder.value,
                label: folder.label,
                value: folder.value,
            }),
        );
    }

    get selectedFolder(): ListBoxItemData | undefined {
        if (!this.formData.folderId) {
            return undefined;
        }

        return this.folders.find(
            (folder) => folder.id === this.formData.folderId,
        );
    }

    setFolder = (folder: ListBoxItemData) => {
        this.formData.folderId = folder.id;
    };

    // List field

    loadLists = (params: {
        connectionId: number;
        directoryId: string;
        folderId?: string;
    }) => {
        sharedTicketAutomationListsController.loadTicketAutomationLists(params);
    };

    get isListsLoading() {
        return sharedTicketAutomationListsController.isLoading;
    }

    get lists(): ListBoxItemData[] {
        return sharedTicketAutomationListsController.ticketAutomationListsList.map(
            (list) => {
                let listId = list.value;

                try {
                    const parsed = JSON.parse(list.value) as { listId: string };

                    if (parsed.listId) {
                        listId = parsed.listId;
                    }
                } catch {
                    // not JSON, keep as is
                }

                return {
                    id: listId,
                    label: list.label,
                    value: list.value,
                };
            },
        );
    }

    get selectedList(): ListBoxItemData | undefined {
        if (!this.formData.listId) {
            return undefined;
        }

        return this.lists.find((list) => list.id === this.formData.listId);
    }

    setList = (list: ListBoxItemData) => {
        this.formData.listId = list.id;
    };

    // Issue type field

    loadIssueTypes = (params: { connectionId: number; projectId: string }) => {
        sharedTicketAutomationIssueTypesController.loadTicketAutomationIssueTypes(
            params,
        );
    };

    get isIssueTypesLoading() {
        return sharedTicketAutomationIssueTypesController.isLoading;
    }

    get issueTypes(): ListBoxItemData[] {
        return sharedTicketAutomationIssueTypesController.ticketAutomationIssueTypeList.map(
            (type) => ({
                id: type.value,
                label: type.name,
                value: type.value,
            }),
        );
    }

    get selectedIssueType(): ListBoxItemData | undefined {
        if (!this.formData.issueTypeId) {
            return undefined;
        }

        return this.issueTypes.find(
            (type) => type.id === this.formData.issueTypeId,
        );
    }

    setIssueType = (issueType: ListBoxItemData) => {
        this.formData.issueTypeId = issueType.id;
    };

    // Ticket type field

    loadTicketTypes = (params: {
        connectionId?: number;
        projectId?: string;
        workspaceId?: string;
        teamId?: string;
    }) => {
        sharedTicketAutomationTicketTypesController.loadTicketAutomationTicketTypes(
            params,
        );
    };

    get isTicketTypesLoading() {
        return sharedTicketAutomationTicketTypesController.isLoading;
    }

    get ticketTypes(): ListBoxItemData[] {
        return sharedTicketAutomationTicketTypesController.ticketAutomationTicketTypesList.map(
            (type) => ({
                id: String(type.value),
                label: String(type.name),
                value: String(type.value),
            }),
        );
    }

    get selectedTicketType(): ListBoxItemData | undefined {
        if (!this.formData.ticketTypeId) {
            return undefined;
        }

        return this.ticketTypes.find(
            (type) => type.id === this.formData.ticketTypeId,
        );
    }

    setTicketType = (ticketType: ListBoxItemData) => {
        this.formData.ticketTypeId = ticketType.id;
    };

    loadStructureFields = (clientType: CreateTicketClientType) => {
        sharedTicketAutomationStructureController.loadTicketAutomationStructure(
            { clientType },
        );
    };

    get structure() {
        return sharedTicketAutomationStructureController.ticketAutomationStructure;
    }

    loadAdditionalFields = (
        typeId: string,
        query: ExternalTicketsControllerGetTicketStructureData['query'],
    ) => {
        sharedTicketAutomationAdditionalFieldsController.loadTicketAutomationAdditionalFields(
            typeId,
            query,
        );
    };

    loadAdditionalLegacyFields = (
        typeId: string,
        query: TicketsControllerGetTicketStructureData['query'],
    ) => {
        sharedTicketAutomationAdditionalFieldsController.loadTicketAutomationLegacyFields(
            typeId,
            query,
        );
    };

    get isAdditionalFieldsLoading() {
        return (
            sharedTicketAutomationAdditionalFieldsController.isLoading ||
            sharedTicketAutomationStructureController.isLoading
        );
    }

    get hasAdditionalFieldsError() {
        return sharedTicketAutomationAdditionalFieldsController.hasError;
    }

    get detailsFields() {
        const automationFields =
            sharedTicketAutomationAdditionalFieldsController.ticketAutomationFields;

        return omit(automationFields, 'project', 'issuetype');
    }

    get dynamicFields(): CreateTicketDynamicField[] {
        const dynamicFields = isEmpty(this.structure)
            ? this.detailsFields
            : this.structure;

        return Object.keys(dynamicFields).map((key) => {
            const field = dynamicFields[key] as CreateTicketDynamicField;

            const { dataEndpoint, dataEndpointParams } =
                this.parseEndpoint(field);

            return {
                ...field,
                dataEndpoint,
                dataEndpointParams,
                allowedValues: field.allowedValues ?? field.options,
                name: key,
            };
        });
    }

    parseEndpoint = (field: CreateTicketDynamicField) => {
        let { dataEndpoint, dataEndpointParams } = field;

        if (isObject(dataEndpoint)) {
            const { path, query } = dataEndpoint as {
                path: string;
                query: Record<string, unknown>;
            };

            dataEndpoint = path;
            dataEndpointParams = query;
        }

        return { dataEndpoint, dataEndpointParams };
    };

    get fields(): Record<string, CreateTicketFormField> {
        return this.formData.fields;
    }

    setField = (field: CreateTicketFormField): void => {
        this.formData.fields[field.name] = field;
    };

    removeField = (name: string) => {
        this.formData.fields = omit(this.formData.fields, name);
    };

    fetchAssignees = (
        dataEndpointParams: Record<string, unknown> = {},
    ): ((params: { search?: string; increasePage?: boolean }) => void) =>
        action((params: { search?: string; increasePage?: boolean }): void => {
            const connectionId = Number(this.connectionId);
            const projectId = Number(this.selectedProject?.id);

            if (isNaN(connectionId) || isNaN(projectId)) {
                return;
            }

            sharedTicketAutomationAssigneesController.onFetchTicketAutomationAssignees(
                Object.assign(dataEndpointParams, {
                    connectionId,
                    projectId,
                    search: params.search,
                    increasePage: params.increasePage,
                }),
            );
        });

    get isAssigneesLoading() {
        return sharedTicketAutomationAssigneesController.isLoading;
    }

    get assignees(): ListBoxItemData[] {
        return sharedTicketAutomationAssigneesController.ticketAutomationAssigneeList.map(
            (assignee) => ({
                id: assignee.value,
                label: assignee.label,
                value: assignee.value,
            }),
        );
    }

    get hasMoreAssignees() {
        return sharedTicketAutomationAssigneesController.hasNextPage;
    }

    fetchUsers = (
        dataEndpointParams: Record<string, unknown> = {},
    ): ((params: { search?: string; increasePage?: boolean }) => void) =>
        action((params: { search?: string; increasePage?: boolean }): void => {
            const connectionId = Number(this.connectionId);

            if (isNaN(connectionId)) {
                return;
            }

            sharedTicketAutomationUsersController.onFetchTicketAutomationUsers(
                Object.assign(dataEndpointParams, {
                    connectionId,
                    search: params.search,
                    increasePage: params.increasePage,
                }),
            );
        });

    get isUsersLoading() {
        return sharedTicketAutomationUsersController.isLoading;
    }

    get users(): ListBoxItemData[] {
        return sharedTicketAutomationUsersController.ticketAutomationUsersList.map(
            (user) => ({
                id: user.value,
                label: user.label,
                value: user.value,
            }),
        );
    }

    get hasMoreUsers() {
        return sharedTicketAutomationUsersController.hasNextPage;
    }

    fetchParents = (
        dataEndpointParams: Record<string, unknown> = {},
    ): ((params: { search?: string; increasePage?: boolean }) => void) =>
        action((params: { search?: string; increasePage?: boolean }): void => {
            const connectionId = Number(this.connectionId);

            if (isNaN(connectionId)) {
                return;
            }

            sharedTicketAutomationParentsController.onFetchTicketAutomationParents(
                Object.assign(dataEndpointParams, {
                    connectionId,
                    search: params.search,
                    increasePage: params.increasePage,
                }),
            );
        });

    get isParentsLoading() {
        return sharedTicketAutomationParentsController.isLoading;
    }

    get parents(): ListBoxItemData[] {
        return sharedTicketAutomationParentsController.ticketAutomationParentsList.map(
            (user) => ({
                id: user.value,
                label: user.label,
                value: user.value,
            }),
        );
    }

    get hasMoreParents() {
        return sharedTicketAutomationParentsController.hasNextPage;
    }

    fetchCycles = (
        dataEndpointParams: Record<string, unknown> = {},
    ): ((params: { search?: string; increasePage?: boolean }) => void) =>
        action((params: { search?: string; increasePage?: boolean }): void => {
            const connectionId = Number(this.connectionId);

            if (isNaN(connectionId)) {
                return;
            }

            sharedTicketAutomationCyclesController.onFetchTicketAutomationCycles(
                Object.assign(dataEndpointParams, {
                    connectionId,
                    search: params.search,
                    increasePage: params.increasePage,
                }),
            );
        });

    get isCyclesLoading() {
        return sharedTicketAutomationCyclesController.isLoading;
    }

    get cycles(): ListBoxItemData[] {
        return sharedTicketAutomationCyclesController.ticketAutomationCyclesList.map(
            (user) => ({
                id: user.value,
                label: user.label,
                value: user.value,
            }),
        );
    }

    get hasMoreCycles() {
        return sharedTicketAutomationCyclesController.hasNextPage;
    }

    fetchStates = (
        dataEndpointParams: Record<string, unknown> = {},
    ): ((params: { search?: string; increasePage?: boolean }) => void) =>
        action((params: { search?: string; increasePage?: boolean }): void => {
            const connectionId = Number(this.connectionId);

            if (isNaN(connectionId)) {
                return;
            }

            sharedTicketAutomationStatesController.onFetchTicketAutomationStates(
                Object.assign(dataEndpointParams, {
                    connectionId,
                    search: params.search,
                    increasePage: params.increasePage,
                }),
            );
        });

    get isStatesLoading() {
        return sharedTicketAutomationStatesController.isLoading;
    }

    get states(): ListBoxItemData[] {
        return sharedTicketAutomationStatesController.ticketAutomationStatesList.map(
            (user) => ({
                id: user.value,
                label: user.label,
                value: user.value,
            }),
        );
    }

    get hasMoreStates() {
        return sharedTicketAutomationStatesController.hasNextPage;
    }

    get isLabelsLoading() {
        return sharedTicketAutomationLabelsController.isLoading;
    }

    fetchLabels = (
        dataEndpointParams: Record<string, unknown> = {},
    ): ((params: { search?: string; increasePage?: boolean }) => void) =>
        action((params: { search?: string; increasePage?: boolean }): void => {
            if (this.connectionId) {
                sharedTicketAutomationLabelsController.onFetchTicketAutomationLabels(
                    Object.assign(dataEndpointParams, {
                        connectionId: this.connectionId,
                        search: params.search,
                        increasePage: params.increasePage,
                    }),
                );
            }
        });

    get labels(): ListBoxItemData[] {
        return sharedTicketAutomationLabelsController.ticketAutomationLabelList.map(
            (label) => ({
                id: label.value,
                label: label.label,
                value: label.value,
            }),
        );
    }

    get hasMoreLabels() {
        return sharedTicketAutomationLabelsController.hasNextPage;
    }

    setLabels = (field: CreateTicketFormField) => {
        this.setField(field);
    };

    getFieldValue = (name: string): string => {
        const field = this.fields[name];

        if (isEmpty(field)) {
            return '';
        }

        const { value, items, item, checked } = field;

        if (checked !== undefined) {
            return checked ? 'true' : 'false';
        }
        if (items !== undefined) {
            return items.map((i) => i.id).join(',');
        }
        if (item !== undefined) {
            return item.id;
        }

        return value ?? '';
    };

    get payload(): CreateTicketPayload {
        return {
            connectionId: this.connectionId,
            workspaceId: this.formData.workspaceId,
            projectId: this.formData.projectId,
            listId: this.formData.listId,
            teamId: this.formData.teamId,
            issueTypeId:
                this.formData.issueTypeId || this.formData.ticketTypeId,
            fields: this.fieldsPayload,
        };
    }

    get fieldsPayload(): CreateTicketPayloadField[] {
        return (
            this.dynamicFields
                .map((dynamicField) => {
                    return {
                        field: dynamicField.name,
                        type: dynamicField.type,
                        value: this.getFieldValue(dynamicField.name),
                    };
                })
                // Skip empty values
                .filter((field) => field.value !== '')
        );
    }

    invalidProps: (keyof CreateTicketPayload)[] = [];

    validateRequiredProps(): boolean {
        if (!this.providerId) {
            this.invalidProps = ['connectionId'];

            return false;
        }
        const requiredProps =
            CREATE_TICKET_REQUIRED_BY_PROVIDER[this.providerId];

        if (isNil(requiredProps) || isEmpty(requiredProps)) {
            return true;
        }

        this.invalidProps = requiredProps.filter((prop) =>
            isNil(this.payload[prop]),
        );

        return isEmpty(this.invalidProps);
    }

    invalidDynamicFields: string[] = [];

    validateDynamicFields(): boolean {
        this.invalidDynamicFields = this.dynamicFields
            .filter((field) => field.required)
            .map((field) => field.name)
            .filter((name) => this.getFieldValue(name) === '');

        return isEmpty(this.invalidDynamicFields);
    }

    validateForm(): boolean {
        const validProps = this.validateRequiredProps();
        const validDynamicFields = this.validateDynamicFields();

        return validProps && validDynamicFields;
    }

    createTicket = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (!this.onCreateTicket) {
            logger.warn({
                message:
                    'Failed to create ticket. Missing onCreateTicket callback',
            });

            return;
        }

        if (!this.validateForm()) {
            return;
        }

        this.isCreating = true;

        const payload = toJS(this.payload);

        return this.onCreateTicket(payload)
            .catch((error) => {
                logger.error({
                    message: 'Failed to create ticket',
                    additionalInfo: {
                        payload,
                    },
                    errorObject: {
                        message: isError(error)
                            ? error.message
                            : 'Unknown error',
                        statusCode: 'unknown',
                    },
                });
            })
            .then(() => {
                runInAction(() => {
                    this.resetForm();
                    this.closeModal();
                });
            })
            .finally(() => {
                this.isCreating = false;
            });
    };

    closeModal = () => {
        modalController.closeModal('create-ticket-modal');
    };
}

export const sharedCreateTicketController = new CreateTicketController();
