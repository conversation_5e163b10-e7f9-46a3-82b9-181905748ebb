export interface CreateTicketPayloadField {
    field: string;
    type: string;
    value: string;
}

export interface CreateTicketPayload {
    workspaceId?: string;
    connectionId?: number;
    projectId?: string;
    issueTypeId?: string;
    listId?: string;
    teamId?: string;
    fields: CreateTicketPayloadField[];
}

export type CreateTicketFn = (payload: CreateTicketPayload) => Promise<void>;
