import type { CreateTicketFormField } from './create-ticket-form-field.type';

export interface CreateTicketFormData {
    connectionId?: number;
    projectId?: string;
    workspaceId?: string;
    teamId?: string;
    issueTypeId?: string;
    ticketTypeId?: string;
    groupId?: string;
    folderId?: string;
    listId?: string;
    assigneeId?: string;
    summary?: string;
    description?: string;
    fields: Record<string, CreateTicketFormField>;
}
