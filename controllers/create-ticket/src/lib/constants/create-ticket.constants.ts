import type { Provider } from '@globals/providers';
import type { CreateTicketPayload } from '../types/create-ticket-payload.type';

export const PROVIDERS_WITH_WORKSPACE = ['ASANA', 'CLICKUP'];

export const CREATE_TICKET_REQUIRED_BY_PROVIDER: Partial<
    Record<Provider, (keyof CreateTicketPayload)[]>
> = {
    ASANA: ['workspaceId', 'projectId', 'issueTypeId'],
    AZURE: ['workspaceId', 'projectId', 'issueTypeId'],
    CLICKUP: ['workspaceId', 'projectId', 'listId', 'issueTypeId'],
    GITLAB: ['projectId', 'issueTypeId'],
    JIRA: ['projectId', 'issueTypeId'],
    MERGEDEV_JIRA_DATA_CENTER: ['projectId', 'issueTypeId'],
    LINEAR: ['teamId'],
    MERGEDEV_SERVICENOW: ['issueTypeId'],
};
