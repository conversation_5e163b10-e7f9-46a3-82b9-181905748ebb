import { ticketsControllerGetTicketLabelsInfiniteOptions } from '@globals/api-sdk/queries';
import type { TicketDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

class TicketAutomationLabelsController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationLabelsQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketLabelsInfiniteOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationLabelsQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationLabelsQuery.hasError;
    }

    get hasNextPage(): boolean {
        return this.ticketAutomationLabelsQuery.hasNextPage;
    }

    get ticketAutomationLabelList(): TicketDataResponseDto[] {
        return (
            this.ticketAutomationLabelsQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get isFetching(): boolean {
        return this.ticketAutomationLabelsQuery.isFetching;
    }

    loadNextPage = (): void => {
        this.ticketAutomationLabelsQuery.nextPage();
    };

    loadTicketAutomationLabels = ({
        connectionId,
        search,
    }: {
        connectionId: number;
        search?: string;
    }) => {
        this.ticketAutomationLabelsQuery.load({
            query: { connectionId, q: search?.trim() },
        });
    };

    onFetchTicketAutomationLabels = ({
        connectionId,
        search,
        increasePage,
    }: {
        connectionId: number;
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }
        this.loadTicketAutomationLabels({ connectionId, search });
    };
}

export const sharedTicketAutomationLabelsController =
    new TicketAutomationLabelsController();
