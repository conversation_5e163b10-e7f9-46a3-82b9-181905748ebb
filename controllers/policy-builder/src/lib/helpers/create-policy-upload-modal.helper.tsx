import { t } from '@globals/i18n/macro';
import { runInAction } from '@globals/mobx';
import type { FormValues, UseFormSubmitReturn } from '@ui/forms';
import { PolicyUploadContent } from '../components/policy-upload-content.component';
import type { PolicyBuilderModalConfig } from '../types/policy-builder-modal-config.type';

interface CreatePolicyUploadModalParams {
    formRef: UseFormSubmitReturn['formRef'];
    rawFormFile?: () => File[];
    triggerSubmit: UseFormSubmitReturn['triggerSubmit'];
    isLoading: () => boolean;
    onConfirm: (values: FormValues) => void;
    onCancel: () => void;
}

export const createPolicyUploadModal = ({
    formRef,
    rawFormFile,
    triggerSubmit,
    isLoading,
    onConfirm,
    onCancel,
}: CreatePolicyUploadModalParams): PolicyBuilderModalConfig => {
    const handleUpload = (): void => {
        triggerSubmit();
    };

    return {
        getTitle: () => t`Upload policy file`,
        getContent: () => {
            return (
                <PolicyUploadContent
                    data-id="policy-upload-content"
                    formRef={formRef}
                    isLoading={isLoading}
                    rawFormFile={rawFormFile}
                    onConfirm={onConfirm}
                />
            );
        },
        getActions: () => [
            {
                label: t`Cancel`,
                level: 'tertiary',
                onClick: onCancel,
                cosmosUseWithCaution_isDisabled: isLoading(),
            },
            {
                label: t`Upload policy`,
                level: 'primary',
                onClick: handleUpload,
                isLoading: isLoading(),
            },
        ],
        handleClose: () => {
            runInAction(() => {
                if (isLoading()) {
                    return;
                }

                onCancel();
            });
        },
    };
};
