import { isEmpty, noop } from 'lodash-es';
import { openPolicyExternalFileSelector } from '@components/policies';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policiesControllerSyncPolicyVersionFromExternalConnectionMutation,
    policyVersionControllerCreatePolicyVersionPdfFromHtmlMutation,
    policyVersionControllerPostPolicyVersionMutation,
    policyVersionControllerPutPolicyVersionExternalFileMutation,
    policyVersionControllerPutPolicyVersionFileMutation,
    policyVersionControllerUpdatePolicyVersionToAuthoredMutation,
} from '@globals/api-sdk/queries';
import type { PolicyVersionControllerPutPolicyVersionFileData } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { downloadBlob } from '@helpers/download-file';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import type { FormValues } from '@ui/forms';
import {
    closePolicyUploadModal,
    openPolicyUploadModal,
} from '../helpers/policy-upload-modal.helper';
import { sharedPolicyBuilderController } from '../policy-builder.controller';

// TODO: Separate out the mutations and their logic into their own files for external, uploaded, and authored policies.
export class PolicyHeaderSharedActions {
    onUploadCompleteCallback?: () => void;

    constructor() {
        makeAutoObservable(this);
    }

    private navigateToPolicyTab(): void {
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;
        const { policyId } = sharedPolicyBuilderModel;

        if (workspaceId && policyId) {
            sharedProgrammaticNavigationController.navigateTo(
                `${routeController.userPartOfUrl}/governance/policies/builder/${policyId}/policy`,
            );
        }
    }

    // TODO: Move these mutations and their logic to policy-header-published-actions.controller.ts
    // These mutations are related to creating drafts from published versions, which is
    /**
     * A published-specific action and should be organized with other published actions.
     */
    createDraftVersionForAuthorMutation = new ObservedMutation(
        policyVersionControllerPostPolicyVersionMutation,
        {
            onSuccess: () => {
                const { policyId } = sharedPolicyBuilderModel;

                this.authorPolicyMutation.mutate({
                    path: { id: policyId },
                });
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to create draft version for author policy',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        action: 'createDraftForAuthor',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'create-draft-for-author-error',
                    props: {
                        title: t`Failed to create draft version`,
                        description: t`We couldn't create a draft version for authoring. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    // TODO: Move this mutation and its logic to policy-header-published-actions.controller.ts
    // This mutation is related to creating drafts from published versions via file upload,
    /**
     * Which is a published-specific action and should be organized with other published actions.
     */
    createDraftVersionForUploadMutation = new ObservedMutation(
        policyVersionControllerPostPolicyVersionMutation,
        {
            onSuccess: (newVersion) => {
                // Always invalidate queries after upload to refresh file data
                if (newVersion.id) {
                    sharedPolicyBuilderController.switchToPolicyVersion(
                        newVersion.id,
                    );
                } else {
                    sharedPolicyBuilderController.invalidateUploadedPolicyQueries();
                }

                // Call the upload complete callback if it exists (for upload-then-save flow)
                if (this.onUploadCompleteCallback) {
                    this.onUploadCompleteCallback();
                    this.onUploadCompleteCallback = undefined;

                    return;
                }

                // Standard upload flow - close modal and show success message
                closePolicyUploadModal();

                snackbarController.addSnackbar({
                    id: 'upload-file-success',
                    props: {
                        title: t`File uploaded successfully`,
                        description: t`The policy file has been uploaded.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Navigate to policy tab if no new version was created
                if (!newVersion.id) {
                    this.navigateToPolicyTab();
                }
            },
            onError: (error) => {
                // Clear the callback on error
                this.onUploadCompleteCallback = undefined;

                logger.error({
                    message: 'Failed to create draft version for file upload',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        action: 'createDraftForUpload',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'create-draft-for-upload-error',
                    props: {
                        title: t`Failed to upload file`,
                        description: t`We couldn't create a new version for the file upload. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    authorPolicyMutation = new ObservedMutation(
        policyVersionControllerUpdatePolicyVersionToAuthoredMutation,
        {
            onSuccess: (authoredVersion) => {
                snackbarController.addSnackbar({
                    id: 'author-policy-success',
                    props: {
                        title: t`Policy converted to authored`,
                        description: t`The policy has been successfully converted to authored status.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                if (authoredVersion.id) {
                    sharedPolicyBuilderController.switchToPolicyVersion(
                        authoredVersion.id,
                    );
                } else {
                    sharedPolicyBuilderController.invalidateUploadedPolicyQueries();
                    this.navigateToPolicyTab();
                }
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to author policy',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        workspaceId:
                            sharedWorkspacesController.currentWorkspaceId,
                        action: 'authorPolicy',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'author-policy-error',
                    props: {
                        title: t`Failed to author policy`,
                        description: t`We couldn't convert your policy to authored status. Please try again in a few moments.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    pdfPreviewMutation = new ObservedMutation(
        policyVersionControllerCreatePolicyVersionPdfFromHtmlMutation,
        {
            onSuccess: (data) => {
                const blob = data as Blob;
                const fileName = `${sharedPolicyBuilderModel.policyName}.pdf`;

                downloadBlob(blob, fileName);
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to generate PDF preview',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        policyName: sharedPolicyBuilderModel.policyName,
                        action: 'pdfPreview',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'pdf-download-error',
                    props: {
                        title: t`Failed to download PDF`,
                        description: t`We couldn't generate the PDF right now. Please try again in a few moments.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    uploadFileMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionFileMutation,
        {
            onSuccess: () => {
                sharedPolicyBuilderController.invalidateUploadedPolicyQueries();

                // Call the upload complete callback if it exists (for upload-then-save flow)
                if (this.onUploadCompleteCallback) {
                    this.onUploadCompleteCallback();
                    this.onUploadCompleteCallback = undefined;

                    return;
                }

                // Standard upload flow - close modal and show success message
                closePolicyUploadModal();

                snackbarController.addSnackbar({
                    id: 'upload-file-success',
                    props: {
                        title: t`File uploaded successfully`,
                        description: t`The policy file has been uploaded.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                this.navigateToPolicyTab();
            },
            onError: (error) => {
                // Clear the callback on error
                this.onUploadCompleteCallback = undefined;

                logger.error({
                    message: 'Failed to upload policy file',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'uploadFile',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'upload-file-error',
                    props: {
                        title: t`Upload failed`,
                        description: t`We couldn't upload your file. Please check the file format and try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    importExternalFileMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionExternalFileMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'import-file-success',
                    props: {
                        title: t`External file imported successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                sharedPolicyBuilderController.invalidateUploadedPolicyQueries();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to import external file',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'importExternalFile',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'import-file-error',
                    props: {
                        title: t`Failed to import external file`,
                        description: t`We couldn't import the external file. Please ensure the file is accessible and try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    syncExternalFileMutation = new ObservedMutation(
        policiesControllerSyncPolicyVersionFromExternalConnectionMutation,
        {
            onSuccess: (data) => {
                if (data.upToDate) {
                    snackbarController.addSnackbar({
                        id: 'sync-file-no-changes',
                        props: {
                            title: t`No changes detected in the file`,
                            description: t`The file you're attempting to sync has no changes compared to the currently available version. No updates are needed at this time.`,
                            severity: 'primary',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    sharedPolicyBuilderController.invalidateUploadedPolicyQueries();

                    snackbarController.addSnackbar({
                        id: 'sync-file-success',
                        props: {
                            title: t`External file synced successfully`,
                            description: t`The policy has been updated with the latest version from the external source.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.navigateToPolicyTab();
                }
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to sync external file',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        action: 'syncExternalFile',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'sync-file-error',
                    props: {
                        title: t`Failed to sync external file`,
                        description: t`We couldn't sync the external file. Please ensure the file is accessible and try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isAuthoringPolicy(): boolean {
        return (
            this.authorPolicyMutation.isPending ||
            this.createDraftVersionForAuthorMutation.isPending
        );
    }

    get isGeneratingPdfDownload(): boolean {
        return this.pdfPreviewMutation.isPending;
    }

    get isUploadingFile(): boolean {
        return (
            this.uploadFileMutation.isPending ||
            this.createDraftVersionForUploadMutation.isPending
        );
    }

    get isImportingFile(): boolean {
        return this.importExternalFileMutation.isPending;
    }

    get isSyncingFile(): boolean {
        return this.syncExternalFileMutation.isPending;
    }

    get authorPolicyAction(): SchemaDropdownItemData {
        return {
            id: 'author-policy-action',
            type: 'item',
            label: t`Author policy`,
            value: 'author-policy',
            onSelect: this.handleAuthorPolicy,
            disabled: this.isAuthoringPolicy,
        };
    }

    get pdfPreviewAction(): SchemaDropdownItemData {
        return {
            id: 'pdf-preview-action',
            type: 'item',
            label: t`PDF preview`,
            value: 'pdf-preview',
            onSelect: this.handlePdfPreview,
            disabled: this.isGeneratingPdfDownload,
        };
    }

    get uploadFileAction(): SchemaDropdownItemData {
        return {
            id: 'upload-file-action',
            type: 'item',
            label: t`Upload file`,
            value: 'upload-file',
            onSelect: openPolicyUploadModal,
            disabled: this.isUploadingFile,
        };
    }

    get importFileAction(): SchemaDropdownItemData {
        return {
            id: 'import-file-action',
            type: 'item',
            label: t`Import file`,
            value: 'import-file',
            onSelect: this.handleImportFile,
            disabled: this.isImportingFile,
        };
    }

    get syncFileAction(): SchemaDropdownItemData {
        return {
            id: 'sync-file-action',
            type: 'item',
            label: t`Sync file`,
            value: 'sync-file',
            onSelect: this.handleSyncFile,
            disabled: this.isSyncingFile,
        };
    }

    handleAuthorPolicy = (): void => {
        const { policyId, isPublished } = sharedPolicyBuilderModel;

        if (isPublished) {
            this.createDraftVersionForAuthorMutation.mutate({
                path: { id: policyId },
                body: {
                    policyType: 'BUILDER',
                    policyVersionStatus: 'PUBLISHED',
                },
            });
        } else {
            this.authorPolicyMutation.mutate({
                path: { id: policyId },
            });
        }
    };

    handlePdfPreview = (): void => {
        const { policyId, htmlContent, hasHtmlContent } =
            sharedPolicyBuilderModel;

        if (!hasHtmlContent) {
            snackbarController.addSnackbar({
                id: 'pdf-download-empty-content',
                props: {
                    title: t`Empty content`,
                    description: t`Policy content is empty and cannot be downloaded as PDF.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.pdfPreviewMutation.mutate({
            path: { policyId },
            body: { html: htmlContent },
        });
    };

    handleFileUpload = (
        values: FormValues,
        onUploadComplete?: () => void,
    ): void => {
        const file = values.file as File[];
        const { policyId, currentVersionId, isPublished } =
            sharedPolicyBuilderModel;

        // Store the callback for use in mutation success handlers
        this.onUploadCompleteCallback = onUploadComplete;

        if (isPublished) {
            this.createDraftVersionForUploadMutation.mutate({
                path: { id: policyId },
                body: {
                    policyType: 'UPLOADED',
                    policyVersionStatus: 'PUBLISHED',
                    file,
                },
            });
        } else if (currentVersionId) {
            this.uploadFileMutation.mutate({
                path: {
                    policyId,
                    policyVersionId: currentVersionId,
                },
                body: {
                    file,
                } satisfies PolicyVersionControllerPutPolicyVersionFileData['body'],
            });
        }
    };

    handleImportFile = (): void => {
        openPolicyExternalFileSelector({
            config: {
                selectionMode: 'single',
                modal: {
                    id: 'policies-external-file-selector',
                    title: t`Import external file`,
                    size: 'lg',
                    confirmButtonLabel: t`Import`,
                    cancelButtonLabel: t`Cancel`,
                    showSelectedCount: false,
                },
                search: {
                    placeholder: t`Search by file name...`,
                    label: t`Search file`,
                    loaderLabel: t`Loading files...`,
                    emptyStateMessage: t`No files found matching your search criteria.`,
                    clearAllLabel: t`Clear all`,
                },
            },
            callbacks: {
                onSelected: (selectedFile) => {
                    // Handle both single item and array response
                    const externalFile = Array.isArray(selectedFile)
                        ? selectedFile[0]
                        : selectedFile;

                    const {
                        policyId,
                        currentVersionId,
                        externalPolicyProvider,
                    } = sharedPolicyBuilderModel;

                    if (
                        !policyId ||
                        !currentVersionId ||
                        !isEmpty(externalFile) ||
                        !externalPolicyProvider
                    ) {
                        logger.error({
                            message:
                                'Missing required parameters for external file import',
                            additionalInfo: {
                                policyId,
                                currentVersionId,
                                externalFile,
                                externalPolicyProvider,
                            },
                        });

                        return;
                    }

                    this.importExternalFileMutation.mutate({
                        path: {
                            policyId,
                            policyVersionId: currentVersionId,
                        },
                        body: {
                            externalFileId: String(externalFile.id),
                            needsPersonnelAcknowledgement: false,
                            clientType: externalPolicyProvider,
                        },
                    });
                },
                onCancel: noop,
            },
        });
    };

    handleSyncFile = (): void => {
        this.syncExternalFileMutation.mutate({
            path: { id: sharedPolicyBuilderModel.policyId },
        });
    };

    get shouldShowAuthoredPolicyActions(): boolean {
        return (
            sharedPolicyBuilderModel.isAuthoredPolicy &&
            sharedFeatureAccessModel.isDownloadControlEnabled
        );
    }

    get shouldShowUploadedPolicyActions(): boolean {
        return sharedPolicyBuilderModel.isUploadedPolicy;
    }

    get shouldShowExternalPolicyActions(): boolean {
        return sharedPolicyBuilderModel.isExternalPolicy;
    }
}

export const sharedPolicyHeaderSharedActionsController =
    new PolicyHeaderSharedActions();
