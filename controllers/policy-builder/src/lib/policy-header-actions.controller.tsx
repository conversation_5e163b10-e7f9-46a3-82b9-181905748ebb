import { isEmpty, isNil } from 'lodash-es';
import type React from 'react';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { makeAutoObservable } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { PolicyHeaderApprovedActions } from './controllers/policy-header-approved-actions.controller';
import { sharedPolicyHeaderBambooActions } from './controllers/policy-header-bamboo-actions.controller';
import { PolicyHeaderDraftActions } from './controllers/policy-header-draft-actions.controller';
import { PolicyHeaderNeedsApprovalActions } from './controllers/policy-header-needs-approval-actions.controller';
import { PolicyHeaderPublishedActions } from './controllers/policy-header-published-actions.controller';

class PolicyHeaderActionsController {
    _draftActions: PolicyHeaderDraftActions | null = null;
    _needsApprovalActions: PolicyHeaderNeedsApprovalActions | null = null;
    _approvedActions: PolicyHeaderApprovedActions | null = null;
    _publishedActions: PolicyHeaderPublishedActions | null = null;

    constructor() {
        makeAutoObservable(
            this,
            {
                _draftActions: false,
                _needsApprovalActions: false,
                _approvedActions: false,
                _publishedActions: false,
                draftActions: false,
                needsApprovalActions: false,
                approvedActions: false,
                publishedActions: false,
            },
            { autoBind: true },
        );
    }

    get actionStack(): React.JSX.Element {
        const actions = this.getActionsForCurrentStatus();

        if (isEmpty(actions)) {
            return <div data-testid="no-actions-available" />;
        }

        return (
            <ActionStack
                data-id="policy-header-action-stack"
                gap="2x"
                data-testid="PolicyHeaderActionStack"
                actions={actions}
            />
        );
    }

    private getActionsForCurrentStatus(): Action[] {
        if (!sharedPolicyBuilderModel.currentStatus) {
            return [];
        }

        const bambooActions = this.getBambooHRActions();

        if (!isNil(bambooActions)) {
            return bambooActions;
        }

        return this.getStandardStatusActions();
    }

    private getBambooHRActions(): Action[] | null {
        switch (true) {
            case sharedPolicyBuilderModel.isBambooHRProvider &&
                !sharedPolicyBuilderModel.hasBambooHRConnection: {
                return [];
            }

            case sharedPolicyBuilderModel.isBambooHRProvider &&
                sharedPolicyBuilderModel.hasBambooHRConnection: {
                return sharedPolicyHeaderBambooActions.getActions();
            }

            case !sharedPolicyBuilderModel.isBambooHRProvider &&
                sharedPolicyBuilderModel.hasBambooHRConnection: {
                return [];
            }

            default: {
                return null;
            }
        }
    }

    private getStandardStatusActions(): Action[] {
        switch (sharedPolicyBuilderModel.currentStatus) {
            case 'DRAFT': {
                return this.draftActions.getActions();
            }
            case 'NEEDS_APPROVAL': {
                return this.needsApprovalActions.getActions();
            }
            case 'APPROVED': {
                return this.approvedActions.getActions();
            }
            case 'PUBLISHED': {
                return this.publishedActions.getActions();
            }
            default: {
                return [];
            }
        }
    }

    get draftActions(): PolicyHeaderDraftActions {
        this._draftActions ??= new PolicyHeaderDraftActions();

        return this._draftActions;
    }

    get needsApprovalActions(): PolicyHeaderNeedsApprovalActions {
        this._needsApprovalActions ??= new PolicyHeaderNeedsApprovalActions();

        return this._needsApprovalActions;
    }

    get approvedActions(): PolicyHeaderApprovedActions {
        this._approvedActions ??= new PolicyHeaderApprovedActions();

        return this._approvedActions;
    }

    get publishedActions(): PolicyHeaderPublishedActions {
        this._publishedActions ??= new PolicyHeaderPublishedActions();

        return this._publishedActions;
    }

    clearActionsCache(): void {
        if (this._draftActions) {
            this._draftActions = null;
        }

        if (this._needsApprovalActions) {
            this._needsApprovalActions = null;
        }

        if (this._approvedActions) {
            this._approvedActions = null;
        }

        if (this._publishedActions) {
            this._publishedActions = null;
        }
    }
}

export const sharedPolicyHeaderActionsController =
    new PolicyHeaderActionsController();
