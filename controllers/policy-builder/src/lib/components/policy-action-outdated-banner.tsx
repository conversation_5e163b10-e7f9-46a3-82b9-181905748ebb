import { Button } from '@cosmos/components/button';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPolicyHeaderSharedActionsController } from '../controllers/policy-header-shared-actions.controller';

export const PolicyActionOutdatedBanner = observer((): React.JSX.Element => {
    const { handleSyncFile } = sharedPolicyHeaderSharedActionsController;

    return (
        <Button
            label={t`Sync to the newest version`}
            level="secondary"
            colorScheme="neutral"
            size="sm"
            hasPadding={false}
            data-id="Wy9leOfk"
            onClick={handleSyncFile}
        />
    );
});
