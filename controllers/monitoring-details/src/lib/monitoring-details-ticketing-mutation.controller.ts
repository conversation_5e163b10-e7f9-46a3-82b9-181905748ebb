import type { CreateTicketPayload } from '@controllers/create-ticket';
import { snackbarController } from '@controllers/snackbar';
import {
    monitorsV2ControllerCreateTicketMutation,
    monitorsV2ControllerDeleteTicketMutation,
} from '@globals/api-sdk/queries';
import type { TicketsCreateRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedMonitoringDetailsTicketsController } from './monitoring-details-ticketing.controller';

export class MonitoringDetailsTicketsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    createTicketMutation = new ObservedMutation(
        monitorsV2ControllerCreateTicketMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-ticket-create-success',
                    props: {
                        title: t`Ticket created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                sharedMonitoringDetailsTicketsController.invalidateTickets();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-ticket-create-error',
                    props: {
                        title: t`Unable to create ticket`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    deleteTicketMutation = new ObservedMutation(
        monitorsV2ControllerDeleteTicketMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-ticket-delete-success',
                    props: {
                        title: t`Ticket deleted`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                sharedMonitoringDetailsTicketsController.invalidateTickets();
                closeConfirmationModal();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-ticket-delete-error',
                    props: {
                        title: t`Unable to delete ticket`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isCreating(): boolean {
        return this.createTicketMutation.isPending;
    }

    get isDeleting(): boolean {
        return this.deleteTicketMutation.isPending;
    }

    get isUnlinkingTicket(): boolean {
        return this.deleteTicketMutation.isPending;
    }

    createTicket = async (
        payload: CreateTicketPayload,
        testId: number,
    ): Promise<void> => {
        const currentWorkspaceId =
            sharedWorkspacesController.currentWorkspace?.id;

        if (!currentWorkspaceId) {
            throw new Error('Workspace ID is required to create tickets');
        }

        const requestData: TicketsCreateRequestDto = {
            ...payload,
            relatedEntityIds: [testId],
            relatedEntityEnum: 'MONITOR',
            workspaceId: currentWorkspaceId,
        };

        return this.createTicketMutation.mutateAsync({
            path: {
                testId,
                workspaceId: currentWorkspaceId,
            } as { testId: number; workspaceId: number },
            body: requestData,
        });
    };

    unlinkTicket = (ticketId: number, testId: number): void => {
        openConfirmationModal({
            title: t`Delete ticket?`,
            body: t`Ticket will be permanently deleted.`,
            confirmText: t`Delete ticket`,
            cancelText: t`Cancel`,
            type: 'danger',
            isLoading: () => this.isDeleting,
            onConfirm: action(() => {
                const currentWorkspaceId =
                    sharedWorkspacesController.currentWorkspace?.id;

                if (!currentWorkspaceId) {
                    throw new Error(
                        'Workspace ID is required to delete tickets',
                    );
                }

                this.deleteTicketMutation.mutate({
                    path: {
                        ticketId,
                        testId,
                        workspaceId: currentWorkspaceId,
                    } as {
                        ticketId: number;
                        testId: number;
                        workspaceId: number;
                    },
                });
            }),
            onCancel: closeConfirmationModal,
        });
    };
}

export const sharedMonitoringDetailsTicketsMutationController =
    new MonitoringDetailsTicketsMutationController();
