import {
    monitorsV2ControllerGetTicketsInfiniteOptions,
    monitorsV2ControllerGetTicketsOptions,
} from '@globals/api-sdk/queries';
import type { TicketResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringDetailsTicketsMutationController } from './monitoring-details-ticketing-mutation.controller';

export class MonitoringDetailsTicketsController {
    ticketsInProgressQuery = new ObservedInfiniteQuery(
        monitorsV2ControllerGetTicketsInfiniteOptions,
    );
    ticketsCompletedQuery = new ObservedQuery(
        monitorsV2ControllerGetTicketsOptions,
    );
    ticketsCompletedInfiniteQuery = new ObservedInfiniteQuery(
        monitorsV2ControllerGetTicketsInfiniteOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get objectName(): string {
        return t`monitor`;
    }

    get ticketsInProgress(): TicketResponseDto[] {
        return (
            this.ticketsInProgressQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasNextPage(): boolean {
        return this.ticketsInProgressQuery.hasNextPage;
    }

    loadNextPage = (): void => {
        this.ticketsInProgressQuery.nextPage();
    };

    get isLoadingTicketsInProgress(): boolean {
        return this.ticketsInProgressQuery.isLoading;
    }

    get totalTicketsInProgress(): number {
        return this.ticketsInProgressQuery.data?.pages[0]?.total ?? 0;
    }

    get ticketsCompleted(): TicketResponseDto[] {
        return this.ticketsCompletedQuery.data?.data ?? [];
    }

    get isLoadingTicketsCompleted(): boolean {
        return this.ticketsCompletedQuery.isLoading;
    }

    get totalTicketsCompleted(): number {
        return this.ticketsCompletedQuery.data?.total ?? 0;
    }

    get ticketsCompletedInfinite(): TicketResponseDto[] {
        return (
            this.ticketsCompletedInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get isLoadingTicketsCompletedInfinite(): boolean {
        return this.ticketsCompletedInfiniteQuery.isLoading;
    }

    get totalTicketsCompletedInfinite(): number {
        return this.ticketsCompletedInfiniteQuery.data?.pages[0]?.total ?? 0;
    }

    get hasNextPageCompleted(): boolean {
        return this.ticketsCompletedInfiniteQuery.hasNextPage;
    }

    loadNextPageCompleted = (): void => {
        this.ticketsCompletedInfiniteQuery.nextPage();
    };

    get userHasPermissionToCreateTicket(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'CreateTicket',
            'MANAGE',
        );
    }

    get isUnlinkingTicket(): boolean {
        return sharedMonitoringDetailsTicketsMutationController.isUnlinkingTicket;
    }

    unlinkTicket = (ticketId: number, objectId: number): void => {
        sharedMonitoringDetailsTicketsMutationController.unlinkTicket(
            ticketId,
            objectId,
        );
    };

    get hasError(): boolean {
        return (
            this.ticketsInProgressQuery.hasError ||
            this.ticketsCompletedQuery.hasError
        );
    }

    get error(): Error | null {
        return (
            this.ticketsInProgressQuery.error ??
            this.ticketsCompletedQuery.error
        );
    }

    loadTickets = (testId: number): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    logger.error({
                        message: 'Failed to load tickets. Workspace not found',
                    });

                    return;
                }

                this.ticketsInProgressQuery.load({
                    path: { testId, workspaceId: currentWorkspace.id },
                    query: { isCompleted: false, limit: 5 },
                });
                this.ticketsCompletedQuery.load({
                    path: { testId, workspaceId: currentWorkspace.id },
                    query: { isCompleted: true },
                });
            },
        );
    };

    loadClosedTicketsInfinite = (testId: number): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    logger.error({
                        message:
                            'Failed to load closed tickets. Workspace not found',
                    });

                    return;
                }

                this.ticketsCompletedInfiniteQuery.load({
                    path: { testId, workspaceId: currentWorkspace.id },
                    query: { isCompleted: true, limit: 5 },
                });
            },
        );
    };

    invalidateTickets = (): void => {
        this.ticketsInProgressQuery.invalidate();
        this.ticketsCompletedQuery.invalidate();
        this.ticketsCompletedInfiniteQuery.invalidate();
    };
}

export const sharedMonitoringDetailsTicketsController =
    new MonitoringDetailsTicketsController();
