import type {
    Attachment<PERSON>onfig,
    INotesController,
    NoteCreateInput,
    NotesLabels,
    NoteUpdateInput,
    StandardNote,
} from '@components/utilities';
import { monitorsControllerGetNotesInfiniteOptions } from '@globals/api-sdk/queries';
import type { NoteResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedInfiniteQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringDetailsNotesMutationController } from './monitoring-details-notes-mutation.controller';

export class MonitoringDetailsNotesController implements INotesController {
    constructor() {
        makeAutoObservable(this);
    }

    notesInfiniteQuery = new ObservedInfiniteQuery(
        monitorsControllerGetNotesInfiniteOptions,
    );

    /**
     * Raw API list retained for legacy wrappers.
     */
    get list(): NoteResponseDto[] {
        return (
            this.notesInfiniteQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    /**
     * INotesController.
     */
    get notes(): StandardNote[] {
        const raw: NoteResponseDto[] = this.list;

        return raw.map((note) => ({
            id: String(note.id),
            comment: note.comment,
            createdAt: new Date(note.createdAt).toISOString(),
            updatedAt: new Date(note.updatedAt).toISOString(),
            owner: {
                id: String(note.owner.id),
                firstName: note.owner.firstName,
                lastName: note.owner.lastName,
                avatarUrl: note.owner.avatarUrl ?? undefined,
            },
            files: note.noteFiles,
        }));
    }

    get isLoading(): boolean {
        return this.notesInfiniteQuery.isLoading;
    }

    /**
     * UI configuration.
     */
    get labels(): NotesLabels {
        return {
            title: t`Internal notes`,
            subtitle: t`Add any feedback or questions you want to track for this monitoring test. These messages are not shared with auditors.`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details for this test.`,
        };
    }

    get attachments(): AttachmentConfig {
        return {};
    }

    get maxNoteCharacters(): number {
        // TODO: Check if this is correct with Agustin
        return 768;
    }

    /**
     * Optional flags.
     */
    get isReadOnly(): boolean {
        return !sharedCurrentUserController.hasUserPermission(
            'Monitors',
            'MANAGE',
        );
    }

    get isUploading(): boolean {
        return false;
    }

    get 'data-id'(): string {
        return 'monitoring-notes';
    }

    get hasSourceInput(): boolean {
        return false;
    }

    get showUnreadIndicators(): boolean {
        return false;
    }

    get enableReadStatusTracking(): boolean {
        return false;
    }

    get enableAuditorOnlyEditing(): boolean {
        return false;
    }

    /**
     * Infinite scrolling properties.
     */
    get hasMore(): boolean {
        return this.notesInfiniteQuery.hasNextPage;
    }

    get isLoadingMore(): boolean {
        return this.notesInfiniteQuery.isFetching && !this.isLoading;
    }

    loadMore = (): void => {
        if (this.hasMore && !this.isLoadingMore) {
            this.notesInfiniteQuery.nextPage();
        }
    };

    /**
     * CRUD delegations.
     */
    createNote(values: NoteCreateInput, onSuccess: () => void): void {
        sharedMonitoringDetailsNotesMutationController.createNote({
            comment: values.comment,
        });
        when(
            () => !sharedMonitoringDetailsNotesMutationController.isCreating,
            () => {
                onSuccess();
            },
        );
    }

    updateNote(
        noteId: string,
        values: NoteUpdateInput,
        onSuccess: () => void,
    ): void {
        sharedMonitoringDetailsNotesMutationController.updateNote(noteId, {
            comment: values.comment ?? '',
        });
        when(
            () => !sharedMonitoringDetailsNotesMutationController.isUpdating,
            () => {
                onSuccess();
            },
        );
    }

    deleteNote(noteId: string): void {
        sharedMonitoringDetailsNotesMutationController.deleteNote(noteId);
    }

    /**
     * Existing load utilities.
     */
    loadNotes = (testId: number): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const { currentWorkspaceId } = sharedWorkspacesController;

                if (!currentWorkspaceId) {
                    logger.error({
                        message: 'Failed to load notes. Workspace not found',
                    });

                    return;
                }

                this.notesInfiniteQuery.load({
                    path: { xProductId: currentWorkspaceId, testId },
                    query: {
                        page: 1,
                        limit: 10,
                    },
                });
            },
        );
    };

    invalidateNotes = (): void => {
        this.notesInfiniteQuery.invalidate();
    };
}

export const sharedMonitoringDetailsNotesController =
    new MonitoringDetailsNotesController();
