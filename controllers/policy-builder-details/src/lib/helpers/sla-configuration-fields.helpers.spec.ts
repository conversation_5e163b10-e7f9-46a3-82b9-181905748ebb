import { describe, expect, test } from 'vitest';
import {
    createGracePeriodSLAField,
    createGracePeriodSLAOptions,
    createP3MatrixSLAField,
    createSLAConfigurationField,
    createWeekTimeFrameSLAField,
    createWeekTimeFrameSLAOptions,
} from './sla-configuration-fields.helpers';

describe('sLA Configuration Field Helpers', () => {
    describe('createWeekTimeFrameSLAOptions', () => {
        test('should create options with metadata for week time frame SLAs', () => {
            const weekTimeFrameSLAs = [
                {
                    policyWeekTimeFrameSLAId: 1,
                    timeFrame: 'ONE_DAY',
                    label: 'SLA 1',
                },
                {
                    policyWeekTimeFrameSLAId: 2,
                    timeFrame: 'TWO_DAYS',
                    label: 'SLA 2',
                },
            ];

            const options = createWeekTimeFrameSLAOptions(weekTimeFrameSLAs);

            expect(options).toHaveLength(6); // Based on weekTimeFrameSLAOptions
            expect(options[0]).toHaveProperty('meta');
            expect(options[0].meta).toStrictEqual({
                labels: ['SLA 1', 'SLA 2'],
                policyWeekTimeFrameSLAIds: [1, 2],
            });
        });
    });

    describe('createWeekTimeFrameSLAField', () => {
        test('should create a week time frame SLA field configuration', () => {
            const weekTimeFrameSLAs = [
                {
                    policyWeekTimeFrameSLAId: 1,
                    timeFrame: 'ONE_DAY',
                    label: 'SLA 1',
                },
            ];

            const field = createWeekTimeFrameSLAField(weekTimeFrameSLAs) as {
                weekTimeFrameSLAs: {
                    type: 'custom';
                    customType: 'arrayOfObjects';
                    initialValue: {
                        timeFrame: {
                            value: string;
                            label: string;
                            id: string;
                            meta: {
                                labels: string[];
                                policyWeekTimeFrameSLAIds: number[];
                            };
                        };
                    }[];
                    render: () => React.JSX.Element;
                    label: string;
                    fields: {
                        timeFrame: {
                            type: 'select';
                            label: string;
                            options: {
                                value: string;
                                label: string;
                                id: string;
                                meta: {
                                    labels: string[];
                                    policyWeekTimeFrameSLAIds: number[];
                                };
                            }[];
                        };
                    };
                };
            };

            expect(field).toHaveProperty('weekTimeFrameSLAs');
            expect(field.weekTimeFrameSLAs).toHaveProperty('type', 'custom');
            expect(field.weekTimeFrameSLAs).toHaveProperty(
                'customType',
                'arrayOfObjects',
            );
            expect(field.weekTimeFrameSLAs).toHaveProperty('initialValue');
            expect(field.weekTimeFrameSLAs).toHaveProperty('render');
            expect(field.weekTimeFrameSLAs).toHaveProperty('label');
            expect(field.weekTimeFrameSLAs).toHaveProperty('fields');
            expect(field.weekTimeFrameSLAs.fields).toHaveProperty('timeFrame');
        });

        test('should map initial values correctly', () => {
            const weekTimeFrameSLAs = [
                {
                    policyWeekTimeFrameSLAId: 1,
                    timeFrame: 'ONE_DAY',
                    label: 'SLA 1',
                },
                {
                    policyWeekTimeFrameSLAId: 2,
                    timeFrame: 'SEVEN_DAYS',
                    label: 'SLA 2',
                },
            ];

            const field = createWeekTimeFrameSLAField(weekTimeFrameSLAs);
            const { initialValue } = field.weekTimeFrameSLAs;

            expect(initialValue).toHaveLength(2);
        });
    });

    describe('createGracePeriodSLAOptions', () => {
        test('should create options with metadata for grace period SLAs', () => {
            const gracePeriodSLAs = [
                {
                    policyGracePeriodSLAId: 1,
                    gracePeriod: 'ONE_DAY',
                    label: 'Grace Period 1',
                },
                {
                    policyGracePeriodSLAId: 2,
                    gracePeriod: 'SEVEN_DAYS',
                    label: 'Grace Period 2',
                },
            ];

            const options = createGracePeriodSLAOptions(gracePeriodSLAs);

            expect(options).toHaveLength(5); // Based on gracePeriodSLAOptions
            expect(options[0]).toHaveProperty('meta');
            expect(options[0].meta).toStrictEqual({
                policyGracePeriodSLAIds: [1, 2],
            });
        });
    });

    describe('createGracePeriodSLAField', () => {
        test('should create a grace period SLA field configuration', () => {
            const gracePeriodSLAs = [
                {
                    policyGracePeriodSLAId: 1,
                    gracePeriod: 'ONE_DAY',
                    label: 'Grace Period 1',
                },
            ];

            const field = createGracePeriodSLAField(gracePeriodSLAs) as {
                gracePeriodSLAs: {
                    type: 'custom';
                    customType: 'arrayOfObjects';
                    render: () => React.JSX.Element;
                    initialValue: {
                        period: {
                            value: string;
                            label: string;
                            id: string;
                            meta: {
                                label: string;
                                policyGracePeriodSLAIds: number[];
                            };
                        };
                    }[];
                    label: string;
                    fields: {
                        period: {
                            type: 'select';
                            label: string;
                            options: {
                                value: string;
                                label: string;
                                id: string;
                                meta: {
                                    labels: string[];
                                    policyGracePeriodSLAIds: number[];
                                };
                            }[];
                        };
                    };
                };
            };

            expect(field).toHaveProperty('gracePeriodSLAs');
            expect(field.gracePeriodSLAs).toHaveProperty('type', 'custom');
            expect(field.gracePeriodSLAs).toHaveProperty(
                'customType',
                'arrayOfObjects',
            );
            expect(field.gracePeriodSLAs).toHaveProperty('initialValue');
            expect(field.gracePeriodSLAs).toHaveProperty('render');
            expect(field.gracePeriodSLAs).toHaveProperty('label');
            expect(field.gracePeriodSLAs).toHaveProperty('fields');
            expect(field.gracePeriodSLAs.fields).toHaveProperty('period');
        });

        test('should map initial values with period metadata', () => {
            const gracePeriodSLAs = [
                {
                    policyGracePeriodSLAId: 1,
                    gracePeriod: 'ONE_DAY',
                    label: 'Grace Period 1',
                },
            ];

            const field = createGracePeriodSLAField(gracePeriodSLAs);
            const { initialValue } = field.gracePeriodSLAs;

            const initialValueArray = initialValue as {
                period: {
                    value: string;
                    label: string;
                    id: string;
                    meta: {
                        label: string;
                        policyGracePeriodSLAIds: number[];
                    };
                };
            }[];

            expect(initialValueArray).toHaveLength(1);
            expect(initialValueArray[0]).toHaveProperty('period');
            expect(initialValueArray[0].period).toHaveProperty(
                'value',
                'ONE_DAY',
            );
            expect(initialValueArray[0].period).toHaveProperty('meta');
            expect(initialValueArray[0].period.meta).toHaveProperty(
                'label',
                'Grace Period 1',
            );
        });
    });

    describe('createP3MatrixSLAField', () => {
        test('should create a P3 matrix SLA field configuration', () => {
            const p3MatrixSLAs = [
                {
                    id: 1,
                    policyP3MatrixSLAId: 1,
                    timeFrame: 'ONE_DAY',
                    definition: 'Critical',
                    severity: 'HIGH',
                    examples: 'Example 1',
                },
            ];
            const label = 'P3 matrix label';

            const field = createP3MatrixSLAField(p3MatrixSLAs, label) as {
                p3MatrixSLAs: {
                    type: 'custom';
                    customType: 'arrayOfObjects';
                    label: string;
                    fields: {
                        timeFrame: {
                            type: 'select';
                            label: string;
                        };
                        definition: {
                            type: 'textarea';
                            label: string;
                        };
                        examples: {
                            type: 'textarea';
                            label: string;
                        };
                    };
                };
            };

            expect(field).toHaveProperty('p3MatrixSLAs');
            expect(field.p3MatrixSLAs).toHaveProperty('type', 'custom');
            expect(field.p3MatrixSLAs).toHaveProperty(
                'customType',
                'arrayOfObjects',
            );
            expect(field.p3MatrixSLAs).toHaveProperty('initialValue');
            expect(field.p3MatrixSLAs).toHaveProperty('render');
            expect(field.p3MatrixSLAs).toHaveProperty('label', label);
            expect(field.p3MatrixSLAs).toHaveProperty('fields');
            expect(field.p3MatrixSLAs.fields).toHaveProperty('timeFrame');
            expect(field.p3MatrixSLAs.fields).toHaveProperty('definition');
            expect(field.p3MatrixSLAs.fields).toHaveProperty('examples');
        });

        test('should map initial values with timeFrame options', () => {
            const p3MatrixSLAs = [
                {
                    id: 1,
                    policyP3MatrixSLAId: 1,
                    timeFrame: 'THREE_DAYS',
                    definition: 'Critical',
                    severity: 'HIGH',
                    examples: 'Example 1',
                },
            ];
            const label = 'P3 matrix label';

            const field = createP3MatrixSLAField(p3MatrixSLAs, label) as {
                p3MatrixSLAs: {
                    initialValue: {
                        timeFrame: {
                            value: string;
                            label: string;
                            id: string;
                        };
                        definition: string;
                        severity: string;
                        examples: string;
                    }[];
                };
            };
            const { initialValue } = field.p3MatrixSLAs;

            expect(initialValue).toHaveLength(1);
            expect(initialValue[0]).toHaveProperty('timeFrame');
            expect(initialValue[0].timeFrame).toHaveProperty(
                'value',
                'THREE_DAYS',
            );
            expect(initialValue[0]).toHaveProperty('definition', 'Critical');
            expect(initialValue[0]).toHaveProperty('severity', 'HIGH');
            expect(initialValue[0]).toHaveProperty('examples', 'Example 1');
        });
    });

    describe('createSLAConfigurationField', () => {
        test('should create an SLA configuration field group', () => {
            const fields = {
                weekTimeFrameSLAs: {
                    type: 'custom' as const,
                    customType: 'arrayOfObjects' as const,
                    initialValue: [],
                    render: () => null,
                    label: 'Test',
                    fields: {},
                },
            };

            const result = createSLAConfigurationField(fields) as {
                slaConfiguration: {
                    type: 'group';
                    header: string;
                    fields: typeof fields;
                };
            };

            expect(result).toHaveProperty('slaConfiguration');
            expect(result.slaConfiguration).toHaveProperty('type', 'group');
            expect(result.slaConfiguration).toHaveProperty('header');
            expect(result.slaConfiguration).toHaveProperty('fields', fields);
        });

        test('should accept multiple field types', () => {
            const fields = {
                weekTimeFrameSLAs: {
                    type: 'custom' as const,
                    customType: 'arrayOfObjects' as const,
                    initialValue: [],
                    render: () => null,
                    label: 'Week',
                    fields: {},
                },
                gracePeriodSLAs: {
                    type: 'custom' as const,
                    customType: 'arrayOfObjects' as const,
                    initialValue: [],
                    render: () => null,
                    label: 'Grace',
                    fields: {},
                },
                p3MatrixSLAs: {
                    type: 'custom' as const,
                    customType: 'arrayOfObjects' as const,
                    initialValue: [],
                    render: () => null,
                    label: 'P3',
                    fields: {},
                },
            };

            const result = createSLAConfigurationField(fields) as {
                slaConfiguration: {
                    type: 'group';
                    header: string;
                    fields: typeof fields;
                };
            };

            expect(result.slaConfiguration.fields).toHaveProperty(
                'weekTimeFrameSLAs',
            );
            expect(result.slaConfiguration.fields).toHaveProperty(
                'gracePeriodSLAs',
            );
            expect(result.slaConfiguration.fields).toHaveProperty(
                'p3MatrixSLAs',
            );
        });
    });
});
