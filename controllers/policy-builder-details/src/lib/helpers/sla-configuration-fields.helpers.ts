import {
    Grace<PERSON><PERSON>odSL<PERSON>ield,
    P3MatrixSLAFields,
    WeekTimeFrameSLAField,
} from '@components/policies';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import {
    gracePeriodSLAOptions,
    p3MatrixSLAOptions,
    weekTimeFrameSLAOptions,
} from '@models/policies';
import type { FormSchema } from '@ui/forms';

interface WeekTimeFrameSLA {
    policyWeekTimeFrameSLAId: number;
    timeFrame: string;
    label: string;
}

interface GracePeriodSLA {
    policyGracePeriodSLAId: number;
    gracePeriod: string;
    label: string;
}

interface P3MatrixSLA {
    id: number;
    policyP3MatrixSLAId: number;
    timeFrame: string;
    definition: string;
    severity: string;
    examples?: string;
}

/**
 * Creates options for Week Time Frame SLA with metadata.
 */
export function createWeekTimeFrameSLAOptions(
    weekTimeFrameSLAs: WeekTimeFrameSLA[],
): ListBoxItemData[] {
    const metaLabels = weekTimeFrameSLAs.map((sla) => sla.label);
    const metaIds = weekTimeFrameSLAs.map(
        (sla) => sla.policyWeekTimeFrameSLAId,
    );

    return weekTimeFrameSLAOptions().map((option) => ({
        ...option,
        meta: {
            labels: metaLabels,
            policyWeekTimeFrameSLAIds: metaIds,
        },
    }));
}

/**
 * Creates the Week Time Frame SLA field configuration.
 */
export function createWeekTimeFrameSLAField(
    weekTimeFrameSLAs: WeekTimeFrameSLA[],
): Pick<FormSchema, 'weekTimeFrameSLAs'> {
    const optionsWithMeta = createWeekTimeFrameSLAOptions(weekTimeFrameSLAs);

    return {
        weekTimeFrameSLAs: {
            type: 'custom',
            customType: 'arrayOfObjects',
            showDivider: false,
            initialValue: weekTimeFrameSLAs.map((sla) => {
                const option = optionsWithMeta.find(
                    ({ value }) => value === sla.timeFrame,
                );

                return {
                    timeFrame: option,
                };
            }),
            render: WeekTimeFrameSLAField,
            label: t`System access control`,
            fields: {
                timeFrame: {
                    type: 'select',
                    label: '',
                    options: optionsWithMeta,
                },
            },
        },
    };
}

/**
 * Creates options for Grace Period SLA with metadata.
 */
export function createGracePeriodSLAOptions(
    gracePeriodSLAs: GracePeriodSLA[],
): ListBoxItemData[] {
    const metaGracePeriodIds = gracePeriodSLAs.map(
        (sla) => sla.policyGracePeriodSLAId,
    );

    return gracePeriodSLAOptions().map((option) => ({
        ...option,
        meta: {
            policyGracePeriodSLAIds: metaGracePeriodIds,
        },
    }));
}

/**
 * Creates the Grace Period SLA field configuration.
 */
export function createGracePeriodSLAField(
    gracePeriodSLAs: GracePeriodSLA[],
): Pick<FormSchema, 'gracePeriodSLAs'> {
    const optionsWithMeta = createGracePeriodSLAOptions(gracePeriodSLAs);
    const metaGracePeriodIds = gracePeriodSLAs.map(
        (sla) => sla.policyGracePeriodSLAId,
    );

    return {
        gracePeriodSLAs: {
            type: 'custom',
            customType: 'arrayOfObjects',
            showDivider: false,
            initialValue: gracePeriodSLAs.map((sla) => {
                const option = optionsWithMeta.find(
                    ({ value }) => value === sla.gracePeriod,
                );

                return {
                    period: {
                        ...option,
                        meta: {
                            label: sla.label,
                            policyGracePeriodSLAIds: metaGracePeriodIds,
                        },
                    },
                };
            }),
            render: GracePeriodSLAField,
            label: t`Information security`,
            fields: {
                period: {
                    label: t`REMOVE ME`,
                    type: 'select',
                    options: optionsWithMeta,
                },
            },
        },
    };
}

/**
 * Creates the P3 Matrix SLA field configuration.
 */
export function createP3MatrixSLAField(
    p3MatrixSLAs: P3MatrixSLA[],
    p3MatrixSLAsLabel: string,
): Pick<FormSchema, 'p3MatrixSLAs'> {
    const optionsForP3MatrixSLA = p3MatrixSLAOptions();

    return {
        p3MatrixSLAs: {
            type: 'custom',
            customType: 'arrayOfObjects',
            showDivider: false,
            initialValue: p3MatrixSLAs.map((sla) => {
                const timeFrame = optionsForP3MatrixSLA.find(
                    ({ value }) => value === sla.timeFrame,
                );

                return {
                    ...sla,
                    timeFrame,
                };
            }),
            render: P3MatrixSLAFields,
            label: p3MatrixSLAsLabel,
            fields: {
                timeFrame: {
                    type: 'select',
                    label: t`Time frame`,
                    options: optionsForP3MatrixSLA,
                },
                definition: {
                    type: 'textarea',
                    label: t`Definition`,
                    maxCharacters: 30000,
                },
                examples: {
                    type: 'textarea',
                    label: t`Examples`,
                    maxCharacters: 30000,
                },
            },
        },
    };
}

/**
 * Creates the complete SLA configuration field group.
 */
export function createSLAConfigurationField(
    fields: FormSchema,
): Pick<FormSchema, 'slaConfiguration'> {
    return {
        slaConfiguration: {
            type: 'group',
            header: t`Service level agreements`,
            showDivider: false,
            fields,
        },
    };
}
