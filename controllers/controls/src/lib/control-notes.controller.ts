import { head } from 'lodash-es';
import type {
    AttachmentConfig,
    INotesController,
    NoteCreateDto,
    NoteCreateInput,
    NotesLabels,
    NoteUpdateInput,
    StandardNote,
} from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    grcControllerCreateNoteMutation,
    grcControllerDeleteNoteMutation,
    grcControllerGetNotesInfiniteOptions,
    grcControllerUpdateNoteMutation,
} from '@globals/api-sdk/queries';
import type { NoteRequestDto, NoteResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    when,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class ControlNotesController implements INotesController {
    createNoteMutation = new ObservedMutation(grcControllerCreateNoteMutation);
    updateNoteMutation = new ObservedMutation(grcControllerUpdateNoteMutation);
    deleteNoteMutation = new ObservedMutation(grcControllerDeleteNoteMutation);
    getNotesInfiniteQuery = new ObservedInfiniteQuery(
        grcControllerGetNotesInfiniteOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    controlId: number | null = null;

    get isCreating(): boolean {
        return this.createNoteMutation.isPending;
    }

    get hasError(): boolean {
        return this.createNoteMutation.hasError;
    }

    get isUpdating(): boolean {
        return this.updateNoteMutation.isPending;
    }

    get hasUpdateError(): boolean {
        return this.updateNoteMutation.hasError;
    }

    get isDeleting(): boolean {
        return this.deleteNoteMutation.isPending;
    }

    get hasDeleteError(): boolean {
        return this.deleteNoteMutation.hasError;
    }

    get isLoading(): boolean {
        return this.getNotesInfiniteQuery.isLoading;
    }

    get list(): NoteResponseDto[] {
        return (
            this.getNotesInfiniteQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    get total(): number {
        const firstPage = head(this.getNotesInfiniteQuery.data?.pages);

        return firstPage?.total ?? 0;
    }

    /**
     * INotesController data adapter.
     */
    get notes(): StandardNote[] {
        const rawNotes: NoteResponseDto[] = this.list;

        return rawNotes.map((note) => ({
            id: String(note.id),
            comment: note.comment,
            createdAt: new Date(note.createdAt).toISOString(),
            updatedAt: new Date(note.updatedAt).toISOString(),
            owner: {
                id: String(note.owner.id),
                firstName: note.owner.firstName,
                lastName: note.owner.lastName,
                avatarUrl: note.owner.avatarUrl ?? undefined,
            },
            files: note.noteFiles,
        }));
    }

    /**
     * ========== UI Configuration ==========.
     */
    get labels(): NotesLabels {
        return {
            title: t`Internal notes`,
            subtitle: t`Add any feedback or questions you want to track for this control. Control owners and approvers will receive an email notification. These messages are not shared with auditors.`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details for this control.`,
            readOnlyEmptyStateDescription: t`Follow any feedback or questions about this control. These messages are not shared with auditors.`,
        };
    }

    get attachments(): AttachmentConfig {
        return {};
    }

    get maxNoteCharacters(): number {
        return 191;
    }

    /**
     * ========== Optional Flags ==========.
     */
    get isReadOnly(): boolean {
        return false;
    }

    get isUploading(): boolean {
        return false;
    }

    get 'data-id'(): string {
        return 'control-notes';
    }

    get hasSourceInput(): boolean {
        return false;
    }

    get showUnreadIndicators(): boolean {
        return false;
    }

    get enableReadStatusTracking(): boolean {
        return false;
    }

    get enableAuditorOnlyEditing(): boolean {
        return false;
    }

    get documentActive() {
        return null;
    }

    get hasNextPage(): boolean {
        return this.getNotesInfiniteQuery.hasNextPage;
    }

    get isFetchingNextPage(): boolean {
        return this.getNotesInfiniteQuery.isFetching && !this.isLoading;
    }

    /**
     * INotesController infinite scrolling properties.
     */
    get hasMore(): boolean {
        return this.hasNextPage;
    }

    get isLoadingMore(): boolean {
        return this.isFetchingNextPage;
    }

    load = (controlId: number): void => {
        this.controlId = controlId;

        this.getNotesInfiniteQuery.load({
            path: { id: controlId },
            query: {
                page: 1,
                limit: 10,
            },
        });
    };

    loadMore = (): void => {
        if (this.hasNextPage && !this.isFetchingNextPage) {
            this.getNotesInfiniteQuery.nextPage();
        }
    };

    invalidate = (): void => {
        this.getNotesInfiniteQuery.invalidate();
    };

    updateNote(
        noteId: string,
        values: NoteUpdateInput | NoteRequestDto,
        onSuccess: () => void,
    ): void {
        this.updateNoteMutation.mutate({
            path: { noteId },
            body: { comment: values.comment ?? '' },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (this.updateNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'control-note-update-error',
                        props: {
                            title: t`Unable to update note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.getNotesInfiniteQuery.invalidate();
                onSuccess();
                snackbarController.addSnackbar({
                    id: 'control-note-update-success',
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    deleteNote(noteId: string): void {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed.`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: action(() => {
                const timestamp = new Date().toISOString();

                this.deleteNoteMutation.mutate({
                    path: {
                        noteId,
                    },
                });

                when(
                    () => !this.isDeleting,
                    () => {
                        if (this.hasDeleteError) {
                            snackbarController.addSnackbar({
                                id: `${timestamp}-deleted-event-note-error`,
                                props: {
                                    title: t`Unable to delete note`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        this.getNotesInfiniteQuery.invalidate();

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    },
                );
            }),
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    }

    createNote(
        values: NoteCreateInput | NoteCreateDto,
        onSuccess: () => void,
    ): void {
        if (!this.controlId) {
            throw new Error('Control ID is not set');
        }

        this.createNoteMutation.mutate({
            path: { id: this.controlId },
            body: {
                comment: values.comment || '',
            },
        });

        when(
            () => !this.isCreating,
            () => {
                if (this.createNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'control-note-create-error',
                        props: {
                            title: t`Unable to create note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.getNotesInfiniteQuery.invalidate();
                onSuccess();

                snackbarController.addSnackbar({
                    id: 'control-note-create-success',
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }
}

export const sharedControlNotesController = new ControlNotesController();
