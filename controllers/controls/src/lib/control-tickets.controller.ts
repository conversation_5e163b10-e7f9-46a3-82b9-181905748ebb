import { isObject, isString } from 'lodash-es';
import type { ClosedTicketsControllerBase } from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    controlTicketsControllerUnlinkTicketMutation,
    ticketsControllerGetTicketsForControlInfiniteOptions,
    ticketsControllerGetTicketsForControlOptions,
} from '@globals/api-sdk/queries';
import type { TicketResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

const getObjectNamePlural = () => t`controls`;
const getObjectName = () => t`control`;

class ControlTicketsController implements ClosedTicketsControllerBase {
    ticketsInProgressForControlsQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketsForControlInfiniteOptions,
    );
    ticketsCompletedForControlsQuery = new ObservedQuery(
        ticketsControllerGetTicketsForControlOptions,
    );
    ticketsCompletedInfiniteQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketsForControlInfiniteOptions,
    );

    unlinkTicketMutation = new ObservedMutation(
        controlTicketsControllerUnlinkTicketMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get objectName(): string {
        return getObjectName();
    }

    get objectgetObjectNamePluralName(): string {
        return getObjectNamePlural();
    }

    get ticketsInProgress(): TicketResponseDto[] {
        return (
            this.ticketsInProgressForControlsQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasNextPage(): boolean {
        return this.ticketsInProgressForControlsQuery.hasNextPage;
    }

    loadNextPage = (): void => {
        this.ticketsInProgressForControlsQuery.nextPage();
    };

    get isLoadingTicketsInProgress(): boolean {
        return this.ticketsInProgressForControlsQuery.isLoading;
    }

    get totalTicketsInProgress(): number {
        return (
            this.ticketsInProgressForControlsQuery.data?.pages[0]?.total ?? 0
        );
    }

    get ticketsCompleted(): TicketResponseDto[] {
        return this.ticketsCompletedForControlsQuery.data?.data ?? [];
    }

    get ticketsCompletedInfinite(): TicketResponseDto[] {
        return (
            this.ticketsCompletedInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasNextPageCompleted(): boolean {
        return this.ticketsCompletedInfiniteQuery.hasNextPage;
    }

    loadNextPageCompleted = (): void => {
        this.ticketsCompletedInfiniteQuery.nextPage();
    };

    get isLoadingTicketsCompleted(): boolean {
        return this.ticketsCompletedForControlsQuery.isLoading;
    }

    get isLoadingTicketsCompletedInfinite(): boolean {
        return this.ticketsCompletedInfiniteQuery.isLoading;
    }

    get totalTicketsCompleted(): number {
        return this.ticketsCompletedForControlsQuery.data?.total ?? 0;
    }

    get totalTicketsCompletedInfinite(): number {
        return this.ticketsCompletedInfiniteQuery.data?.pages[0]?.total ?? 0;
    }

    get userHasPermissionToCreateTicket(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'CreateTicket',
            'MANAGE',
        );
    }

    get isUnlinkingTicket(): boolean {
        return this.unlinkTicketMutation.isPending;
    }

    unlinkTicket = (ticketId: number, objectId: number) => {
        if (this.isUnlinkingTicket) {
            return;
        }

        openConfirmationModal({
            title: t`Unlink ticket`,
            body: t`Are you sure you want to unlink this ticket? This action cannot be undone.`,
            confirmText: t`Unlink`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.unlinkTicketMutation
                    .mutateAsync({
                        path: { ticketId, controlId: objectId },
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'ticket-unlinked-success',
                            hasTimeout: true,
                            props: {
                                title: t`Ticket unlinked successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: 'Close',
                            },
                        });
                        closeConfirmationModal();
                        // Invalidate queries to refresh the ticket list
                        this.ticketsInProgressForControlsQuery.invalidate();
                        this.ticketsCompletedForControlsQuery.invalidate();
                        this.ticketsCompletedInfiniteQuery.invalidate();
                    })
                    .catch((error) => {
                        let errorMessage = t`Could not unlink ticket at the moment`;

                        // Safely extract error message
                        if (isString(error)) {
                            errorMessage = error;
                        } else if (
                            error &&
                            isObject(error) &&
                            'message' in error &&
                            isString((error as { message?: unknown }).message)
                        ) {
                            errorMessage = (error as { message: string })
                                .message;
                        }

                        snackbarController.addSnackbar({
                            id: 'ticket-unlinked-error',
                            props: {
                                title: errorMessage,
                                severity: 'critical',
                                closeButtonAriaLabel: 'Close',
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    load = (controlId: number) => {
        this.ticketsInProgressForControlsQuery.load({
            // We are limiting the number of tickets in progress to 5 for UX reasons
            query: { controlId, isCompleted: false, limit: 5 },
        });
        this.ticketsCompletedForControlsQuery.load({
            query: { controlId, isCompleted: true },
        });
    };

    loadClosedTicketsInfinite = (controlId: number) => {
        this.ticketsCompletedInfiniteQuery.load({
            query: { controlId, isCompleted: true, limit: 5 },
        });
    };
}

export const sharedControlTicketsController = new ControlTicketsController();
