import { isError } from 'lodash-es';
import { sharedControlTicketsController } from '@controllers/controls';
import type { CreateTicketPayload } from '@controllers/create-ticket';
import { snackbarController } from '@controllers/snackbar';
import { ticketsControllerCreateTicketMutation } from '@globals/api-sdk/queries';
import type { TicketsCreateRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class ControlTicketCreationController {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Mutation for creating control tickets.
     */
    createControlTicketMutation = new ObservedMutation(
        ticketsControllerCreateTicketMutation,
    );

    get isCreating(): boolean {
        return this.createControlTicketMutation.isPending;
    }

    get hasError(): boolean {
        return this.createControlTicketMutation.hasError;
    }

    get error(): Error | null {
        return this.createControlTicketMutation.error;
    }

    /**
     * Creates a ticket for a specific control.
     */
    createControlTicket = (
        payload: CreateTicketPayload,
        controlId: number,
    ): Promise<void> => {
        const currentWorkspaceId =
            sharedWorkspacesController.currentWorkspace?.id;

        if (!currentWorkspaceId) {
            throw new Error('Workspace ID is required to create tickets');
        }

        const requestData: TicketsCreateRequestDto = {
            ...payload,
            relatedEntityIds: [controlId],
            relatedEntityEnum: 'CONTROL',
            workspaceId: currentWorkspaceId,
        };

        return this.createControlTicketMutation
            .mutateAsync({
                body: requestData,
            })
            .then(() => {
                sharedControlTicketsController.ticketsInProgressForControlsQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'control-ticket-create-success',
                    props: {
                        title: t`Control ticket created successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch((error) => {
                snackbarController.addSnackbar({
                    id: 'control-ticket-create-error',
                    props: {
                        title: t`Failed to create control ticket`,
                        description: isError(error)
                            ? error.message
                            : t`Please try again`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedControlTicketCreationController =
    new ControlTicketCreationController();
