import { isEmpty, isNil } from 'lodash-es';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { monitorsControllerListControlTestInstancesOptions } from '@globals/api-sdk/queries';
import type { ControlTestResponseDto } from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

const defaultSelectedMonitorValue = {
    id: '',
    label: '',
    value: '',
};

class MonitorsController {
    constructor() {
        makeAutoObservable(this);

        // Auto-select first monitor when options are available and no selection exists
        reaction(
            () => ({
                monitors: this.controlMonitorsWithIssues,
                hasSelection: <PERSON>olean(this.selectedValue.value),
            }),
            ({ monitors, hasSelection }) => {
                if (!isEmpty(monitors) && !hasSelection) {
                    this.setMonitorSelectedValue({
                        id: monitors[0].testId.toString(),
                        label: monitors[0].name,
                        value: monitors[0].testId.toString(),
                    });
                }
            },
        );
    }

    pagination = {
        page: 1,
        pageSize: 10,
    };

    filters = {
        search: '',
        sortByName: false,
        controlId: null as number | null,
    };

    controlId = 0;

    monitorsQuery = new ObservedQuery(
        monitorsControllerListControlTestInstancesOptions,
    );

    controlMonitorsWithIssuesQuery = new ObservedQuery(
        monitorsControllerListControlTestInstancesOptions,
    );

    invalidate = (): void => {
        this.monitorsQuery.invalidate();
    };

    setControlId(controlId: number | null): void {
        if (!controlId) {
            throw new Error('Control ID is required');
        }

        this.filters.controlId = controlId;
        this.controlId = controlId;
    }

    loadMonitorsByTestName = (testName: string): void => {
        this.monitorsQuery.load({
            path: {
                xProductId: sharedWorkspacesController.currentWorkspace
                    ?.id as number,
            },
            query: {
                name: testName,
                includeDrafts: true,
            },
        });
    };

    selectedValue = {
        id: '',
        label: '',
        value: '',
    } as ListBoxItemData;

    loadAllMonitorsWithIssuesLinkedToControl = (controlId: number): void => {
        when(
            () => sharedWorkspacesController.isLoaded && !isNil(controlId),
            () => {
                type Query = Required<
                    Parameters<
                        typeof monitorsControllerListControlTestInstancesOptions
                    >
                >[0]['query'];

                const queryParams: Query = {
                    controlId,
                    checkResultStatus: 'FAILED',
                    getAll: true,
                    includeArchivedControls: true,
                };

                this.controlMonitorsWithIssuesQuery.load({
                    path: {
                        xProductId: sharedWorkspacesController.currentWorkspace
                            ?.id as number,
                    },
                    query: queryParams,
                });
            },
        );
    };

    loadMonitors = (
        controlIdParam?: number,
        params?: FetchDataResponseParams,
    ): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                if (params) {
                    this.pagination = {
                        page: params.pagination.page || 1,
                        pageSize: params.pagination.pageSize || 10,
                    };

                    this.filters.search = params.globalFilter.search || '';
                    this.filters.sortByName =
                        params.sorting[0]?.id === 'name' &&
                        params.sorting[0]?.desc;
                }

                type Query = Required<
                    Parameters<
                        typeof monitorsControllerListControlTestInstancesOptions
                    >
                >[0]['query'];

                const queryParams: Query = {
                    page: this.pagination.page,
                    limit: this.pagination.pageSize,
                    q: this.filters.search,
                    sortByName: this.filters.sortByName,
                    includeArchivedControls: true,
                    includeDrafts: true,
                };

                if (this.filters.controlId !== null) {
                    queryParams.controlId = controlIdParam ?? this.controlId;
                }

                this.monitorsQuery.load({
                    path: {
                        xProductId: sharedWorkspacesController.currentWorkspace
                            ?.id as number,
                    },
                    query: queryParams,
                });
            },
        );
    };

    get monitors(): ControlTestResponseDto[] {
        return this.monitorsQuery.data?.data ?? [];
    }

    get controlMonitorsWithIssues(): ControlTestResponseDto[] {
        return this.controlMonitorsWithIssuesQuery.data?.data ?? [];
    }

    get monitorsTotal(): number {
        return this.monitorsQuery.data?.total ?? 0;
    }

    get monitorSelected(): ListBoxItemData {
        return this.selectedValue;
    }

    get isLoading(): boolean {
        return this.monitorsQuery.isLoading;
    }

    get isLoadingControlMonitorsWithIssues(): boolean {
        return this.controlMonitorsWithIssuesQuery.isLoading;
    }

    get hasFilters(): boolean {
        return !isEmpty(this.filters.search);
    }

    setMonitorSelectedValue = (monitorSelected: ListBoxItemData) => {
        this.selectedValue = monitorSelected;
        sharedMonitoringTestDetailsController.loadTest(
            Number(monitorSelected.value),
        );
    };

    resetSelectedMonitor = () => {
        this.selectedValue = {
            id: '',
            label: '',
            value: '',
        };
    };

    get monitoringTestsOptions(): ListBoxItemData[] {
        return this.controlMonitorsWithIssues.map((monitor) => ({
            id: monitor.testId.toString(),
            label: monitor.name,
            value: monitor.testId.toString(),
        }));
    }

    getSelectedMonitorTest(): ListBoxItemData | undefined {
        return this.monitoringTestsOptions.find(
            (option) => option.value === this.selectedValue.value,
        );
    }

    getDefaultMonitorTestSelection(): ListBoxItemData {
        return isEmpty(this.monitoringTestsOptions)
            ? defaultSelectedMonitorValue
            : this.monitoringTestsOptions[0];
    }

    get selectedMonitoredTests(): ListBoxItemData {
        return (
            this.getSelectedMonitorTest() ??
            this.getDefaultMonitorTestSelection()
        );
    }
}

export const sharedMonitorsController = new MonitorsController();
