/**
 * Shared constants for audit wizards across the application.
 * These constants are used by both audit creation wizard and audit hub wizard.
 */

import { AuditorFrameworkTypeNames } from '@drata/enums';

export const OS_OPTIONS = [
    {
        id: 'macos',
        value: 'macos',
        label: 'macOS',
    },
    {
        id: 'windows',
        value: 'windows',
        label: 'Windows',
    },
    {
        id: 'linux',
        value: 'linux',
        label: 'Linux',
    },
] as const;

export const CONTROL_EVIDENCE_INPUT_NAMES = {
    PLATFORM: 'platform',
    DATE_RANGE: 'dateRange',
    HIRED_PERSONNEL: 'hiredPersonnel',
    FORMER_PERSONNEL: 'formerPersonnel',
    CURRENT_PERSONNEL: 'currentPersonnel',
} as const;

/**
 * Minimum number of date range selections required for validation.
 */
export const MIN_DATE_RANGE_SELECTIONS = 2;

/**
 * Utility function to check if a framework type is SOC2 Type 1.
 * This can be reused across different controllers and models.
 */
export const isSOC2Type1Framework = (
    frameworkType: string | undefined | null,
): boolean => {
    return frameworkType === AuditorFrameworkTypeNames.SOC_2_TYPE_1;
};
