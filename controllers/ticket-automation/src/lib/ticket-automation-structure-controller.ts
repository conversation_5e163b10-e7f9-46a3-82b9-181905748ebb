import { externalTicketsControllerGetTicketCreationStructureOptions } from '@globals/api-sdk/queries';
import type {
    ExternalTicketsControllerGetTicketCreationStructureData,
    ExternalTicketStructureResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationStructureController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationStructureQuery = new ObservedQuery(
        externalTicketsControllerGetTicketCreationStructureOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationStructureQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationStructureQuery.hasError;
    }

    get ticketAutomationStructure(): ExternalTicketStructureResponseDto['fields'] {
        return this.ticketAutomationStructureQuery.data?.fields ?? {};
    }

    loadTicketAutomationStructure = (
        query: ExternalTicketsControllerGetTicketCreationStructureData['query'],
    ) => {
        this.ticketAutomationStructureQuery.load({ query });
    };
}

export const sharedTicketAutomationStructureController =
    new TicketAutomationStructureController();
