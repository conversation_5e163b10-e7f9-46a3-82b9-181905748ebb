import { ticketsControllerGetTicketUsersInfiniteOptions } from '@globals/api-sdk/queries';
import type { TicketUserResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

class TicketAutomationUsersController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationUsersQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketUsersInfiniteOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationUsersQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationUsersQuery.hasError;
    }

    get hasNextPage(): boolean {
        return this.ticketAutomationUsersQuery.hasNextPage;
    }

    get ticketAutomationUsersList(): TicketUserResponseDto[] {
        return (
            this.ticketAutomationUsersQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get isFetching(): boolean {
        return this.ticketAutomationUsersQuery.isFetching;
    }

    loadNextPage = (): void => {
        this.ticketAutomationUsersQuery.nextPage();
    };

    loadTicketAutomationUsers = ({
        connectionId,
        optionalPaginationRequest,
        search,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
    }) => {
        this.ticketAutomationUsersQuery.load({
            query: {
                connectionId,
                q: search?.trim(),
                optionalPaginationRequest,
            },
        });
    };

    onFetchTicketAutomationUsers = ({
        connectionId,
        optionalPaginationRequest,
        search,
        increasePage,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }

        this.loadTicketAutomationUsers({
            connectionId,
            optionalPaginationRequest,
            search,
        });
    };
}

export const sharedTicketAutomationUsersController =
    new TicketAutomationUsersController();
