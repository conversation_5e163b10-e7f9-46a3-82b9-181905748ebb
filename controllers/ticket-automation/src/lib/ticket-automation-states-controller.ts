import { ticketsControllerGetTicketStatesInfiniteOptions } from '@globals/api-sdk/queries';
import type { TicketsDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

class TicketAutomationStatesController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationStatesQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketStatesInfiniteOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationStatesQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationStatesQuery.hasError;
    }

    get hasNextPage(): boolean {
        return this.ticketAutomationStatesQuery.hasNextPage;
    }

    get ticketAutomationStatesList(): TicketsDataResponseDto['data'] {
        return (
            this.ticketAutomationStatesQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get isFetching(): boolean {
        return this.ticketAutomationStatesQuery.isFetching;
    }

    loadNextPage = (): void => {
        this.ticketAutomationStatesQuery.nextPage();
    };

    loadTicketAutomationStates = ({
        connectionId,
        optionalPaginationRequest,
        search,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
    }) => {
        this.ticketAutomationStatesQuery.load({
            query: {
                connectionId,
                q: search?.trim(),
                optionalPaginationRequest,
            },
        });
    };

    onFetchTicketAutomationStates = ({
        connectionId,
        optionalPaginationRequest,
        search,
        increasePage,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }

        this.loadTicketAutomationStates({
            connectionId,
            optionalPaginationRequest,
            search,
        });
    };
}

export const sharedTicketAutomationStatesController =
    new TicketAutomationStatesController();
