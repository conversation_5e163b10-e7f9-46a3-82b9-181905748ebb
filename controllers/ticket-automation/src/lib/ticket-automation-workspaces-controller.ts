import { externalTicketsControllerGetWorkspacesOptions } from '@globals/api-sdk/queries';
import type { TicketDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationWorkspacesController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationWorkspacesQuery = new ObservedQuery(
        externalTicketsControllerGetWorkspacesOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationWorkspacesQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationWorkspacesQuery.hasError;
    }

    get ticketAutomationWorkspacesList(): TicketDataResponseDto[] {
        return this.ticketAutomationWorkspacesQuery.data?.data ?? [];
    }

    loadTicketAutomationWorkspaces = (connectionId: number) => {
        this.ticketAutomationWorkspacesQuery.load({ query: { connectionId } });
    };
}

export const sharedTicketAutomationWorkspacesController =
    new TicketAutomationWorkspacesController();
