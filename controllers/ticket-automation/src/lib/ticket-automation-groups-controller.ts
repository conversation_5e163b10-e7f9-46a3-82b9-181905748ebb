import { externalTicketsControllerGetFoldersOptions } from '@globals/api-sdk/queries';
import type { TicketDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationFoldersController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationFolders = new ObservedQuery(
        externalTicketsControllerGetFoldersOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationFolders.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationFolders.hasError;
    }

    get ticketAutomationFoldersList(): TicketDataResponseDto[] {
        return this.ticketAutomationFolders.data?.data ?? [];
    }

    loadTicketAutomationFolders = (connectionId: number, space: string) => {
        this.ticketAutomationFolders.load({
            query: { connectionId, optionalPaginationRequest: { space } },
        });
    };
}

export const sharedTicketAutomationFoldersController =
    new TicketAutomationFoldersController();
