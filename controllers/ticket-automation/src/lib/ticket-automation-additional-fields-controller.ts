import {
    externalTicketsControllerGetTicketStructureOptions,
    ticketsControllerGetTicketStructureOptions,
} from '@globals/api-sdk/queries';
import type {
    ExternalTicketsControllerGetTicketStructureData,
    TicketsControllerGetTicketStructureData,
    TicketStructureResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationAdditionalFieldsController {
    isLegacy = false;

    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationAdditionalFieldsQuery = new ObservedQuery(
        externalTicketsControllerGetTicketStructureOptions,
    );

    ticketAutomationLegacyFieldsQuery = new ObservedQuery(
        ticketsControllerGetTicketStructureOptions,
    );

    get isLoading(): boolean {
        return this.isLegacy
            ? this.ticketAutomationLegacyFieldsQuery.isLoading
            : this.ticketAutomationAdditionalFieldsQuery.isLoading;
    }

    get hasError(): boolean {
        return this.isLegacy
            ? this.ticketAutomationLegacyFieldsQuery.hasError
            : this.ticketAutomationAdditionalFieldsQuery.hasError;
    }

    get ticketAutomationFields(): TicketStructureResponseDto['fields'] {
        return this.isLegacy
            ? (this.ticketAutomationLegacyFieldsQuery.data?.fields ?? {})
            : (this.ticketAutomationAdditionalFieldsQuery.data?.fields ?? {});
    }

    loadTicketAutomationAdditionalFields = (
        ticketTypeId: string,
        query: ExternalTicketsControllerGetTicketStructureData['query'],
    ) => {
        this.isLegacy = false;
        this.ticketAutomationAdditionalFieldsQuery.load({
            path: { id: ticketTypeId },
            query,
        });
    };

    loadTicketAutomationLegacyFields = (
        ticketTypeId: string,
        query: TicketsControllerGetTicketStructureData['query'],
    ) => {
        this.isLegacy = true;
        this.ticketAutomationLegacyFieldsQuery.load({
            path: { id: ticketTypeId },
            query,
        });
    };
}

export const sharedTicketAutomationAdditionalFieldsController =
    new TicketAutomationAdditionalFieldsController();
