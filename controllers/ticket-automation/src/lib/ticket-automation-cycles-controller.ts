import { ticketsControllerGetTicketCyclesInfiniteOptions } from '@globals/api-sdk/queries';
import type { TicketsDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

class TicketAutomationCyclesController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationCyclesQuery = new ObservedInfiniteQuery(
        ticketsControllerGetTicketCyclesInfiniteOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationCyclesQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationCyclesQuery.hasError;
    }

    get hasNextPage(): boolean {
        return this.ticketAutomationCyclesQuery.hasNextPage;
    }

    get ticketAutomationCyclesList(): TicketsDataResponseDto['data'] {
        return (
            this.ticketAutomationCyclesQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get isFetching(): boolean {
        return this.ticketAutomationCyclesQuery.isFetching;
    }

    loadNextPage = (): void => {
        this.ticketAutomationCyclesQuery.nextPage();
    };

    loadTicketAutomationCycles = ({
        connectionId,
        optionalPaginationRequest,
        search,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
    }) => {
        this.ticketAutomationCyclesQuery.load({
            query: {
                connectionId,
                q: search?.trim(),
                optionalPaginationRequest,
            },
        });
    };

    onFetchTicketAutomationCycles = ({
        connectionId,
        optionalPaginationRequest,
        search,
        increasePage,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }

        this.loadTicketAutomationCycles({
            connectionId,
            optionalPaginationRequest,
            search,
        });
    };
}

export const sharedTicketAutomationCyclesController =
    new TicketAutomationCyclesController();
