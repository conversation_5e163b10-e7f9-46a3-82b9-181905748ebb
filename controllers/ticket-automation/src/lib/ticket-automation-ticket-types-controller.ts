import { externalTicketsControllerGetTicketTypesOptions } from '@globals/api-sdk/queries';
import type {
    ExternalTicketsControllerGetTicketTypesData,
    ExternalTicketTypesResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationTicketTypesController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationTicketTypesQuery = new ObservedQuery(
        externalTicketsControllerGetTicketTypesOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationTicketTypesQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationTicketTypesQuery.hasError;
    }

    get ticketAutomationTicketTypesList(): ExternalTicketTypesResponseDto['ticketTypes'] {
        return this.ticketAutomationTicketTypesQuery.data?.ticketTypes ?? [];
    }

    loadTicketAutomationTicketTypes = (
        query: ExternalTicketsControllerGetTicketTypesData['query'],
    ) => {
        this.ticketAutomationTicketTypesQuery.load({ query });
    };
}

export const sharedTicketAutomationTicketTypesController =
    new TicketAutomationTicketTypesController();
