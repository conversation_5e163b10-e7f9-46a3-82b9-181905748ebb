import { ticketsControllerGetParentTicketInfiniteOptions } from '@globals/api-sdk/queries';
import type { TicketUserResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

class TicketAutomationParentsController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationParentsQuery = new ObservedInfiniteQuery(
        ticketsControllerGetParentTicketInfiniteOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationParentsQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationParentsQuery.hasError;
    }

    get hasNextPage(): boolean {
        return this.ticketAutomationParentsQuery.hasNextPage;
    }

    get ticketAutomationParentsList(): TicketUserResponseDto[] {
        return (
            this.ticketAutomationParentsQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get isFetching(): boolean {
        return this.ticketAutomationParentsQuery.isFetching;
    }

    loadNextPage = (): void => {
        this.ticketAutomationParentsQuery.nextPage();
    };

    loadTicketAutomationParents = ({
        connectionId,
        optionalPaginationRequest,
        search,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
    }) => {
        this.ticketAutomationParentsQuery.load({
            query: {
                connectionId,
                q: search?.trim(),
                optionalPaginationRequest,
            },
        });
    };

    onFetchTicketAutomationParents = ({
        connectionId,
        optionalPaginationRequest,
        search,
        increasePage,
    }: {
        connectionId: number;
        optionalPaginationRequest?: Record<string, unknown>;
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }

        this.loadTicketAutomationParents({
            connectionId,
            optionalPaginationRequest,
            search,
        });
    };
}

export const sharedTicketAutomationParentsController =
    new TicketAutomationParentsController();
