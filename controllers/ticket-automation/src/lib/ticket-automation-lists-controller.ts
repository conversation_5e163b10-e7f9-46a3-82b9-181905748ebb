import { externalTicketsControllerGetListsOptions } from '@globals/api-sdk/queries';
import type { TicketDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationListsController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationListsQuery = new ObservedQuery(
        externalTicketsControllerGetListsOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationListsQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationListsQuery.hasError;
    }

    get ticketAutomationListsList(): TicketDataResponseDto[] {
        return this.ticketAutomationListsQuery.data?.data ?? [];
    }

    loadTicketAutomationLists = (params: {
        connectionId: number;
        directoryId: string;
        folderId?: string;
    }) => {
        const { connectionId, directoryId, folderId } = params;

        this.ticketAutomationListsQuery.load({
            query: {
                connectionId,
                optionalPaginationRequest: {
                    listPayload: JSON.stringify({
                        folderId,
                        directoryId,
                        type: folderId ? 'folder' : 'folderlessList',
                    }),
                },
            },
        });
    };
}

export const sharedTicketAutomationListsController =
    new TicketAutomationListsController();
