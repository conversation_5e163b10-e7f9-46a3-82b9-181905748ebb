import type {
    AuditorPersonnelDetailsResponseDto,
    FrameworkForNewAuditResponseDto,
} from '@globals/api-sdk/types';

export type PersonnelType = 'hired' | 'former' | 'current';

/**
 * Personnel data structure used across multiple components.
 */
export interface PersonnelData {
    hiredPersonnel: AuditorPersonnelDetailsResponseDto[];
    currentPersonnel: AuditorPersonnelDetailsResponseDto[];
    formerPersonnel: AuditorPersonnelDetailsResponseDto[];
}

export interface AuditorOption {
    id: string;
    label: string;
    value: string;
    metadata?: {
        email?: string;
        firmName?: string;
        avatarUrl?: string;
    };
    [key: string]: unknown;
}

export interface PersonnelOption {
    id: string;
    label: string;
    value: string;
    avatar?: {
        imgSrc?: string;
    };
}

export interface AuditSamplesData {
    platform: {
        id: string;
        label: string;
        value: string;
    } | null;
    dateRange: string[] | string;
    hiredPersonnel: PersonnelOption[];
    formerPersonnel: PersonnelOption[];
    currentPersonnel: PersonnelOption[];
}

export interface AuditCreationWizardData {
    conductAuditOption: string;
    auditDetails?: AuditDetailsType;
    auditSamples: AuditSamplesData;
    assignedAuditors?: AuditorOption[];
}

export interface Dates {
    startingDate: string;
    endingDate?: string;
}
export interface AuditDetailsType {
    name?: string;
    description?: string;
    framework?: FrameworkForNewAuditResponseDto;
    date?: Dates;
}

/**
 * Framework type with string ID for form handling.
 */
export type FrameworkWithStringId = Omit<
    FrameworkForNewAuditResponseDto,
    'id'
> & {
    id: string;
};

// Framework type constants using SDK types for type safety
export const FRAMEWORK_TYPES = {
    SOC_2_TYPE_1: 'SOC_2_TYPE_1' as FrameworkForNewAuditResponseDto['type'],
    SOC_2_TYPE_2: 'SOC_2_TYPE_2' as FrameworkForNewAuditResponseDto['type'],
    ISO_27001: 'ISO_27001' as FrameworkForNewAuditResponseDto['type'],
} as const;
