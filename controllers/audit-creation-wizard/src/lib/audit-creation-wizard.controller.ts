import { isEmpty, isString } from 'lodash-es';
import {
    isSOC2Type1Framework,
    MIN_DATE_RANGE_SELECTIONS,
} from '@controllers/audit-hub';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import {
    auditsControllerPostAuditsMutation,
    usersControllerGetAuditorsOptions,
} from '@globals/api-sdk/queries';
import type {
    AuditorWithAuditsResponseDto,
    AuditRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    toJS,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { FormValues } from '@ui/forms';
import { sharedAuditCreationPersonnelController } from './audit-creation-personnel.controller';
import {
    type AuditCreationWizardData,
    type AuditDetailsType,
    type AuditorOption,
    type AuditSamplesData,
    FRAMEWORK_TYPES,
    type PersonnelOption,
} from './audit-creation-wizard.types';

class AuditCreationWizardController {
    /**
     * ID of the created audit (available after step 2 completion).
     */
    createdAuditId: string | null = null;

    wizardData: AuditCreationWizardData = {
        conductAuditOption: '',
        auditSamples: {
            platform: null,
            dateRange: [], // Will be converted to string for SOC2 Type 1
            hiredPersonnel: [],
            formerPersonnel: [],
            currentPersonnel: [],
        },
        auditDetails: {
            framework: undefined,
            date: {
                startingDate: '',
                endingDate: '',
            },
        },
        assignedAuditors: [],
    };

    auditorsQuery = new ObservedQuery(usersControllerGetAuditorsOptions);

    createAuditMutation = new ObservedMutation(
        auditsControllerPostAuditsMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    addAuditDetails = (data: AuditDetailsType): void => {
        this.wizardData.auditDetails = {
            ...this.wizardData.auditDetails,
            ...data,
        };

        // Auto-create audit and load personnel data when audit details are available
        // This is triggered after step 2 completion
        if (this.isDownloadOnly) {
            this.createAuditAndLoadPersonnel().catch(() => {
                this.handleAuditCreationError();
            });
        }
    };

    setConductAuditOption = (option: string): void => {
        this.wizardData.conductAuditOption = option;
    };

    setAuditSamplesData = (auditSamples: AuditSamplesData): void => {
        this.wizardData.auditSamples = auditSamples;
    };

    setAssignedAuditors = (auditors: AuditorOption[]): void => {
        this.wizardData.assignedAuditors = auditors;
    };

    buildAuditRequest = (): AuditRequestDto | null => {
        const { auditDetails } = this.wizardData;
        const { framework, date } = auditDetails ?? {};
        const productId = sharedWorkspacesController.currentWorkspaceId;

        if (!framework?.id || !date?.startingDate || !productId) {
            return null;
        }

        return {
            frameworkType: framework.type,
            customFrameworkId: `${framework.id}`,
            startDate: date.startingDate,
            endDate: date.endingDate || date.startingDate,
            productId,
            auditType: 'DOWNLOAD_ONLY_AUDIT',
        };
    };

    handleAuditCreationSuccess = (response: { id: string }): void => {
        this.createdAuditId = response.id;
        sharedAuditCreationPersonnelController.loadPersonnelData(response.id);
    };

    handleAuditCreationError = (): void => {
        snackbarController.addSnackbar({
            id: 'audit-creation-error',
            props: {
                title: t`Audit Creation Failed`,
                description: t`Failed to create audit. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    /**
     * Creates audit and loads personnel data.
     * This method creates the audit first, then loads personnel data using the audit ID.
     */
    createAuditAndLoadPersonnel = async (): Promise<void> => {
        const auditRequest = this.buildAuditRequest();

        if (!auditRequest) {
            return;
        }

        await this.createAuditMutation.mutateAsync({
            body: auditRequest,
        });

        const { response } = this.createAuditMutation;

        if (response?.id) {
            this.handleAuditCreationSuccess(response);
        }
    };

    loadInitialAuditors = (): void => {
        this.auditorsQuery.load({
            query: {
                q: '',
                limit: DEFAULT_PAGE_SIZE,
                page: 1,
            },
        });
    };

    handleAuditorSearch = ({
        search,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        this.auditorsQuery.load({
            query: {
                q: search ?? '',
                limit: DEFAULT_PAGE_SIZE,
                page: 1,
            },
        });
    };

    handleAssignAuditorsSubmit = (values: FormValues): void => {
        this.setAssignedAuditors(
            values.assignedAuditors as AuditorOption[] | [],
        );
    };

    addAuditor = (auditor: AuditorOption): void => {
        if (
            !this.wizardData.assignedAuditors ||
            isEmpty(this.wizardData.assignedAuditors)
        ) {
            this.wizardData.assignedAuditors = [];
        }

        const exists = this.wizardData.assignedAuditors.some(
            (existing) => existing.id === auditor.id,
        );

        if (!exists) {
            this.wizardData.assignedAuditors.push(auditor);
        }
    };

    removeAuditor = (auditorId: string): void => {
        if (
            !this.wizardData.assignedAuditors ||
            isEmpty(this.wizardData.assignedAuditors)
        ) {
            return;
        }

        this.wizardData.assignedAuditors =
            this.wizardData.assignedAuditors.filter(
                (auditor) => auditor.id !== auditorId,
            );
    };

    handleAuditSamplesSubmit = (values: FormValues): void => {
        this.setAuditSamplesData({
            platform: values.platform as AuditSamplesData['platform'],
            dateRange: values.dateRange as AuditSamplesData['dateRange'],
            hiredPersonnel: values.hiredPersonnel as PersonnelOption[],
            formerPersonnel: values.formerPersonnel as PersonnelOption[],
            currentPersonnel: values.currentPersonnel as PersonnelOption[],
        });
    };

    validateConductAuditStep = (): boolean => {
        return !isEmpty(this.wizardData.conductAuditOption);
    };

    validateAuditSamplesStep = (): boolean => {
        const { auditSamples } = this.wizardData;

        if (!auditSamples.platform) {
            return false;
        }

        // For SOC2 Type 1, dateRange is a single string, for others it's an array
        if (this.isSoc2Type1) {
            return (
                isString(auditSamples.dateRange) &&
                !isEmpty(auditSamples.dateRange)
            );
        }

        // For other audit types, require minimum date range selections
        const dateRangeLength = Array.isArray(auditSamples.dateRange)
            ? auditSamples.dateRange.length
            : 0;

        return dateRangeLength >= MIN_DATE_RANGE_SELECTIONS;
    };

    get data(): AuditCreationWizardData {
        return toJS(this.wizardData);
    }

    get getWizardData(): AuditCreationWizardData {
        return toJS(this.wizardData);
    }

    get auditDetails() {
        return toJS(this.wizardData.auditDetails);
    }

    get auditSamplesData(): AuditSamplesData {
        return toJS(this.wizardData.auditSamples);
    }

    get conductAuditOption(): string {
        return this.wizardData.conductAuditOption;
    }

    get isDownloadOnly(): boolean {
        return this.wizardData.conductAuditOption === 'download-only';
    }

    get assignedAuditors(): AuditorOption[] | undefined {
        return this.wizardData.assignedAuditors;
    }

    get auditorsData(): AuditorWithAuditsResponseDto[] {
        return this.auditorsQuery.data?.data ?? [];
    }

    get isSoc2Type1(): boolean {
        return isSOC2Type1Framework(this.data.auditDetails?.framework?.type);
    }

    get isSoc2Type2(): boolean {
        return (
            this.data.auditDetails?.framework?.type ===
            FRAMEWORK_TYPES.SOC_2_TYPE_2
        );
    }

    get isIso27001(): boolean {
        return (
            this.data.auditDetails?.framework?.type ===
            FRAMEWORK_TYPES.ISO_27001
        );
    }

    get hasControls(): boolean {
        return (
            !isEmpty(this.data.auditDetails?.framework) &&
            this.data.auditDetails.framework.hasControls
        );
    }

    get isCreatingAudit(): boolean {
        return this.createAuditMutation.isPending;
    }

    get hasAuditCreationError(): boolean {
        return this.createAuditMutation.hasError;
    }

    get auditCreationError(): Error | null {
        return this.createAuditMutation.error;
    }

    get hasCreatedAudit(): boolean {
        return Boolean(this.createdAuditId);
    }
    resetWizardData = (): void => {
        this.wizardData = {
            conductAuditOption: '',
            auditSamples: {
                platform: null,
                dateRange: [], // Will be converted to string for SOC2 Type 1
                hiredPersonnel: [],
                formerPersonnel: [],
                currentPersonnel: [],
            },
            auditDetails: {
                framework: undefined,
                date: {
                    startingDate: '',
                    endingDate: '',
                },
            },
            assignedAuditors: [],
        };

        this.createdAuditId = null;

        sharedAuditCreationPersonnelController.resetPersonnelData();
    };

    completeWizard = (): void => {
        // TODO: Implement actual audit creation API call in https://drata.atlassian.net/browse/ENG-72181

        sharedProgrammaticNavigationController.navigateTo(
            `${routeController.userPartOfUrl}/compliance/audits`,
        );
    };

    cancelWizard = (): void => {
        sharedProgrammaticNavigationController.navigateTo(
            `${routeController.userPartOfUrl}/compliance/audits`,
        );
    };
}

export const sharedAuditCreationWizardController =
    new AuditCreationWizardController();
