import { snackbarController } from '@controllers/snackbar';
import { ticketsControllerGetTicketOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';

export class TicketDownloadController {
    ticketDownloadQuery = new ObservedQuery(ticketsControllerGetTicketOptions);

    constructor() {
        makeAutoObservable(this);
    }

    downloadTicketFiles = (ticketId: number): void => {
        this.ticketDownloadQuery.load({
            path: { ticket_id: ticketId },
            query: { eventCategory: 'GRC' },
        });

        when(() => !this.ticketDownloadQuery.isLoading)
            .then(() => {
                const { data } = this.ticketDownloadQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-ticket-files-error`,
                    props: {
                        title: t`Unable to download ticket documents.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    get isLoading(): boolean {
        return this.ticketDownloadQuery.isLoading;
    }

    get hasError(): boolean {
        return this.ticketDownloadQuery.hasError;
    }

    get error(): Error | null {
        return this.ticketDownloadQuery.error;
    }
}

export const sharedTicketDownloadController = new TicketDownloadController();
