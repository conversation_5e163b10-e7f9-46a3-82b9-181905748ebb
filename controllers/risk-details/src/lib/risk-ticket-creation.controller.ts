import { isError, isObject } from 'lodash-es';
import type { CreateTicketPayload } from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { riskManagementTicketsControllerCreateTicketMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedRiskTicketsController } from './risk-tickets.controller';

const getErrorStatusCode = (error: unknown): string | number => {
    if (isObject(error) && 'status' in error) {
        return error.status as string | number;
    }
    if (isObject(error) && 'statusCode' in error) {
        return error.statusCode as string | number;
    }

    return 'unknown';
};

class RiskTicketCreationController {
    constructor() {
        makeAutoObservable(this);
    }

    createRiskTicketMutation = new ObservedMutation(
        riskManagementTicketsControllerCreateTicketMutation,
    );

    get isCreating(): boolean {
        return this.createRiskTicketMutation.isPending;
    }

    get hasError(): boolean {
        return this.createRiskTicketMutation.hasError;
    }

    get error(): Error | null {
        return this.createRiskTicketMutation.error;
    }

    createRiskTicket = (
        payload: CreateTicketPayload,
        riskId: string,
    ): Promise<void> => {
        return this.createRiskTicketMutation
            .mutateAsync({
                path: { risk_id: riskId },
                body: payload,
            })
            .then(() => {
                sharedRiskTicketsController.load(riskId);
                snackbarController.addSnackbar({
                    id: 'risk-ticket-create-success',
                    props: {
                        title: t`Risk ticket created successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                modalController.closeModal('create-ticket-modal');
            })
            .catch((error: unknown) => {
                const errorTitle = t`Failed to create risk ticket`;
                let errorDescription = t`Please try again`;

                if (isError(error)) {
                    errorDescription = error.message;
                }

                logger.error({
                    message: 'Failed to create risk ticket',
                    additionalInfo: {
                        riskId,
                        connectionId: payload.connectionId,
                        projectId: payload.projectId,
                    },
                    errorObject: {
                        message: isError(error)
                            ? error.message
                            : 'Unknown error',
                        statusCode: getErrorStatusCode(error),
                    },
                });
                snackbarController.addSnackbar({
                    id: 'risk-ticket-create-error',
                    props: {
                        title: errorTitle,
                        description: errorDescription,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedRiskTicketCreationController =
    new RiskTicketCreationController();
