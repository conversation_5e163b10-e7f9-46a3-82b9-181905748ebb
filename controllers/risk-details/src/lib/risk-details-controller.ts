import { isEmpty, orderBy } from 'lodash-es';
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { riskManagementControllerGetRiskOptions } from '@globals/api-sdk/queries';
import type { RiskResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, runInAction } from '@globals/mobx';

interface RiskPaginatedControls {
    data: RiskResponseDto['controls'];
    total: number;
    page: number;
    limit: number;
}

class RiskDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    riskDetailsQuery = new ObservedQuery(
        riskManagementControllerGetRiskOptions,
    );

    loadRiskDetails(riskId: string): void {
        this.riskDetailsQuery.load({ path: { risk_id: riskId } });
    }

    resetControlsPagination = (): void => {
        runInAction(() => {
            this.controlRiskCurrentPage = DEFAULT_PAGE;
            this.controlRiskPageSize = DEFAULT_PAGE_SIZE;
            this.controlRiskSortId = '';
            this.controlRiskSortDirection = false;
        });
    };

    get riskDetails(): RiskResponseDto | null {
        return this.riskDetailsQuery.data;
    }

    get isLoading(): boolean {
        return this.riskDetailsQuery.isLoading;
    }

    get controls(): RiskResponseDto['controls'] {
        const baseControls = this.riskDetails?.controls ?? [];

        // Apply the same sorting that's used in the table to maintain consistency with panel pagination
        if (this.controlRiskSortId && this.controlRiskSortDirection) {
            return orderBy(
                baseControls,
                [this.controlRiskSortId],
                [this.controlRiskSortDirection],
            );
        }

        return baseControls;
    }

    controlRiskCurrentPage = DEFAULT_PAGE;
    controlRiskPageSize = DEFAULT_PAGE_SIZE;
    controlRiskSortId = '';
    controlRiskSortDirection: 'asc' | 'desc' | boolean = false;

    get paginatedControls(): RiskPaginatedControls {
        const startIndex =
            (this.controlRiskCurrentPage - 1) * this.controlRiskPageSize;
        const endIndex = startIndex + this.controlRiskPageSize;

        // Use the already sorted controls from the getter to maintain consistency
        const sortedControls = this.controls;

        return {
            data: sortedControls.slice(startIndex, endIndex),
            total: sortedControls.length,
            page: this.controlRiskCurrentPage,
            limit: this.controlRiskPageSize,
        };
    }

    loadControls = (params: FetchDataResponseParams): void => {
        const { pagination, sorting } = params;
        const { page, pageSize } = pagination;

        this.controlRiskCurrentPage = page ?? 1;
        this.controlRiskPageSize = pageSize;

        if (!isEmpty(sorting)) {
            const sort = sorting[0].id;
            const direction = sorting[0].desc ? 'desc' : 'asc';

            this.controlRiskSortId = sort;
            this.controlRiskSortDirection = direction;
        }
    };

    loadControlsWithFreshPagination = (
        params: FetchDataResponseParams,
    ): void => {
        this.loadControls(params);
    };
}

export const sharedRiskDetailsController = new RiskDetailsController();
