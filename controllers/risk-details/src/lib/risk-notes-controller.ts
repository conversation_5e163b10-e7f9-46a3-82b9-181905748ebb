import type {
    AttachmentConfig,
    INotesController,
    NoteCreateInput,
    NotesLabels,
    NoteUpdateInput,
    StandardNote,
} from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    riskManagementControllerAppendNoteToRiskMutation,
    riskManagementControllerDeleteRiskNoteMutation,
    riskManagementControllerUpdateRiskNoteMutation,
} from '@globals/api-sdk/queries';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    when,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedRiskDetailsController } from './risk-details-controller';

class RiskNotesController implements INotesController {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    createNoteMutation = new ObservedMutation(
        riskManagementControllerAppendNoteToRiskMutation,
    );
    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    updateNoteMutation = new ObservedMutation(
        riskManagementControllerUpdateRiskNoteMutation,
    );
    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    deleteNoteMutation = new ObservedMutation(
        riskManagementControllerDeleteRiskNoteMutation,
    );

    riskId: string | null = null;

    /**
     * ========== Data Adapters ==========.
     */
    get isLoading(): boolean {
        return sharedRiskDetailsController.isLoading;
    }

    get notes(): StandardNote[] {
        return (sharedRiskDetailsController.riskDetails?.notes ?? []).map(
            (note) => ({
                id: String(note.id),
                comment: note.comment,
                createdAt: new Date(note.createdAt).toISOString(),
                updatedAt: new Date(note.updatedAt).toISOString(),
                owner: {
                    id: String(note.owner.id),
                    firstName: note.owner.firstName,
                    lastName: note.owner.lastName,
                    avatarUrl: note.owner.avatarUrl ?? undefined,
                },
                // Risk notes currently don't support attachments
                files: [],
            }),
        );
    }

    /**
     * ========== Mutation State ==========.
     */
    get isCreating(): boolean {
        return this.createNoteMutation.isPending;
    }

    get hasError(): boolean {
        return this.createNoteMutation.hasError;
    }

    get isUpdating(): boolean {
        return this.updateNoteMutation.isPending;
    }

    get hasUpdateError(): boolean {
        return this.updateNoteMutation.hasError;
    }

    get isDeleting(): boolean {
        return this.deleteNoteMutation.isPending;
    }

    get hasDeleteError(): boolean {
        return this.deleteNoteMutation.hasError;
    }

    get lastError() {
        return (
            this.createNoteMutation.error ??
            this.updateNoteMutation.error ??
            this.deleteNoteMutation.error ??
            null
        );
    }

    /**
     * ========== UI Configuration ==========.
     */
    get labels(): NotesLabels {
        return {
            title: t`Internal notes`,
            subtitle: t`Add any feedback or questions you want to track for this risk. Risk owners and reviewers will receive an email notification. These messages are not shared with auditors.`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details for this risk.`,
            readOnlyEmptyStateDescription: t`Follow any feedback or questions about this risk. These messages are not shared with auditors.`,
        };
    }

    get attachments(): AttachmentConfig {
        return {};
    }

    get maxNoteCharacters(): number {
        return 191;
    }

    /**
     * ========== Optional Flags ==========.
     */
    get isReadOnly(): boolean {
        return !sharedFeatureAccessModel.hasRiskManagePermission;
    }

    get isUploading(): boolean {
        return false;
    }

    get 'data-id'(): string {
        return 'risk-notes';
    }

    get hasSourceInput(): boolean {
        return false;
    }

    get showUnreadIndicators(): boolean {
        return false;
    }

    get enableReadStatusTracking(): boolean {
        return false;
    }

    get enableAuditorOnlyEditing(): boolean {
        return false;
    }

    get documentActive() {
        return null;
    }

    /**
     * ========== Infinite scrolling properties ==========.
     */
    get hasMore(): boolean {
        // No pagination support yet
        return false;
    }

    get isLoadingMore(): boolean {
        // No pagination support yet
        return false;
    }

    loadMore = (): void => {
        // No pagination support yet - does nothing
    };

    /**
     * ========== Wiring ==========.
     */
    setRiskId = (riskId: string): void => {
        this.riskId = riskId;
    };

    /**
     * ========== CRUD ==========.
     */
    createNote = (values: NoteCreateInput, onSuccess: () => void): void => {
        if (!this.riskId) {
            throw new Error('Risk ID is not set');
        }

        this.createNoteMutation.mutate({
            path: { risk_id: this.riskId },
            body: { comment: values.comment },
        });

        when(
            () => !this.isCreating,
            () => {
                if (this.createNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'risk-note-create-error',
                        props: {
                            title: t`Unable to create note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Refresh risk details to show updated notes
                if (this.riskId) {
                    sharedRiskDetailsController.loadRiskDetails(this.riskId);
                }

                snackbarController.addSnackbar({
                    id: 'risk-note-create-success',
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess();
            },
        );
    };

    updateNote = (
        noteId: string,
        values: NoteUpdateInput,
        onSuccess: () => void,
    ): void => {
        if (!this.riskId) {
            throw new Error('Risk ID is not set');
        }

        this.updateNoteMutation.mutate({
            path: { risk_id: this.riskId, note_id: Number(noteId) },
            body: { comment: values.comment ?? '' },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (this.updateNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'risk-note-update-error',
                        props: {
                            title: t`Unable to update note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Refresh risk details to show updated notes
                if (this.riskId) {
                    sharedRiskDetailsController.loadRiskDetails(this.riskId);
                }

                snackbarController.addSnackbar({
                    id: 'risk-note-update-success',
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess();
            },
        );
    };

    deleteNote = (noteId: string): void => {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed.`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: action(() => {
                if (!this.riskId) {
                    throw new Error('Risk ID is not set');
                }

                const timestamp = new Date().toISOString();

                this.deleteNoteMutation.mutate({
                    path: {
                        risk_id: this.riskId,
                        note_id: Number(noteId),
                    },
                });

                when(
                    () => !this.isDeleting,
                    () => {
                        if (this.hasDeleteError) {
                            snackbarController.addSnackbar({
                                id: `${timestamp}-deleted-risk-note-error`,
                                props: {
                                    title: t`Unable to delete note`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        // Refresh risk details to show updated notes
                        if (this.riskId) {
                            sharedRiskDetailsController.loadRiskDetails(
                                this.riskId,
                            );
                        }

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-risk-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    },
                );
            }),
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };
}

export const sharedRiskNotesController = new RiskNotesController();
