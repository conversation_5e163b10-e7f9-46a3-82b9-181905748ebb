import { isEmpty } from 'lodash-es';
import type {
    AttachmentConfig,
    INotesController,
    NoteCreateInput,
    NoteUpdateInput,
    StandardNote,
} from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import {
    eventsControllerCreateNoteMutation,
    eventsControllerDeleteNoteFileMutation,
    eventsControllerDeleteNoteMutation,
    eventsControllerGetEventNoteFileOptions,
    eventsControllerGetNotesInfiniteOptions,
    eventsControllerUpdateNoteMutation,
    eventsControllerUploadNotesMutation,
} from '@globals/api-sdk/queries';
import type {
    EventsControllerDeleteNoteError,
    NoteResponseDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class EventsNotesController implements INotesController {
    eventNotesInfiniteQuery = new ObservedInfiniteQuery(
        eventsControllerGetNotesInfiniteOptions,
    );

    eventNoteCreateMutation = new ObservedMutation(
        eventsControllerCreateNoteMutation,
        {
            onSuccess: () => {
                this.eventNotesInfiniteQuery.invalidate();
            },
        },
    );
    eventNoteUpdateMutation = new ObservedMutation(
        eventsControllerUpdateNoteMutation,
    );
    eventNoteDeleteMutation = new ObservedMutation(
        eventsControllerDeleteNoteMutation,
    );
    eventDownloadAttachmentQuery = new ObservedQuery(
        eventsControllerGetEventNoteFileOptions,
    );
    eventNoteUploadMutation = new ObservedMutation(
        eventsControllerUploadNotesMutation,
    );
    eventNoteDeleteFileMutation = new ObservedMutation(
        eventsControllerDeleteNoteFileMutation,
    );

    eventId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * INotesController adapter.
     */
    get notes(): StandardNote[] {
        // Use infinite notes if available, otherwise fallback to regular query
        const raw: NoteResponseDto[] =
            this.eventNotesInfiniteQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? [];

        return raw.map((note) => ({
            id: String(note.id),
            comment: note.comment,
            createdAt: new Date(note.createdAt).toISOString(),
            updatedAt: new Date(note.updatedAt).toISOString(),
            owner: {
                id: String(note.owner.id),
                firstName: note.owner.firstName,
                lastName: note.owner.lastName,
                avatarUrl: note.owner.avatarUrl ?? undefined,
            },
            files: note.noteFiles,
        }));
    }

    get isLoading(): boolean {
        return this.eventNotesInfiniteQuery.isLoading;
    }

    get isCreating(): boolean {
        return this.eventNoteCreateMutation.isPending;
    }

    get hasCreateError(): boolean {
        return this.eventNoteCreateMutation.hasError;
    }

    get labels() {
        return {
            title: t`Internal notes`,
            subtitle: t`Add any feedback or questions you want to track for this event. These messages are not shared with auditors`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details for this event.`,
            readOnlyEmptyStateDescription: t`Follow any feedback or questions about this event. These messages are not shared with auditors.`,
        };
    }

    get attachments(): AttachmentConfig {
        return {
            showAddAttachment: 'modal',
            includeAttachmentCreationDate: false,
            includeAttachmentTitle: true,
            acceptedFormats: [
                'pdf',
                'docx',
                'odt',
                'xlsx',
                'ods',
                'pptx',
                'odp',
                'gif',
                'jpeg',
                'png',
            ],
        };
    }

    get maxNoteCharacters(): number {
        return 5000;
    }

    get isUploading(): boolean {
        return this.eventNoteUploadMutation.isPending;
    }

    get 'data-id'(): string {
        return 'events-notes';
    }

    get showUnreadIndicators(): boolean {
        return false;
    }

    get enableReadStatusTracking(): boolean {
        return false;
    }

    get enableAuditorOnlyEditing(): boolean {
        return false;
    }

    get hasSourceInput(): boolean {
        return false;
    }

    get documentActive() {
        return null;
    }

    get lastError() {
        return (
            this.eventNoteCreateMutation.error ??
            this.eventNoteUpdateMutation.error ??
            this.eventNoteDeleteMutation.error ??
            this.eventNoteUploadMutation.error ??
            this.eventNoteDeleteFileMutation.error ??
            null
        );
    }

    get hasMore(): boolean {
        return this.eventNotesInfiniteQuery.hasNextPage;
    }

    /**
     * INotesController isLoadingMore property.
     */
    get isLoadingMore(): boolean {
        return this.eventNotesInfiniteQuery.isFetching;
    }

    loadNotes = (eventId: string, params?: FetchDataResponseParams): void => {
        this.eventId = eventId;
        const { pagination } = params ?? {
            // FOR NOTES IS 10
            pagination: { pageSize: 10, page: 1 },
        };

        const { pageSize, page } = pagination;

        const query = {
            page,
            limit: pageSize,
        };

        this.eventNotesInfiniteQuery.load({
            path: { id: eventId },
            query: {
                ...query,
            },
        });
    };

    loadMore = (): void => {
        if (this.hasMore && !this.isLoadingMore) {
            this.eventNotesInfiniteQuery.nextPage();
        }
    };

    createNote(values: NoteCreateInput, onSuccess: () => void): void {
        if (!this.eventId) {
            throw new Error('Event ID is not set');
        }
        const fileMetadata = values.files?.map((file) => ({
            originalFile: file.name,
            name: file.name,
            creationDate: new Date().toISOString(),
        }));

        this.eventNoteCreateMutation.mutate({
            path: { id: this.eventId },
            body: {
                comment: values.comment,
                ...(values.files && !isEmpty(values.files)
                    ? { 'files[]': values.files as (Blob | File)[] }
                    : {}),
                ...(fileMetadata && !isEmpty(fileMetadata)
                    ? { fileMetadata }
                    : {}),
            },
        });

        when(
            () => !this.isCreating,
            () => {
                const timestamp = new Date().toISOString();

                if (this.hasCreateError) {
                    snackbarController.addSnackbar({
                        id: `${timestamp}-created-event-note-error`,
                        props: {
                            title: t`Couldn't create note. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `${timestamp}-created-event-note-success`,
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess();
            },
        );
    }

    updateNote(
        noteId: string,
        values: NoteUpdateInput,
        onSuccess: () => void,
    ): void {
        const timestamp = new Date().toISOString();
        const files = values.files as (File | Blob)[] | undefined;
        const { filesToDelete } = values;
        const { comment } = values;
        const fileMetadata = files?.map((file) => ({
            originalFile: file instanceof File ? file.name : 'blob',
            name: file instanceof File ? file.name : 'blob',
            creationDate: new Date().toISOString(),
        }));

        if (comment && !isEmpty(comment)) {
            this.eventNoteUpdateMutation.mutate({
                path: { noteId },
                body: { comment },
            });
            when(
                () => !this.eventNoteUpdateMutation.isPending,
                () => {
                    if (this.eventNoteUpdateMutation.hasError) {
                        this.handleUpdateError(timestamp);

                        return;
                    }
                    this.processFileOperations(
                        noteId,
                        timestamp,
                        files,
                        filesToDelete,
                        fileMetadata,
                    );
                    onSuccess();
                },
            );
        } else {
            this.processFileOperations(
                noteId,
                timestamp,
                files,
                filesToDelete,
                fileMetadata,
            );
        }
    }

    processFileOperations = (
        noteId: string,
        timestamp: string,
        files?: (File | Blob)[],
        filesToDelete?: string[],
        fileMetadata?: Record<string, unknown>[],
    ): void => {
        if (files && !isEmpty(files)) {
            this.uploadFiles(noteId, files, fileMetadata, timestamp, () => {
                if (filesToDelete) {
                    this.deleteFiles(filesToDelete, timestamp);
                } else {
                    this.handleUpdateSuccess(timestamp);
                }
            });
        } else if (filesToDelete) {
            this.deleteFiles(filesToDelete, timestamp);
        } else {
            this.handleUpdateSuccess(timestamp);
        }
    };

    uploadFiles = (
        noteId: string,
        files: (File | Blob)[],
        fileMetadata: Record<string, unknown>[] | undefined,
        timestamp: string,
        onComplete?: () => void,
    ): void => {
        const [file, ...remainingFiles] = files;
        const metadata = fileMetadata?.[0];
        const fileName = file instanceof File ? file.name : `file-0`;

        this.eventNoteUploadMutation.mutate({
            path: { noteId },
            body: {
                file,
                creationDate:
                    (metadata?.creationDate as string) ||
                    new Date().toISOString(),
                name: (metadata?.name as string) || fileName,
            },
        });

        when(
            () => !this.eventNoteUploadMutation.isPending,
            () => {
                if (this.eventNoteUploadMutation.hasError) {
                    this.handleUpdateError(timestamp);

                    return;
                }

                if (!isEmpty(remainingFiles)) {
                    this.uploadFiles(
                        noteId,
                        remainingFiles,
                        fileMetadata?.slice(1),
                        timestamp,
                        onComplete,
                    );
                } else if (onComplete) {
                    onComplete();
                }
            },
        );
    };

    deleteFiles = (filesToDelete: string[], timestamp: string): void => {
        if (isEmpty(filesToDelete)) {
            this.handleUpdateSuccess(timestamp);

            return;
        }

        const [fileId, ...remainingFiles] = filesToDelete;

        this.eventNoteDeleteFileMutation.mutate({
            path: { noteFileId: fileId },
        });

        when(
            () => !this.eventNoteDeleteFileMutation.isPending,
            () => {
                if (this.eventNoteDeleteFileMutation.hasError) {
                    this.handleUpdateError(timestamp);

                    return;
                }

                if (isEmpty(remainingFiles)) {
                    this.handleUpdateSuccess(timestamp);
                } else {
                    this.deleteFiles(remainingFiles, timestamp);
                }
            },
        );
    };

    handleUpdateSuccess = (timestamp: string): void => {
        this.eventNotesInfiniteQuery.invalidate();
        snackbarController.addSnackbar({
            id: `${timestamp}-updated-event-note-success`,
            props: {
                title: t`Note updated`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    handleUpdateError = (timestamp: string): void => {
        snackbarController.addSnackbar({
            id: `${timestamp}-updated-event-note-error`,
            props: {
                title: t`Unable to update note`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    deleteNote(noteId: string): void {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                const timestamp = new Date().toISOString();

                this.eventNoteDeleteMutation
                    .mutateAsync({
                        path: { noteId },
                    })
                    .then(() => {
                        this.eventNotesInfiniteQuery.invalidate();
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch((error: EventsControllerDeleteNoteError) => {
                        const title =
                            error.statusCode === 403
                                ? t`Only the same owner can delete a note`
                                : t`Unable to delete note at the moment`;

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-error`,
                            props: {
                                title,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    }

    get isReadOnly(): boolean {
        return !sharedFeatureAccessModel.hasEventManagePermission;
    }

    downloadNoteAttachment = (noteFileId: string): void => {
        this.eventDownloadAttachmentQuery.load({ path: { noteFileId } });
        when(() => !this.eventDownloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.eventDownloadAttachmentQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedEventsNotesController = new EventsNotesController();
