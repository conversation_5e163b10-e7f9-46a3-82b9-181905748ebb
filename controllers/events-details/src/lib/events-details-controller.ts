import { isEmpty, isNil } from 'lodash-es';
import { CHECK_RESULT_STATUS_ENUM } from '@components/events-detail-card';
import { sharedAIExecutionController } from '@controllers/monitoring';
import { snackbarController } from '@controllers/snackbar';
import { SocketEvent } from '@drata/enums';
import {
    eventsControllerGetEventDetailsOptions,
    eventsControllerGetEventPdfDownloadUrlOptions,
    eventsControllerGetEventPdfUrlPreviewOptions,
} from '@globals/api-sdk/queries';
import type {
    AiExecutionGroupResponseDto,
    EventDetailsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { isProviderType, providers } from '@globals/providers';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    codeToJSONString,
    downloadBlob,
    sanitizeFileName,
} from '@helpers/download-file';
import { MAX_MEGABYTES_EVENT_SIZE_TO_PROCESS, VALUE_SIZE } from './constants';

class EventsDetailsController {
    constructor() {
        makeAutoObservable(this, { eventsDetails: false });
    }

    eventsDetails = new ObservedQuery(eventsControllerGetEventDetailsOptions);

    getDownloadPDFUrlQuery = new ObservedQuery(
        eventsControllerGetEventPdfDownloadUrlOptions,
    );

    getDownloadPDFUrlPreviewQuery = new ObservedQuery(
        eventsControllerGetEventPdfUrlPreviewOptions,
    );

    get isLoading(): boolean {
        return this.eventsDetails.isLoading;
    }

    get eventsDetailsData(): EventDetailsResponseDto | null {
        return this.eventsDetails.data;
    }

    get eventsDetailsCode() {
        return this.eventsDetails.data?.metadata;
    }

    get eventsDetailsType() {
        return this.eventsDetails.data?.type;
    }

    get connectionClientType() {
        return this.eventsDetails.data?.connection?.clientType;
    }

    get providerName(): string {
        const clientType = this.connectionClientType;

        if (!clientType) {
            return '';
        }

        if (isProviderType(clientType)) {
            return providers[clientType].name;
        }

        return clientType;
    }

    get isEventDataTooLarge() {
        if (isNil(this.eventsDetails.data?.metadata)) {
            return false;
        }
        const codeLength = new TextEncoder().encode(
            JSON.stringify(this.eventsDetails.data.metadata),
        ).length;

        return (
            codeLength / VALUE_SIZE / VALUE_SIZE >
            MAX_MEGABYTES_EVENT_SIZE_TO_PROCESS
        );
    }

    get isGeneratingAiSummary(): boolean {
        return sharedAIExecutionController.isProcessing;
    }

    get aiSummaryError(): string | null {
        const { executionGroups } = sharedAIExecutionController;
        const errorExecution = executionGroups
            .flatMap((group) => group.executions)
            .find((execution) => execution.status === 'ERROR');

        if (errorExecution) {
            logger.log({
                loggerLevel: 'error',
                message: 'AI summary generation failed',
                additionalInfo: {
                    eventId: this.eventsDetailsData?.id,
                    errorCode: errorExecution.errors[0]?.code,
                    executionId: errorExecution.id,
                },
            });

            return (
                errorExecution.errors[0]?.code ?? 'AI summary generation failed'
            );
        }

        return null;
    }

    get aiSummaryData(): AiExecutionGroupResponseDto | null {
        const { executionGroups } = sharedAIExecutionController;
        const eventId = this.eventsDetailsData?.id;

        if (!eventId) {
            return null;
        }

        const matchingGroup = executionGroups.find(
            (group) => group.featureId === eventId,
        );

        return matchingGroup ?? null;
    }

    get hasAiSummary(): boolean {
        const data = this.aiSummaryData;

        return Boolean(data?.id);
    }

    get shouldShowAiSummary(): boolean {
        if (!this.eventsDetailsData) {
            return false;
        }

        const { controlTestInstance, haveFailedResources, status } =
            this.eventsDetailsData;

        return Boolean(
            controlTestInstance?.runMode === 'AP2' &&
                !isNil(controlTestInstance.ap2EnabledAt) &&
                status === CHECK_RESULT_STATUS_ENUM.FAILED &&
                haveFailedResources,
        );
    }

    loadEventDetails = (eventId: string): void => {
        when(
            () => !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspace?.id;

                this.eventsDetails.load({
                    path: { id: eventId },
                    query: { workspaceId, includeMetadata: true },
                });
            },
        );
    };

    downloadTXT(): void {
        try {
            if (isEmpty(this.eventsDetailsData)) {
                throw new Error('Event details are not available');
            }
            const { category, type, createdAt, metadata } =
                this.eventsDetailsData;
            const [eventDate] = new Date(createdAt).toISOString().split('T');
            const fileName = sanitizeFileName(
                `${eventDate}-${category}-${type}.txt`,
            );

            // Create a new Blob object with the desired content
            const blob = new Blob([codeToJSONString(metadata)], {
                type: 'text/plain',
            });

            downloadBlob(blob, fileName);
        } catch (error: unknown) {
            const timestamp = new Date().toISOString();

            snackbarController.addSnackbar({
                id: `${timestamp}-cant-download-txt`,
                props: {
                    title: 'Cant download TXT at the moment. Please try again later.',
                    description: 'Please try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
            // TODO: remove this catch block once the logger is implemented.
            console.error(error);
        }
    }

    downloadPDF(): void {
        try {
            if (isEmpty(this.eventsDetailsData)) {
                throw new Error('Event details are not available');
            }

            this.getDownloadPDFUrlQuery.load({
                path: {
                    id: this.eventsDetailsData.id,
                },
            });

            reaction(
                () => !this.getDownloadPDFUrlQuery.hasError,
                () => {
                    throw this.getDownloadPDFUrlQuery.error as Error;
                },
            );
        } catch (error) {
            snackbarController.addSnackbar({
                id: 'cant-download-pdf',
                props: {
                    title: 'Cant download PDF at the moment.',
                    description: 'Please try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
            console.error(error);
        }
    }

    downloadPDFPreview(eventId: string, evidenceId: number) {
        try {
            this.getDownloadPDFUrlPreviewQuery.load({
                path: {
                    id: eventId,
                    evidenceId,
                },
            });

            when(
                () => this.getDownloadPDFUrlPreviewQuery.hasError,
                () => {
                    throw this.getDownloadPDFUrlPreviewQuery.error as Error;
                },
            );
        } catch (error) {
            snackbarController.addSnackbar({
                id: 'cant-download-pdf-preview',
                props: {
                    title: 'Cant download PDF preview at the moment.',
                    description: 'Please try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
            console.error(error);
        }
    }

    handleGenerateAiSummary = (): void => {
        const eventId = this.eventsDetailsData?.id;

        if (!eventId) {
            return;
        }

        sharedAIExecutionController.loadExecutionGroups(
            {
                processType: 'SUMMARY',
                processFeature: 'EVENT_TEST_FAILURE',
                featureIds: [eventId],
                page: 1,
                limit: 20,
            },
            {
                channelType: 'company',
                eventName: SocketEvent.TEST_FAILURES_SUMMARY,
                caller: 'EventsDetailsController',
            },
            {
                message: t`AI could not generate a response. Please try again.`,
            },
        );

        sharedAIExecutionController.generateExecution({
            processType: 'SUMMARY',
            processFeature: 'EVENT_TEST_FAILURE',
            featureId: eventId,
        });
    };
}

export const sharedEventsDetailsController = new EventsDetailsController();
