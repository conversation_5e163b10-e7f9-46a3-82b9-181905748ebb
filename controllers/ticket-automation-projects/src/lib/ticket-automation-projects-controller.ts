import { ticketsControllerGetProjectsOptions } from '@globals/api-sdk/queries';
import type { TicketDataResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class TicketAutomationProjectsController {
    constructor() {
        makeAutoObservable(this);
    }

    ticketAutomationProjects = new ObservedQuery(
        ticketsControllerGetProjectsOptions,
    );

    get isLoading(): boolean {
        return this.ticketAutomationProjects.isLoading;
    }

    get hasError(): boolean {
        return this.ticketAutomationProjects.hasError;
    }

    get ticketAutomationProjectList(): TicketDataResponseDto[] {
        return this.ticketAutomationProjects.data?.data ?? [];
    }

    loadTicketAutomationProjects = (
        connectionId: number,
        workspace?: string | null,
    ) => {
        this.ticketAutomationProjects.load({
            query: {
                connectionId,
                optionalPaginationRequest: workspace
                    ? { workspace }
                    : undefined,
            },
        });
    };
}

export const sharedTicketAutomationProjectsController =
    new TicketAutomationProjectsController();
