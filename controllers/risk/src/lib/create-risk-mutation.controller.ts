import { isEmpty, isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { VendorRiskUploadStatus } from '@controllers/vendors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    riskManagementControllerCreateCustomRiskMutation,
    riskManagementControllerUploadDocumentsMutation,
} from '@globals/api-sdk/queries';
import type {
    AuditFrameworkControlResponseDto,
    CustomFieldsSubmissionResponseDto,
    RiskRequestDto,
    RiskResponseDto,
} from '@globals/api-sdk/types';
import { zRiskRequestDto } from '@globals/api-sdk/zod';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    toJS,
    when,
} from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormValues } from '@ui/forms';
import type { CreateRiskCustomMutationType } from '../types/create-risk-custom-mutation.type';
import { sharedControlsForRiskManagementController } from './controls-for-risk-management.controller';
import {
    getAdaptedControls,
    getInherentValue,
    getResidualScore,
    getResidualValue,
    getScore,
    getUniqueIdsList,
} from './helpers/risks-adapter.helper';

class CreateRiskMutationController {
    risk: RiskResponseDto | null = null;
    uploadStatus: VendorRiskUploadStatus = 'NOT_STARTED';
    mutatedRiskDetails: RiskRequestDto | null = null;
    mappedControls: ListBoxItemData[] = [];
    createRiskWizardData: CreateRiskCustomMutationType | null = null;
    customFields: Record<string, string> = {};
    availableCustomFields: CustomFieldsSubmissionResponseDto[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    createRiskMutation = new ObservedMutation(
        riskManagementControllerCreateCustomRiskMutation,
    );

    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    vendorRiskUploadDocumentMutation = new ObservedMutation(
        riskManagementControllerUploadDocumentsMutation,
    );

    get newRiskId(): string | null {
        return this.createRiskMutation.response?.riskId ?? null;
    }

    setCustomFields = (data: FormValues) => {
        const customFieldEntries = Object.entries(data).filter(
            ([key, value]) =>
                key.startsWith('customField_') &&
                value !== '' &&
                value !== null &&
                value !== undefined,
        );

        if (!isEmpty(customFieldEntries)) {
            for (const [key, value] of customFieldEntries) {
                this.customFields[key] = String(value);
            }
        }
    };

    setAvailableCustomFields = (data: CustomFieldsSubmissionResponseDto[]) => {
        this.availableCustomFields = data;
    };

    setCreateRiskWizardDataOverride = (data: FormValues) => {
        this.setCustomFields(data);

        this.createRiskWizardData = {
            ...this.createRiskWizardData,
            ...data,
        };
    };

    getMutatedStateForStep = (
        stepName: string,
    ): Partial<CreateRiskCustomMutationType> => {
        if (!this.createRiskWizardData) {
            return {};
        }

        switch (stepName) {
            case 'sourceAndStatus': {
                return {
                    riskSourceGroup: this.createRiskWizardData.riskSourceGroup,
                    statusGroup: this.createRiskWizardData.statusGroup,
                    vendorSource: this.createRiskWizardData.vendorSource,
                };
            }
            case 'details': {
                return {
                    riskDetailsGroup:
                        this.createRiskWizardData.riskDetailsGroup,
                    customFields: this.customFields,
                };
            }
            case 'assessmentAndTreatment': {
                return {
                    inherentScoreGroup:
                        this.createRiskWizardData.inherentScoreGroup,
                    treatmentGroup: this.createRiskWizardData.treatmentGroup,
                    customFields: this.customFields,
                };
            }
            default: {
                return {};
            }
        }
    };

    setMappedControls = (controls: ListBoxItemData[]) => {
        this.mappedControls = controls;
    };

    resetProcess = () => {
        this.risk = null;
        this.uploadStatus = 'NOT_STARTED';
        this.mutatedRiskDetails = null;
        this.createRiskWizardData = null;
        this.customFields = {};
        this.availableCustomFields = [];
    };

    createMutatedRiskDetails = () => {
        const formValues = toJS(this.createRiskWizardData) ?? {};
        const type = formValues.riskSourceGroup?.riskSource ?? 'INTERNAL';
        const status = formValues.statusGroup?.status.value ?? 'ACTIVE';
        const controls = getAdaptedControls(
            sharedControlsForRiskManagementController.selectedControls as AuditFrameworkControlResponseDto[],
        );
        const identifiedAt = formValues.riskDetailsGroup?.identifiedAt
            ? formatDate('timestamp', formValues.riskDetailsGroup.identifiedAt)
            : undefined;
        const categories = formValues.riskDetailsGroup?.categories
            ? getUniqueIdsList(formValues.riskDetailsGroup.categories)
            : undefined;
        const owners = formValues.riskDetailsGroup?.owners
            ? getUniqueIdsList(formValues.riskDetailsGroup.owners)
            : undefined;
        const impact = getInherentValue(formValues, 'impact');
        const likelihood = getInherentValue(formValues, 'likelihood');
        const score = getScore(formValues);
        const residualScore = getResidualScore(formValues.treatmentGroup);
        const residualImpact = formValues.treatmentGroup
            ? getResidualValue(formValues.treatmentGroup, 'residualImpact')
            : undefined;
        const residualLikelihood = formValues.treatmentGroup
            ? getResidualValue(formValues.treatmentGroup, 'residualLikelihood')
            : undefined;
        const treatmentPlan =
            formValues.treatmentGroup?.treatment?.value ?? 'UNTREATED';
        const treatmentDetails =
            formValues.treatmentGroup?.treatmentPlan ?? undefined;
        const reviewers = formValues.treatmentGroup?.reviewers
            ? getUniqueIdsList(formValues.treatmentGroup.reviewers)
            : undefined;
        const anticipatedCompletionDate = formValues.treatmentGroup
            ?.anticipatedCompletionDate
            ? formatDate(
                  'timestamp',
                  formValues.treatmentGroup.anticipatedCompletionDate,
              )
            : undefined;
        const completionDate = formValues.treatmentGroup?.completedDate
            ? formatDate('timestamp', formValues.treatmentGroup.completedDate)
            : undefined;
        const vendorId =
            formValues.riskSourceGroup?.riskSource === 'EXTERNAL'
                ? Number(formValues.vendorSource?.vendor?.id)
                : undefined;

        this.mutatedRiskDetails = zRiskRequestDto.parse({
            type,
            status,
            identifiedAt,
            vendorId,
            categories,
            owners,
            impact,
            likelihood,
            score,
            residualScore,
            residualImpact,
            residualLikelihood,
            treatmentPlan,
            treatmentDetails,
            controls,
            title: formValues.riskDetailsGroup?.title,
            description: formValues.riskDetailsGroup?.description,
            reviewers,
            anticipatedCompletionDate,
            completionDate,
        });
    };

    createNewRisk = () => {
        if (this.uploadStatus === 'IN_PROGRESS') {
            return;
        }
        this.uploadStatus = 'IN_PROGRESS';

        this.createMutatedRiskDetails();

        if (isNil(this.risk)) {
            this.createRiskMutation.mutate({
                body: {
                    ...toJS(this.mutatedRiskDetails),
                    title: this.mutatedRiskDetails?.title || '',
                    description: this.mutatedRiskDetails?.description || '',
                    treatmentPlan:
                        this.mutatedRiskDetails?.treatmentPlan || 'UNTREATED',
                    // @ts-expect-error -- There is a mismatch between the types of the SDK and the API
                    customFieldSubmissions:
                        sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                            this.customFields,
                            this.availableCustomFields,
                        )?.customFieldSubmissions ?? [],
                },
            });
        }

        when(
            () => !this.createRiskMutation.isPending,
            () => {
                if (this.createRiskMutation.hasError) {
                    this.uploadStatus = 'ERROR';

                    snackbarController.addSnackbar({
                        id: 'create-risk-error',
                        props: {
                            title: t`Error creating risk data`,
                            description: t`An error occurred while creating the risk data. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                const { response } = this.createRiskMutation;

                this.risk = response;

                this.mayUploadRiskFiles(response);
            },
        );

        when(
            () => this.uploadStatus === 'SUCCESS',
            () => {
                snackbarController.addSnackbar({
                    id: 'create-risk-success',
                    props: {
                        title: t`Risk created`,
                        description: t`The risk was created successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };

    mayUploadRiskFiles = (response: RiskResponseDto | null) => {
        const formValues = toJS(this.createRiskWizardData) ?? {};

        if (
            !isEmpty(formValues.riskDetailsGroup?.documents) &&
            response?.riskId
        ) {
            if (Array.isArray(formValues.riskDetailsGroup?.documents)) {
                this.uploadRiskFiles(
                    response.riskId,
                    formValues.riskDetailsGroup.documents as unknown as File[],
                );
            }
        } else {
            this.uploadStatus = 'SUCCESS';
        }
    };

    uploadRiskFiles = (riskId: string, files: File[]) => {
        this.vendorRiskUploadDocumentMutation.mutate({
            body: {
                files,
            },
            path: { risk_id: riskId },
        });

        when(
            () => !this.vendorRiskUploadDocumentMutation.isPending,
            () => {
                if (this.vendorRiskUploadDocumentMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'create-risk-upload-error',
                        props: {
                            title: t`Error uploading risk supporting documents`,
                            description: t`An error occurred while uploading the supporting documents. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }
                // Setting status to success because the Risk was created in spite of the error
                if (this.uploadStatus !== 'SUCCESS') {
                    this.uploadStatus = 'SUCCESS';
                }
            },
        );
    };

    get isCreating(): boolean {
        return this.createRiskMutation.isPending;
    }

    get hasError(): Error | null {
        return this.createRiskMutation.error;
    }

    createRisk = (data: RiskRequestDto) => {
        this.createRiskMutation.mutate({
            body: data,
        });
    };
}

export const sharedCreateRiskMutationController =
    new CreateRiskMutationController();
