import { isError } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    riskManagementControllerCreateCategoryMutation,
    riskManagementControllerDeleteRiskCategoriesMutation,
    riskManagementControllerGetRiskCategoriesInfiniteOptions,
    riskManagementControllerGetRiskCategoriesOptions,
} from '@globals/api-sdk/queries';
import type {
    RiskCategoryResponseDto,
    RiskManagementControllerGetRiskCategoriesData,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    ObservedQuery,
    runInAction,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { MAX_CATEGORY_NAME_LENGTH } from '../constants/risk-categories.constants';

export class RiskCategoriesController {
    _pendingCategory: {
        name: string;
        onSuccess?: (newCategory: ListBoxItemData) => void;
    } | null = null;

    newCategoryName = '';
    validationError: string | null = null;
    deletingCategoryId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    categoriesInfiniteQuery = new ObservedInfiniteQuery(
        riskManagementControllerGetRiskCategoriesInfiniteOptions,
    );

    categoriesPaginatedQuery = new ObservedQuery(
        riskManagementControllerGetRiskCategoriesOptions,
    );

    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    createCategoryMutation = new ObservedMutation(
        riskManagementControllerCreateCategoryMutation,
        {
            onSuccess: () => {
                this.categoriesInfiniteQuery.invalidate();
                this.categoriesPaginatedQuery.invalidate();

                // Clear form state
                runInAction(() => {
                    this.newCategoryName = '';
                    this.validationError = null;
                });

                snackbarController.addSnackbar({
                    id: 'risk-category-created',
                    props: {
                        title: t`Category created`,
                        description: t`The new risk category has been created successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to create risk category',
                    additionalInfo: {
                        action: 'createCategory',
                        categoryName: this.newCategoryName,
                    },
                    errorObject: {
                        message: error.message || 'Unknown error',
                        statusCode: error.statusCode || 'unknown',
                    },
                });

                snackbarController.addSnackbar({
                    id: 'category-api-error',
                    props: {
                        title: t`Error creating category`,
                        description:
                            error.message ||
                            t`Failed to create the category. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    deleteCategoriesMutation = new ObservedMutation(
        riskManagementControllerDeleteRiskCategoriesMutation,
        {
            onSuccess: () => {
                runInAction(() => {
                    this.deletingCategoryId = null;
                });

                this.categoriesInfiniteQuery.invalidate();
                this.categoriesPaginatedQuery.invalidate();

                snackbarController.addSnackbar({
                    id: 'risk-category-deleted',
                    props: {
                        title: t`Category deleted`,
                        description: t`The risk category has been deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: (error) => {
                runInAction(() => {
                    this.deletingCategoryId = null;
                });

                // Step 1: Log for engineering
                logger.error({
                    message: 'Failed to delete risk category',
                    additionalInfo: {
                        action: 'deleteCategory',
                    },
                    errorObject: {
                        message: error.message || 'Unknown error',
                        statusCode: error.statusCode || 'unknown',
                    },
                });

                // Step 2: Help the customer recover
                snackbarController.addSnackbar({
                    id: 'delete-category-error',
                    props: {
                        title: t`Error deleting category`,
                        description:
                            error.message ||
                            t`Failed to delete the category. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get hasNextPage(): boolean {
        return this.categoriesInfiniteQuery.hasNextPage;
    }

    get categories(): RiskCategoryResponseDto[] {
        return (
            this.categoriesInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasError(): boolean {
        return this.categoriesInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.categoriesInfiniteQuery.isLoading;
    }

    loadNextPage = (): void => {
        this.categoriesInfiniteQuery.nextPage();
    };

    loadCategories = (searchTerm?: string): void => {
        const query: RiskManagementControllerGetRiskCategoriesData['query'] = {
            page: 1,
        };

        if (searchTerm) {
            query.q = searchTerm;
        }

        this.categoriesInfiniteQuery.load({
            query,
        });
    };

    onFetchCategories = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    } = {}): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }
        this.loadCategories(search?.trim());
    };

    initialize = (): void => {
        // Load categories if not already loaded
        if (!this.categoriesInfiniteQuery.data) {
            this.loadCategories();
        }
    };

    /**
     * Paginated query methods for PaginationControls.
     */
    loadCategoriesPaginated = (
        page: number,
        limit: number,
        searchTerm?: string,
    ): void => {
        const query: RiskManagementControllerGetRiskCategoriesData['query'] = {
            page,
            limit,
        };

        if (searchTerm) {
            query.q = searchTerm;
        }

        this.categoriesPaginatedQuery.load({
            query,
        });
    };

    get paginatedCategories(): RiskCategoryResponseDto[] {
        return this.categoriesPaginatedQuery.data?.data ?? [];
    }

    get paginatedTotal(): number {
        return this.categoriesPaginatedQuery.data?.total ?? 0;
    }

    get isPaginatedLoading(): boolean {
        return this.categoriesPaginatedQuery.isLoading;
    }

    get hasPaginatedError(): boolean {
        return this.categoriesPaginatedQuery.hasError;
    }

    addCategory = async (categoryName: string): Promise<void> => {
        // Validate input
        if (!categoryName.trim()) {
            logger.error({
                message: 'Failed to add risk category - validation error',
                additionalInfo: {
                    action: 'addCategory',
                    categoryName: categoryName || 'empty',
                    validationError: 'Category name is required',
                },
            });

            snackbarController.addSnackbar({
                id: 'add-category-validation-error',
                props: {
                    title: t`Invalid category name`,
                    description: t`Category name cannot be empty. Please enter a valid name.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        try {
            await this.createCategoryMutation.mutateAsync({
                body: { name: categoryName.trim() },
            });
        } catch (error) {
            logger.error({
                message: 'Failed to add risk category',
                additionalInfo: {
                    action: 'addCategory',
                    categoryName: categoryName || 'empty',
                },
                errorObject: {
                    message: isError(error) ? error.message : 'Unknown error',
                    statusCode: 'unknown',
                },
            });
            throw error; // Re-throw so the caller can handle it
        }
    };

    get isCreatingCategory(): boolean {
        return this.createCategoryMutation.isPending;
    }

    get isDeletingCategory(): boolean {
        return this.deleteCategoriesMutation.isPending;
    }

    isDeletingSpecificCategory = (categoryId: string): boolean => {
        return (
            this.deletingCategoryId === categoryId &&
            this.deleteCategoriesMutation.isPending
        );
    };

    validateCategoryName = (name: string): string | null => {
        if (!name.trim()) {
            return t`Category name is required`;
        }

        if (name.length > MAX_CATEGORY_NAME_LENGTH) {
            return t`Category name must be ${MAX_CATEGORY_NAME_LENGTH} characters or less`;
        }

        return null;
    };

    updateNewCategoryName = (name: string): void => {
        runInAction(() => {
            this.newCategoryName = name;
            // Clear validation error when user starts typing
            if (this.validationError) {
                this.validationError = null;
            }
        });
    };

    addCategoryWithValidation = (): void => {
        runInAction(() => {
            this.validationError = this.validateCategoryName(
                this.newCategoryName,
            );
        });

        if (this.validationError) {
            return;
        }

        this.addCategory(this.newCategoryName.trim()).catch((error) => {
            logger.error(`Failed to create category: ${error}`);
        });
    };

    deleteCategory = (categoryId: string): void => {
        const categoryIdNumber = Number(categoryId);

        if (isNaN(categoryIdNumber)) {
            snackbarController.addSnackbar({
                id: 'invalid-category-id',
                props: {
                    title: t`Invalid category`,
                    description: t`Unable to delete category. Invalid category ID.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        runInAction(() => {
            this.deletingCategoryId = categoryId;
        });

        this.deleteCategoriesMutation.mutate({
            body: { ids: [categoryIdNumber] },
        });
    };

    deleteCategoryWithConfirmation = (
        categoryId: string,
        categoryName: string,
    ): void => {
        openConfirmationModal({
            title: t`Delete category`,
            body: t`You’re about to delete ${categoryName}. This will remove it from all associated risks. This action cannot be undone.`,
            confirmText: t`Delete category`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.deleteCategory(categoryId);
                closeConfirmationModal();
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };
}

export const sharedRiskCategoriesController = new RiskCategoriesController();
