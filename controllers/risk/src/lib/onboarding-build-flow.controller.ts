import { isArray, isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import type { RisksResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { plural, t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';
import type { FormValues } from '@ui/forms';
import type { RiskGroup } from '@views/risk-register-onboarding-build';
import { sharedRiskLibraryCopyController } from './risk-library-copy-mutation.controller';
import { sharedRiskManagementBulkActionsMutationController } from './risk-management-bulk-actions-mutation.controller';
import { sharedRiskManagementController } from './risk-management-controller';

class OnboardingBuildFlowController {
    riskGroups: Set<RiskGroup> = new Set();

    constructor() {
        makeAutoObservable(this);
    }

    addRiskGroup = (riskGroup: RiskGroup | RiskGroup[]): void => {
        if (isArray(riskGroup)) {
            riskGroup.forEach((group) => {
                this.riskGroups.add(group);
            });

            return;
        }

        this.riskGroups.add(riskGroup);
    };

    removeRiskGroups = (riskGroup?: RiskGroup): void => {
        switch (riskGroup) {
            case 'AI_DEVELOPMENT':
            case 'AI_USE': {
                this.riskGroups.delete('AI_DEVELOPMENT');
                this.riskGroups.delete('AI_USE');

                return;
            }
            default: {
                if (riskGroup) {
                    this.riskGroups.delete(riskGroup);

                    return;
                }
            }
        }
    };

    clearRiskGroups = (): void => {
        this.riskGroups.clear();
    };

    get onboardingResponseIsPending(): boolean {
        const { isScalingRiskFoundationEnabled } = sharedFeatureAccessModel;

        const { isPending } = isScalingRiskFoundationEnabled
            ? sharedRiskLibraryCopyController
            : sharedRiskManagementBulkActionsMutationController;

        return isPending;
    }

    get onboardingResponseHasError(): boolean {
        const { isScalingRiskFoundationEnabled } = sharedFeatureAccessModel;
        const { hasError } = isScalingRiskFoundationEnabled
            ? sharedRiskLibraryCopyController
            : sharedRiskManagementBulkActionsMutationController;

        return hasError;
    }

    get onboardingResponse(): RisksResponseDto | null {
        const { isScalingRiskFoundationEnabled } = sharedFeatureAccessModel;

        const { response } = isScalingRiskFoundationEnabled
            ? sharedRiskLibraryCopyController
            : sharedRiskManagementBulkActionsMutationController;

        return response;
    }

    handleOnStepSubmit = (riskGroup: RiskGroup, formData: FormValues): void => {
        switch (riskGroup) {
            case 'AI_DEVELOPMENT':
            case 'AI_USE': {
                this.removeRiskGroups('AI_DEVELOPMENT');
                this.removeRiskGroups('AI_USE');

                if (!isEmpty(formData.riskGroups)) {
                    this.addRiskGroup(formData.riskGroups as RiskGroup[]);
                }

                return;
            }
            case 'DEVICE_DELIVERY': {
                if (this.onboardingResponseIsPending) {
                    return;
                }

                this.removeRiskGroups('DEVICE_DELIVERY');

                if (!isEmpty(formData.riskGroups)) {
                    this.addRiskGroup(formData.riskGroups as RiskGroup);
                }

                this.handleBuildRiskRegister();

                return;
            }
            case 'PHYSICAL_SITE':
            case 'CLOUD_ENVIRONMENT':
            case 'REGULATORY_REQUIREMENTS':
            case 'SOFTWARE_DEVELOPMENT':
            case 'UNSECURE_DEVICES': {
                this.removeRiskGroups(riskGroup);

                if (!isEmpty(formData.riskGroups)) {
                    this.addRiskGroup(formData.riskGroups as RiskGroup);
                }

                return;
            }
            default: {
                throw new Error('Unknown risk group');
            }
        }
    };

    handleBuildRiskRegister = (): void => {
        const { isScalingRiskFoundationEnabled } = sharedFeatureAccessModel;
        const { copyRisksFromLibrary } = sharedRiskLibraryCopyController;
        const { bulkUpdateRisks } =
            sharedRiskManagementBulkActionsMutationController;

        const riskGroups = [...this.riskGroups];

        if (isEmpty(riskGroups)) {
            this.clearRiskGroups();
            sharedProgrammaticNavigationController.navigateTo(
                `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks`,
            );

            return;
        }

        if (isScalingRiskFoundationEnabled) {
            // @ts-expect-error -- the API docs are wrong, we can omit the other props
            copyRisksFromLibrary({
                bulkActionType: 'COPY_BY_GROUPS',
                riskGroups,
            });
        } else {
            // @ts-expect-error -- the API docs are wrong, we can omit the other props
            bulkUpdateRisks({
                bulkActionType: 'APPLICABLE_BY_RISK_GROUP',
                riskGroups,
            });
        }

        when(
            () => !this.onboardingResponseIsPending,
            () => {
                if (this.onboardingResponseHasError) {
                    snackbarController.addSnackbar({
                        id: 'build-risk-register-error',
                        props: {
                            title: t`Unable to move risks to register`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const risksMoved = this.onboardingResponse?.risks.length ?? 0;

                snackbarController.addSnackbar({
                    id: 'build-risk-register-success',
                    props: {
                        title: plural(risksMoved, {
                            one: '1 risk has been moved to the register.',
                            other: '# risks have been moved to the register.',
                        }),
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                this.clearRiskGroups();
                sharedRiskManagementController.riskManagementStatisticsQuery.invalidate();
                sharedRiskManagementController.riskManagementListQuery.invalidate();

                sharedProgrammaticNavigationController.navigateTo(
                    `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks`,
                );
            },
        );
    };
}

export const sharedOnboardingBuildFlowController =
    new OnboardingBuildFlowController();
