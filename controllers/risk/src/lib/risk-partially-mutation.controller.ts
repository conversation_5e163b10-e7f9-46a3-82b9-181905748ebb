import { sharedControlRisksController } from '@controllers/controls';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { snackbarController } from '@controllers/snackbar';
import { riskManagementControllerUpdateRiskPartiallyMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';

/**
 * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
 */
type UpdateRiskPartiallyBody = NonNullable<
    NonNullable<
        Parameters<
            typeof riskManagementControllerUpdateRiskPartiallyMutation
        >[0]
    >['body']
>;

export class RiskPartiallyMutationController {
    riskId: string | null = null;
    lastSuccessfulRiskId: string | null = null;
    currentSection: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * TODO: migrate to RiskManagementV2 endpoint https://drata.atlassian.net/browse/ENG-73884.
     */
    updateRiskPartiallyMutation = new ObservedMutation(
        riskManagementControllerUpdateRiskPartiallyMutation,
        {
            onSuccess: (data) => {
                this.riskId = null;
                this.currentSection = null;
                sharedControlRisksController.setControlRisksListQueryData(data);
            },
        },
    );

    get isPending(): boolean {
        return this.updateRiskPartiallyMutation.isPending;
    }

    get hasError(): boolean {
        return this.updateRiskPartiallyMutation.hasError;
    }

    get mutationError(): Error | null {
        return this.updateRiskPartiallyMutation.error;
    }

    isSectionPending(section: string): boolean {
        return (
            this.updateRiskPartiallyMutation.isPending &&
            this.currentSection === section
        );
    }

    hasSectionError(section: string): boolean {
        return (
            this.updateRiskPartiallyMutation.hasError &&
            this.currentSection === section
        );
    }

    updateRiskPartially = (
        riskId: string,
        requestBody: UpdateRiskPartiallyBody,
        section?: string,
    ): void => {
        this.riskId = riskId;
        this.currentSection = section || null;

        this.updateRiskPartiallyMutation.mutate({
            path: { risk_id: riskId },
            body: requestBody,
        });

        when(
            () => !this.updateRiskPartiallyMutation.isPending,
            () => {
                if (!this.updateRiskPartiallyMutation.hasError) {
                    return;
                }

                logger.error({
                    message: 'Failed to update risk',
                    additionalInfo: {
                        error: this.updateRiskPartiallyMutation.error,
                        riskId,
                        requestBody,
                    },
                });
            },
        );
    };

    updateRiskPartiallyDetails = (
        riskId: string,
        requestBody: UpdateRiskPartiallyBody,
        section?: string,
    ): void => {
        this.riskId = riskId;
        this.currentSection = section || null;

        this.updateRiskPartiallyMutation.mutate({
            path: { risk_id: riskId },
            body: requestBody,
        });

        when(
            () => !this.updateRiskPartiallyMutation.isPending,
            () => {
                if (this.updateRiskPartiallyMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'risk-details-update-error',
                        props: {
                            title: t`Failed to update risk details`,
                            description: t`An error occurred while updating the risk details. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    sharedRiskDetailsController.loadRiskDetails(riskId);
                    snackbarController.addSnackbar({
                        id: 'risk-details-update-success',
                        props: {
                            title: t`Risk details updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };
}

export const sharedRiskPartiallyMutationController =
    new RiskPartiallyMutationController();
