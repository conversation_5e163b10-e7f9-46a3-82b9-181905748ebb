import { isEmpty } from 'lodash-es';
import {
    getEvidenceTypeApiValue,
    sharedAuditHubEvidenceController,
} from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { auditHubControllerGetAuditEvidenceDownloadOptions } from '@globals/api-sdk/queries';
import type {
    AuditHubEvidenceResponseDto,
    AuditHubEvidenceTypeEnum,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import {
    downloadBase64AsFile,
    downloadFileFromSignedUrl,
} from '@helpers/download-file';

class AuditHubEvidenceViewerController {
    selectedEvidence: {
        evidence: AuditHubEvidenceResponseDto;
        rowIndex: number;
    } | null = null;

    enhancedEvidence: AuditHubEvidenceResponseDto | null = null;

    evidenceDownloadQuery = new ObservedQuery(
        auditHubControllerGetAuditEvidenceDownloadOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isGeneratingEvidence(): boolean {
        return this.evidenceDownloadQuery.isLoading;
    }

    get evidenceDocument(): AuditHubEvidenceResponseDto | null {
        if (!this.selectedEvidence) {
            return null;
        }

        const { evidence } = this.selectedEvidence;

        return this.resolveEvidenceDocument(evidence);
    }

    private resolveEvidenceDocument(
        evidence: AuditHubEvidenceResponseDto,
    ): AuditHubEvidenceResponseDto {
        if (this.hasEvidenceData(evidence)) {
            return evidence;
        }

        if (this.hasEnhancedEvidence(evidence) && this.enhancedEvidence) {
            return this.enhancedEvidence;
        }

        return evidence;
    }

    private hasEvidenceData(evidence: AuditHubEvidenceResponseDto): boolean {
        return Boolean(
            evidence.signedUrl || (evidence.fileData && evidence.fileName),
        );
    }

    private hasEnhancedEvidence(
        evidence: AuditHubEvidenceResponseDto,
    ): boolean {
        return Boolean(
            this.enhancedEvidence &&
                this.isSameEvidence(this.enhancedEvidence, evidence),
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        if (!this.selectedEvidence) {
            return [];
        }

        const { artifact, type, date, controlCodes } =
            this.selectedEvidence.evidence;

        return [
            this.createArtifactKeyValuePair(artifact),
            this.createTypeKeyValuePair(type),
            this.createControlCodesKeyValuePair(controlCodes),
            this.createDateKeyValuePair(date),
        ];
    }

    private createArtifactKeyValuePair(
        artifact: string | undefined,
    ): KeyValuePairProps {
        return {
            id: 'evidence-artifact',
            label: t`Artifact`,
            value: artifact ?? '',
            showEmptyValue: !artifact,
        };
    }

    private createTypeKeyValuePair(type: string): KeyValuePairProps {
        return {
            id: 'evidence-type',
            label: t`Type`,
            value: type,
        };
    }

    private createControlCodesKeyValuePair(
        controlCodes: string[] | undefined,
    ): KeyValuePairProps {
        const codes = controlCodes ?? [];

        return {
            id: 'evidence-control-codes',
            label: t`Linked Controls`,
            type: 'TAG',
            visibleItemsLimit: 2,
            value: isEmpty(codes)
                ? []
                : codes.map((code: string) => ({
                      label: code,
                      colorScheme: 'neutral',
                      type: 'tag',
                  })),
            showEmptyValue: isEmpty(codes),
        };
    }

    private createDateKeyValuePair(
        date: string | undefined,
    ): KeyValuePairProps {
        return {
            id: 'evidence-date',
            label: t`Creation Date`,
            value: date ? formatDate('table', date) : '',
            showEmptyValue: !date,
        };
    }

    setSelectedEvidence = (
        evidence: AuditHubEvidenceResponseDto,
        rowIndex: number,
    ): void => {
        this.selectedEvidence = { evidence, rowIndex };

        this.ensureEvidenceDataAvailable();
    };

    ensureEvidenceDataAvailable = (): void => {
        if (!this.selectedEvidence) {
            return;
        }

        const { evidence } = this.selectedEvidence;

        // If evidence already has data or we have enhanced evidence, no need to generate
        if (
            this.hasEvidenceData(evidence) ||
            this.hasEnhancedEvidence(evidence)
        ) {
            return;
        }

        this.generateEvidenceData(evidence);
    };

    get currentEvidenceIndex(): number {
        return this.selectedEvidence?.rowIndex ?? -1;
    }

    get currentPosition(): number {
        return this.currentEvidenceIndex + 1;
    }

    get totalCount(): number {
        return sharedAuditHubEvidenceController.auditCustomerRequestEvidences
            .length;
    }

    get canNavigatePrevious(): boolean {
        return this.currentEvidenceIndex > 0;
    }

    get canNavigateNext(): boolean {
        const evidenceList =
            sharedAuditHubEvidenceController.auditCustomerRequestEvidences;

        return (
            this.currentEvidenceIndex >= 0 &&
            this.currentEvidenceIndex < evidenceList.length - 1
        );
    }

    navigateToPrevious = (): void => {
        if (!this.canNavigatePrevious) {
            return;
        }

        const previousIndex = this.currentEvidenceIndex - 1;

        this.navigateToIndex(previousIndex);
    };

    navigateToNext = (): void => {
        if (!this.canNavigateNext) {
            return;
        }

        const nextIndex = this.currentEvidenceIndex + 1;

        this.navigateToIndex(nextIndex);
    };

    private navigateToIndex(index: number): void {
        const evidenceList =
            sharedAuditHubEvidenceController.auditCustomerRequestEvidences;

        // If no evidence, do nothing
        if (isEmpty(evidenceList)) {
            return;
        }

        // If index is out of bounds, do nothing
        if (index < 0 || index >= evidenceList.length) {
            return;
        }

        const evidence = evidenceList[index];

        this.setSelectedEvidence(evidence, index);
    }

    downloadSelectedEvidence = (): void => {
        if (!this.selectedEvidence) {
            return;
        }

        const { evidence } = this.selectedEvidence;

        if (
            this.tryDownloadFromSignedUrl(evidence) ||
            this.tryDownloadFromFileData(evidence)
        ) {
            return;
        }

        this.generateAndDownloadEvidence(evidence);
    };

    generateEvidenceForViewing = (): void => {
        this.ensureEvidenceDataAvailable();
    };

    private tryDownloadFromSignedUrl(
        evidence: AuditHubEvidenceResponseDto,
    ): boolean {
        if (evidence.signedUrl) {
            downloadFileFromSignedUrl(evidence.signedUrl);

            return true;
        }

        return false;
    }

    private tryDownloadFromFileData(
        evidence: AuditHubEvidenceResponseDto,
    ): boolean {
        if (evidence.fileData && evidence.fileName) {
            const mimeType = evidence.fileType ?? 'application/octet-stream';

            downloadBase64AsFile(
                evidence.fileData,
                evidence.fileName,
                mimeType,
            );

            return true;
        }

        return false;
    }

    private generateAndDownloadEvidence(
        evidence: AuditHubEvidenceResponseDto,
    ): void {
        sharedAuditHubEvidenceController.generateAndDownloadEvidence(evidence);
    }

    clearSelectedEvidence = (): void => {
        this.selectedEvidence = null;
        this.enhancedEvidence = null;
    };

    isSameEvidence = (
        evidence1: AuditHubEvidenceResponseDto,
        evidence2: AuditHubEvidenceResponseDto,
    ): boolean => {
        return (
            evidence1.name === evidence2.name &&
            evidence1.artifact === evidence2.artifact &&
            evidence1.type === evidence2.type
        );
    };

    generateEvidenceData = (evidence: AuditHubEvidenceResponseDto): void => {
        const { getRequestId: requestId, auditId } =
            sharedCustomerRequestDetailsController;

        if (!auditId || !requestId) {
            return;
        }

        // Don't generate if already in progress or if no control codes
        if (
            this.evidenceDownloadQuery.isLoading ||
            isEmpty(evidence.controlCodes)
        ) {
            return;
        }

        this.evidenceDownloadQuery.load({
            path: {
                auditId: String(auditId),
                customerRequestId: Number(requestId),
            },
            query: {
                type: getEvidenceTypeApiValue(
                    evidence.type,
                ) as AuditHubEvidenceTypeEnum,
                artifact: evidence.artifact,
                controlCode: evidence.controlCodes[0],
            },
        });

        when(
            () => !this.evidenceDownloadQuery.isLoading,
            () => {
                if (this.evidenceDownloadQuery.hasError) {
                    snackbarController.addSnackbar({
                        id: 'evidence-generation-error',
                        props: {
                            title: t`Failed to generate evidence`,
                            description: t`An error occurred while generating the evidence. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (!this.evidenceDownloadQuery.data) {
                    snackbarController.addSnackbar({
                        id: 'evidence-generation-no-data',
                        props: {
                            title: t`No evidence data available`,
                            description: t`Unable to download evidence. No download data was returned.`,
                            severity: 'warning',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const downloadData = this.evidenceDownloadQuery.data;

                this.enhancedEvidence = {
                    ...evidence,
                    fileData: downloadData.fileData ?? evidence.fileData,
                    fileName: downloadData.fileName ?? evidence.fileName,
                    fileType: downloadData.fileType ?? evidence.fileType,
                    signedUrl: downloadData.signedUrl ?? evidence.signedUrl,
                };
            },
        );
    };
}

export const sharedAuditHubEvidenceViewerController =
    new AuditHubEvidenceViewerController();
