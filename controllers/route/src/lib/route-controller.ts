import { isEmpty } from 'lodash-es';
import type { ErrorContent } from '@app/types';
import type { PageHeaderOverrides } from '@controllers/page-header';
import type { IconName } from '@cosmos/components/icon';
import type { MetadataProps } from '@cosmos/components/metadata';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { Params, UIMatch } from '@remix-run/react';
import type { NavigationItem } from '@ui/page-content';

interface Topic {
    id?: string;
    topicPath: string;
    label: string;
    icon?: string;
    iconName?: IconName;
    metadata?: MetadataProps;
}

interface Domain {
    label: string;
    hideLabel?: boolean;
    topicsOrder: string[];
    topics: Record<string, Topic>;
    icon?: string;
}

interface TopicsNav {
    id?: string;
    title: string;
    domainsOrder: string[];
    domains: Record<string, Domain>;
}

export interface Tab {
    id?: string;
    topicPath: string;
    label: string;
    iconName?: IconName;
    metadata?: MetadataProps;
}

export interface DomainsNav {
    id: string;
    title: string;
    domainsOrder: string[];
    domains: Record<string, Domain>;
}

export interface DomainsNavModel {
    navigation: DomainsNav;
}

interface ContentNavOverrides {
    tabs: Tab[];
}

export type UtilitiesName =
    // Notes
    | 'notes_for_events'
    | 'notes_for_access_review_active_period_user'
    | 'notes_for_access_review_active_application'
    | 'notes_for_completed_application_on_completed_review'
    | 'notes_for_controls'
    | 'notes_for_customer_requests'
    | 'notes_for_risk_management'
    | 'notes_for_monitoring_tests'
    | 'notes_for_vendor_observations'
    // Tasks
    | 'tasks'
    | 'tasks_for_risks'
    | 'tasks_for_controls'
    // Ticketing
    | 'tickets_for_controls'
    | 'tickets_for_monitors'
    | 'tickets_for_codebase_monitors'
    | 'tickets_for_risk_management'
    | 'tickets_for_access_review_active_period_user'
    // VRM Agent
    | 'vrm_agent_criteria'
    | 'vrm_agent_summary'
    | 'vrm_agent_assessment'
    // Resource Guide
    | 'resource_guide_for_monitoring_tests';

interface UtilitiesOverrides {
    utilitiesList: UtilitiesName[];
}

interface LayoutOverrides {
    centered?: boolean;
}

interface RouteContext {
    params: Params;
}

interface SubdomainConfig {
    id: string;
    userPart: string;
    authRoute: string;
    errorContent: ErrorContent;
}

interface RouteData {
    pageHeader?: PageHeaderOverrides;
    tabs?: Tab[];
    contentNav?: ContentNavOverrides;
    topicsNav?: TopicsNav | { topicsNav: TopicsNav };
    domainsNav?: DomainsNavModel;
    utilities?: UtilitiesOverrides;
    layout?: LayoutOverrides;
    subdomainConfig?: SubdomainConfig;
}

class RouteController {
    tabs: Tab[] | null = null;
    pageHeader: PageHeaderOverrides | null = null;
    contentNav: ContentNavOverrides | null = null;
    topicsNav: TopicsNav | null = null;
    domainsNav: DomainsNavModel | null = null;
    utilities: UtilitiesOverrides | null = null;
    layout: LayoutOverrides | null = null;
    currentParams: Params = {};
    userPartOfUrl = '';
    isSubNavMinimized = false;

    constructor() {
        makeAutoObservable(this);
    }

    matches: UIMatch[] = [];
    simpleMergedMatches: Record<string, unknown> = {};

    setMatches(matches: UIMatch[]): void {
        this.matches = matches;

        // eslint-disable-next-line unicorn/no-array-reduce -- Best choice of method
        const simpleMergedMatches = matches.reduce<Record<string, unknown>>(
            (acc, match) => {
                if (!match.data) {
                    return acc;
                }

                const {
                    pageHeader: matchPageHeader,
                    tabs: matchTabs,
                    contentNav: matchContentNav,
                    topicsNav: matchTopicsNav,
                    domainsNav: matchDomainsNav,
                    utilities: matchUtilities,
                    layout: matchLayout,
                } = match.data as {
                    pageHeader?: PageHeaderOverrides;

                    tabs?: Tab[];
                    contentNav?: ContentNavOverrides;
                    topicsNav?: TopicsNav | { topicsNav: TopicsNav };
                    domainsNav?: DomainsNavModel;
                    utilities?: UtilitiesOverrides;
                    layout?: LayoutOverrides;
                };

                return {
                    ...acc,
                    ...(matchPageHeader && { pageHeader: matchPageHeader }),
                    ...(matchTabs && { tabs: matchTabs }),
                    ...(matchContentNav && { contentNav: matchContentNav }),
                    ...(matchTopicsNav && {
                        topicsNav:
                            'topicsNav' in matchTopicsNav
                                ? matchTopicsNav.topicsNav
                                : matchTopicsNav,
                    }),
                    ...(matchDomainsNav && { domainsNav: matchDomainsNav }),
                    ...(matchUtilities && { utilities: matchUtilities }),
                    ...(matchLayout && { layout: matchLayout }),
                } as Record<string, unknown>;
            },
            {},
        );

        this.simpleMergedMatches = simpleMergedMatches;
        /**
         * TODO: refactor contentNav/tabs to topics.
         */
        this.tabs = simpleMergedMatches.tabs as Tab[];
        this.pageHeader = simpleMergedMatches.pageHeader as PageHeaderOverrides;
        /**
         * TODO: refactor contentNav/tabs to topics.
         */
        this.contentNav = simpleMergedMatches.contentNav as ContentNavOverrides;
        this.topicsNav = simpleMergedMatches.topicsNav as TopicsNav;
        this.domainsNav = simpleMergedMatches.domainsNav as DomainsNavModel;
        this.utilities = simpleMergedMatches.utilities as UtilitiesOverrides;
        this.layout = simpleMergedMatches.layout as LayoutOverrides;

        // TODO there should be an observable way to do this
        this.setUserPartOfUrl();
    }

    setRouteContext({ params }: RouteContext): void {
        this.currentParams = params;
    }

    get currentSubdomainConfig(): SubdomainConfig | null {
        const matchWithSubdomainConfig: UIMatch | undefined = this.matches.find(
            (match: UIMatch) => {
                const { data } = match as { data?: RouteData };

                return data?.subdomainConfig?.userPart;
            },
        );

        const { subdomainConfig } =
            (matchWithSubdomainConfig as { data?: RouteData } | undefined)
                ?.data ?? {};

        return subdomainConfig ?? null;
    }

    setUserPartOfUrl(): void {
        this.userPartOfUrl = this.currentSubdomainConfig?.userPart ?? '';
    }

    setMinimizeSubNav(minimizeSubnav: boolean): void {
        this.isSubNavMinimized = minimizeSubnav;
    }

    get contentNavItems(): NavigationItem[] {
        const topics = this.contentNav?.tabs ?? this.tabs;

        if (!topics || isEmpty(topics)) {
            return [];
        }

        return this.buildContentNavItems({
            topics,
            params: this.currentParams,
        });
    }

    buildContentNavItems({
        topics,
        params,
    }: {
        topics: Topic[] | null;
        params: Params;
    }): NavigationItem[] {
        if (!topics || isEmpty(topics)) {
            return [];
        }

        return topics.map(({ topicPath, label, iconName, metadata }: Topic) => {
            const topicPathWithParamReplacement = topicPath
                .split('/')
                .map((pathPart) => {
                    if (pathPart.startsWith(':')) {
                        const paramId = pathPart.split(':')[1];

                        return params[paramId] || pathPart;
                    }

                    return pathPart;
                })
                .join('/');

            return {
                id: topicPath,
                props: {
                    href: `${this.userPartOfUrl}/${topicPathWithParamReplacement}`,
                    label,
                    iconName,
                    metadata,
                },
            };
        });
    }

    getAuthRoute(): string {
        const subdomainConfig = this.currentSubdomainConfig;

        return subdomainConfig?.authRoute ?? '/auth/login';
    }

    get errorContent(): ErrorContent {
        const defaultErrorContent: ErrorContent = {
            supportEmail: '<EMAIL>',
            returnHomeLabel: t`Return home`,
            userType: 'customer',
        };

        return this.currentSubdomainConfig?.errorContent ?? defaultErrorContent;
    }
}

export const routeController = new RouteController();
