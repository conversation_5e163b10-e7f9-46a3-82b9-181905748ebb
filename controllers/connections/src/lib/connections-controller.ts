import { isEmpty, isNil } from 'lodash-es';
import {
    connectionsControllerGetConnectionSettingsOptions,
    connectionsControllerGetConnectionsListByClientTypeOptions,
    connectionsControllerGetControlTestInstancesByClientTypeOptions,
    v2ConnectionsControllerGetConnectionsOptions,
    v2ConnectionsControllerGetSetupDetailsOptions,
} from '@globals/api-sdk/queries';
import type {
    ClientTypeEnum,
    ConnectionMetadataResponseDto,
    ConnectionResponseDto,
    ConnectionSetupDetailsResponseDto,
    InfrastructureConnectionResponseDto,
    TicketingConnectionResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import {
    type BaseProvider,
    providers,
    type ProviderType,
} from '@globals/providers';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { ConnectionProps } from './types/connection.type';
import type { ConnectionState } from './types/connection-state.type';
import type { SingleConnection } from './types/single-connection.type';

class ConnectionsController {
    constructor() {
        makeAutoObservable(this);
    }

    #selectedConnectionId: number | null = null;

    allConfiguredConnectionsQuery = new ObservedQuery(
        v2ConnectionsControllerGetConnectionsOptions,
    );

    allConfiguredConnectionsByClientTypeQuery = new ObservedQuery(
        connectionsControllerGetConnectionsListByClientTypeOptions,
    );

    get isLoading(): boolean {
        return this.allConfiguredConnectionsQuery.isLoading;
    }

    get allConfiguredConnections(): ConnectionProps[] {
        if (!this.allConfiguredConnectionsQuery.data) {
            return [];
        }

        return Object.values(this.allConfiguredConnectionsQuery.data)
            .flat()
            .map(
                ({
                    id,
                    clientId,
                    clientType,
                    state,
                    clientAlias,
                    products,
                    writeAccessEnabled,
                    providerTypes,
                }) => {
                    const provider =
                        this.findProviderClientDetailsByClientTypeId(
                            clientType,
                        );

                    return {
                        id,
                        clientId,
                        clientType,
                        state: state as ConnectionState,
                        name:
                            provider?.name ??
                            `${clientType} **MISSING CONFIGURATION**`,
                        alias: clientAlias,
                        logo: provider?.logo,
                        workspaces: products.map((product) => ({
                            id: product.id,
                            name: product.name,
                        })),
                        providerTypes: providerTypes ?? [],
                        writeAccessEnabled,
                    };
                },
            );
    }

    get allAvailableConnections(): ConnectionProps[] {
        const configuredConnections = this.allConfiguredConnections.map(
            (connection) => connection.clientType,
        );

        // Get all providers that are not in configuredConnections
        return Object.values(providers)
            .filter(
                (provider) =>
                    !configuredConnections.includes(
                        /**
                         * Casting provider.id as ClientTypeEnum is not safe
                         * but it's the way the FE and BE have been dealing with
                         * the client type differences until now.
                         */
                        provider.id as ClientTypeEnum,
                    ),
            )
            .map((provider) => ({
                clientType: provider.id as ClientTypeEnum,
                name: provider.name,
                alias: '',
                logo: provider.logo,
                workspaces: [],
                providerTypes: provider.providerTypes.map((type) => ({
                    value: type,
                    isEnabled: false,
                })),
            }));
    }

    findProviderClientDetailsByClientTypeId = (
        clientType: string,
    ): BaseProvider | undefined => {
        return Object.values(providers).find(
            (provider) => provider.id === clientType,
        );
    };

    get singleConnection(): SingleConnection | null {
        if (
            !this.#selectedConnectionId ||
            !this.allConfiguredConnectionsQuery.data
        ) {
            return null;
        }

        const connection = Object.values(
            this.allConfiguredConnectionsQuery.data,
        )
            .flat()
            .find(
                ({ id: connectionId }) =>
                    connectionId === this.#selectedConnectionId,
            );

        if (!connection) {
            return null;
        }

        /**
         * Todo: We should find a better way to infer the type of the connection to get non-intersected properties.
         */
        const {
            id,
            clientType,
            state,
            clientAlias,
            products,
            accountId,
            user,
            clientId,
            providerTypes,
            groupIdList,
            inclusions,
            exclusions,
        } = connection as ConnectionResponseDto &
            InfrastructureConnectionResponseDto &
            TicketingConnectionResponseDto;
        const provider =
            this.findProviderClientDetailsByClientTypeId(clientType);

        return {
            id,
            clientType,
            state: state as ConnectionState,
            name: provider?.name ?? `${clientType} **MISSING CONFIGURATION**`,
            alias: clientAlias,
            logo: provider?.logo,
            workspaces: products.map((product) => ({
                id: product.id,
                name: product.name,
            })),
            providerTypes: providerTypes ?? [],
            accountId,
            user,
            clientId,
            groupIdList,
            inclusions,
            exclusions,
        };
    }

    get allConfiguredConnectionsByClientType(): ConnectionResponseDto[] {
        return this.allConfiguredConnectionsByClientTypeQuery.data?.data ?? [];
    }

    get ticketingConnectionWithWriteAccess() {
        return this.allConfiguredConnections.find(
            (connection) =>
                connection.providerTypes
                    .map((p) => p.value)
                    .includes('TICKETING') && connection.writeAccessEnabled,
        );
    }

    loadAllConnectedConnection = () => {
        this.allConfiguredConnectionsQuery.load();
    };

    get allTicketingConnectionWithWriteAccess(): ConnectionProps[] {
        return this.allConfiguredConnections.filter(
            (connection) =>
                connection.providerTypes
                    .map((p) => p.value)
                    .includes('TICKETING') && connection.writeAccessEnabled,
        );
    }

    get hasTicketingConnectionWithWriteAccessForWorkspace(): boolean {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        return this.allTicketingConnectionWithWriteAccess.some(
            (connection) =>
                isEmpty(connection.workspaces) ||
                connection.workspaces.some(
                    (workspace) =>
                        isNil(workspaceId) || workspace.id === workspaceId,
                ),
        );
    }

    get hasTicketingConnectionWithWriteAccess() {
        return !isNil(this.ticketingConnectionWithWriteAccess);
    }

    loadSingleConnection = (connectionId: number) => {
        this.#selectedConnectionId = connectionId;

        this.allConfiguredConnectionsQuery.load();
    };

    loadConfiguredConnectionsByProviderType = (providerType: ProviderType) => {
        return this.allConfiguredConnections
            .filter((connection) =>
                connection.providerTypes
                    .map((p) => p.value)
                    .includes(providerType),
            )
            .filter((connection) =>
                connection.providerTypes.some(
                    (provider) =>
                        provider.value === providerType && provider.isEnabled,
                ),
            );
    };

    loadConfiguredConnectionsByClientType = (clientType: ProviderType) => {
        return this.allConfiguredConnections.filter(
            (connection) => connection.clientType === clientType,
        );
    };

    benefits = new ObservedQuery(
        connectionsControllerGetControlTestInstancesByClientTypeOptions,
    );

    /**
     * What follows is a temporary solution
     * to control a toggle that controls part of the UI
     * for two unconnected components.
     */
    isWwsOrganizationalUnits = false;

    setIsWwsOrganizationalUnits = (value: boolean): void => {
        this.isWwsOrganizationalUnits = value;
    };

    #setupDetailsQuery = new ObservedQuery(
        v2ConnectionsControllerGetSetupDetailsOptions,
    );

    loadSetupDetails = (connectionId: number) => {
        this.#setupDetailsQuery.load({
            path: { id: connectionId },
        });
    };

    get setupDetails(): ConnectionSetupDetailsResponseDto | null {
        return this.#setupDetailsQuery.data;
    }

    #connectionSettingsQuery = new ObservedQuery(
        connectionsControllerGetConnectionSettingsOptions,
    );

    loadConnectionSettings = (connectionId: number) => {
        this.#connectionSettingsQuery.load({
            path: { id: connectionId },
        });
    };

    get connectionSettings(): ConnectionMetadataResponseDto | null {
        return this.#connectionSettingsQuery.data;
    }
}

export const sharedConnectionsController = new ConnectionsController();
