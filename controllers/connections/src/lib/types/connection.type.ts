import type { ClientTypeEnum } from '@globals/api-sdk/types';
import type { ProviderIsEnabledType } from '@globals/providers';
import type { ConnectionState } from './connection-state.type';
import type { ConnectionWorkspace } from './connection-workspace.type';

export interface ConnectionProps {
    id?: number;
    clientId?: string;
    clientType: ClientTypeEnum;
    name: string;
    alias?: string;
    workspaces: ConnectionWorkspace[];
    providerTypes: ProviderIsEnabledType[];
    logo?: string;
    state?: ConnectionState;
    writeAccessEnabled?: boolean;
}
